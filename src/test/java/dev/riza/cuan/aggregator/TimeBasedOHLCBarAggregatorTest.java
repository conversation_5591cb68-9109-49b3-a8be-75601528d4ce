package dev.riza.cuan.aggregator;

import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.OHLCCandle;
import dev.riza.cuan.util.TimeIntervalUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.messaging.support.MessageBuilder;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;

/**
 * Unit tests for TimeBasedOHLCBarAggregator to verify candle generation,
 * time alignment, and proper OHLC calculations.
 */
class TimeBasedOHLCBarAggregatorTest {

    @Mock
    private MarketDataGateway marketDataGateway;

    private TimeBasedOHLCBarAggregator aggregator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        aggregator = new TimeBasedOHLCBarAggregator(marketDataGateway, "1m", 100);
    }

    @Test
    void testTimeIntervalParsing() {
        assertEquals(Duration.ofMinutes(1), aggregator.getTimeframe());
        assertEquals("1m", aggregator.getTimeInterval());
        assertEquals(100, aggregator.getBufferSize());
    }

    @Test
    void testSingleTradeCreatesCandle() {
        // Create a trade event
        AggTradeEvent trade = createTrade("BTCUSDT", "50000.00", "1.0", "buy");
        
        // Process the trade
        aggregator.onMarketData(MessageBuilder.withPayload(trade).build());
        
        // Verify current candle is created
        OHLCCandle currentCandle = aggregator.getCurrentCandle();
        assertNotNull(currentCandle);
        assertEquals("BTCUSDT", currentCandle.getSymbol());
        assertEquals(new BigDecimal("50000.00"), currentCandle.getOpen());
        assertEquals(new BigDecimal("50000.00"), currentCandle.getHigh());
        assertEquals(new BigDecimal("50000.00"), currentCandle.getLow());
        assertEquals(new BigDecimal("50000.00"), currentCandle.getClose());
        assertEquals(new BigDecimal("1.0"), currentCandle.getTotalVolume());
        assertEquals(1, currentCandle.getTradeCount());
        assertFalse(currentCandle.isComplete());
    }

    @Test
    void testMultipleTradesUpdateCandle() {
        // Create multiple trades with different prices
        AggTradeEvent trade1 = createTrade("BTCUSDT", "50000.00", "1.0", "buy");
        AggTradeEvent trade2 = createTrade("BTCUSDT", "50100.00", "0.5", "sell");
        AggTradeEvent trade3 = createTrade("BTCUSDT", "49900.00", "2.0", "buy");
        
        // Process trades
        aggregator.onMarketData(MessageBuilder.withPayload(trade1).build());
        aggregator.onMarketData(MessageBuilder.withPayload(trade2).build());
        aggregator.onMarketData(MessageBuilder.withPayload(trade3).build());
        
        // Verify OHLC values
        OHLCCandle currentCandle = aggregator.getCurrentCandle();
        assertNotNull(currentCandle);
        assertEquals(new BigDecimal("50000.00"), currentCandle.getOpen()); // First trade price
        assertEquals(new BigDecimal("50100.00"), currentCandle.getHigh()); // Highest price
        assertEquals(new BigDecimal("49900.00"), currentCandle.getLow());  // Lowest price
        assertEquals(new BigDecimal("49900.00"), currentCandle.getClose()); // Last trade price
        assertEquals(new BigDecimal("3.5"), currentCandle.getTotalVolume()); // Sum of volumes
        assertEquals(3, currentCandle.getTradeCount());
    }

    @Test
    void testVolumeCalculation() {
        // Create trades with different sides
        AggTradeEvent buyTrade = createTrade("BTCUSDT", "50000.00", "1.0", "buy");
        AggTradeEvent sellTrade = createTrade("BTCUSDT", "50000.00", "0.5", "sell");
        
        // Process trades
        aggregator.onMarketData(MessageBuilder.withPayload(buyTrade).build());
        aggregator.onMarketData(MessageBuilder.withPayload(sellTrade).build());
        
        // Verify volume breakdown
        OHLCCandle currentCandle = aggregator.getCurrentCandle();
        assertNotNull(currentCandle);
        assertEquals(new BigDecimal("1.5"), currentCandle.getTotalVolume());
        assertEquals(new BigDecimal("1.0"), currentCandle.getBuyVolume());
        assertEquals(new BigDecimal("0.5"), currentCandle.getSellVolume());
    }

    @Test
    void testTimeAlignment() {
        // Create a trade at a specific time
        Instant tradeTime = Instant.parse("2024-01-01T12:34:45.123Z");
        AggTradeEvent trade = createTradeWithTime("BTCUSDT", "50000.00", "1.0", "buy", tradeTime);
        
        // Process the trade
        aggregator.onMarketData(MessageBuilder.withPayload(trade).build());
        
        // Verify time alignment (should align to minute boundary)
        OHLCCandle currentCandle = aggregator.getCurrentCandle();
        assertNotNull(currentCandle);
        
        Instant expectedStart = TimeIntervalUtils.alignToInterval(tradeTime, Duration.ofMinutes(1));
        assertEquals(expectedStart, currentCandle.getOpenTime());
        assertEquals(expectedStart.plus(Duration.ofMinutes(1)), currentCandle.getCloseTime());
    }

    @Test
    void testCandleCompletion() {
        // Create trades in different time periods
        Instant time1 = Instant.parse("2024-01-01T12:34:00.000Z");
        Instant time2 = Instant.parse("2024-01-01T12:35:00.000Z"); // Next minute
        
        AggTradeEvent trade1 = createTradeWithTime("BTCUSDT", "50000.00", "1.0", "buy", time1);
        AggTradeEvent trade2 = createTradeWithTime("BTCUSDT", "50100.00", "0.5", "sell", time2);
        
        // Set up stream verification
        StepVerifier.create(aggregator.getStream().take(2))
                .then(() -> {
                    aggregator.onMarketData(MessageBuilder.withPayload(trade1).build());
                    aggregator.onMarketData(MessageBuilder.withPayload(trade2).build());
                })
                .expectNextMatches(candle -> !candle.isComplete()) // First incomplete candle
                .expectNextMatches(candle -> candle.isComplete())   // Completed candle when new period starts
                .verifyComplete();
        
        // Verify completed candles are stored
        List<OHLCCandle> completedCandles = aggregator.getCompletedCandles();
        assertEquals(1, completedCandles.size());
        assertTrue(completedCandles.get(0).isComplete());
        
        // Verify gateway was called to publish completed candle
        verify(marketDataGateway, times(1)).publish(any(OHLCCandle.class));
    }

    @Test
    void testForceCompleteCandle() {
        // Create a trade
        AggTradeEvent trade = createTrade("BTCUSDT", "50000.00", "1.0", "buy");
        aggregator.onMarketData(MessageBuilder.withPayload(trade).build());
        
        // Verify current candle exists and is incomplete
        OHLCCandle currentCandle = aggregator.getCurrentCandle();
        assertNotNull(currentCandle);
        assertFalse(currentCandle.isComplete());
        
        // Force complete the candle
        OHLCCandle completedCandle = aggregator.forceCompleteCurrentCandle();
        assertNotNull(completedCandle);
        assertTrue(completedCandle.isComplete());
        
        // Verify completed candles list
        List<OHLCCandle> completedCandles = aggregator.getCompletedCandles();
        assertEquals(1, completedCandles.size());
        
        // Verify gateway was called
        verify(marketDataGateway, times(1)).publish(any(OHLCCandle.class));
    }

    @Test
    void testGetRecentCandles() {
        // Force complete multiple candles
        for (int i = 0; i < 5; i++) {
            AggTradeEvent trade = createTrade("BTCUSDT", "50000.00", "1.0", "buy");
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build());
            aggregator.forceCompleteCurrentCandle();
        }
        
        // Test getting recent candles
        List<OHLCCandle> recent3 = aggregator.getRecentCandles(3);
        assertEquals(3, recent3.size());
        
        List<OHLCCandle> recentAll = aggregator.getRecentCandles(10);
        assertEquals(5, recentAll.size()); // Should return all 5
    }

    @Test
    void testStatistics() {
        // Create some candles
        AggTradeEvent trade = createTrade("BTCUSDT", "50000.00", "1.0", "buy");
        aggregator.onMarketData(MessageBuilder.withPayload(trade).build());
        
        String stats = aggregator.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("TimeBasedOHLCBarAggregator Stats"));
        assertTrue(stats.contains("interval=1m"));
    }

    @Test
    void testConfigInfo() {
        String config = aggregator.getConfigInfo();
        assertNotNull(config);
        assertTrue(config.contains("TimeBasedOHLCBarAggregator"));
        assertTrue(config.contains("interval=1m"));
        assertTrue(config.contains("bufferSize=100"));
    }

    // Helper methods
    private AggTradeEvent createTrade(String symbol, String price, String quantity, String side) {
        return createTradeWithTime(symbol, price, quantity, side, Instant.now());
    }

    private AggTradeEvent createTradeWithTime(String symbol, String price, String quantity, String side, Instant timestamp) {
        AggTradeEvent trade = new AggTradeEvent();
        trade.setSymbol(symbol);
        trade.setPrice(new BigDecimal(price));
        trade.setQuantity(new BigDecimal(quantity));
        trade.setSide(side);
        trade.setTimestamp(timestamp);
        return trade;
    }
}
