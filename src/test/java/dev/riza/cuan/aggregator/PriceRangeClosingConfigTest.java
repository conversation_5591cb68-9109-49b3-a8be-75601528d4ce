package dev.riza.cuan.aggregator;

import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.MarketConditionEvent;
import org.junit.jupiter.api.Test;
import org.springframework.messaging.support.MessageBuilder;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the configurable price range-based closing functionality
 */
class PriceRangeClosingConfigTest {

    @Test
    void testPriceRangeClosingDisabled() {
        // Create aggregator with price range closing disabled
        AdaptiveBarAggregator aggregator = new AdaptiveBarAggregator(
            new BigDecimal("100"), // baseVolumeThreshold
            50, // baseTradeCountThreshold
            new BigDecimal("0.001"), // baseVolatilityMultiplier
            false, // priceRangeClosingEnabled = FALSE
            new BigDecimal("2.0"), // volatilityMultiplier
            new BigDecimal("0.01"), // minimumRangePercent
            5, // minimumTradeCount
            new BigDecimal("10.0") // minimumAbsoluteRange
        );

        MarketConditionEvent condition = createMarketCondition(new BigDecimal("100"));
        
        // Create trades with significant price movement
        List<AggTradeEvent> trades = createTradesWithPriceRange(
            new BigDecimal("50000"), new BigDecimal("50500")); // $500 range

        List<String> closingReasons = new ArrayList<>();
        aggregator.getStream().subscribe(bar -> closingReasons.add(bar.getClosingReason()));

        // Process trades
        for (AggTradeEvent trade : trades) {
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build(), condition);
        }

        // Should not close due to price range since it's disabled
        // (Would need to trigger volume or trade count threshold to close)
        assertTrue(closingReasons.isEmpty() || 
                   closingReasons.stream().noneMatch(reason -> reason.contains("PRICE_RANGE")));
    }

    @Test
    void testMinimumTradeCountRequirement() {
        // Create aggregator requiring 10 trades minimum
        AdaptiveBarAggregator aggregator = new AdaptiveBarAggregator(
            new BigDecimal("1000"), // High volume threshold
            100, // High trade count threshold
            new BigDecimal("0.001"),
            true, // priceRangeClosingEnabled
            new BigDecimal("2.0"), // Low volatility multiplier
            new BigDecimal("0.01"), // Low percentage requirement
            10, // minimumTradeCount = 10
            new BigDecimal("10.0") // Low absolute range
        );

        MarketConditionEvent condition = createMarketCondition(new BigDecimal("100"));
        
        List<String> closingReasons = new ArrayList<>();
        aggregator.getStream().subscribe(bar -> closingReasons.add(bar.getClosingReason()));

        // Send only 5 trades (less than minimum 10)
        for (int i = 0; i < 5; i++) {
            AggTradeEvent trade = createTrade("BTCUSDT", 
                String.valueOf(50000 + i * 100), "1.0", "buy"); // Large price movements
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build(), condition);
        }

        // Should not close due to insufficient trade count
        assertTrue(closingReasons.isEmpty());

        // Send 5 more trades (total 10, meets minimum)
        for (int i = 5; i < 10; i++) {
            AggTradeEvent trade = createTrade("BTCUSDT", 
                String.valueOf(50000 + i * 100), "1.0", "buy");
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build(), condition);
        }

        // Now should close due to price range
        assertFalse(closingReasons.isEmpty());
        assertTrue(closingReasons.get(0).contains("PRICE_RANGE_SIGNIFICANT"));
    }

    @Test
    void testMinimumAbsoluteRangeRequirement() {
        AdaptiveBarAggregator aggregator = new AdaptiveBarAggregator(
            new BigDecimal("1000"), // High volume threshold
            100, // High trade count threshold
            new BigDecimal("0.001"),
            true, // priceRangeClosingEnabled
            new BigDecimal("1.0"), // Very low volatility multiplier
            new BigDecimal("0.001"), // Very low percentage requirement
            5, // Low trade count requirement
            new BigDecimal("100.0") // minimumAbsoluteRange = $100
        );

        MarketConditionEvent condition = createMarketCondition(new BigDecimal("200")); // High volatility
        
        List<String> closingReasons = new ArrayList<>();
        aggregator.getStream().subscribe(bar -> closingReasons.add(bar.getClosingReason()));

        // Create trades with small absolute range ($50) but high percentage
        List<AggTradeEvent> trades = createTradesWithPriceRange(
            new BigDecimal("1000"), new BigDecimal("1050")); // Only $50 range

        for (AggTradeEvent trade : trades) {
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build(), condition);
        }

        // Should not close because absolute range ($50) < minimum ($100)
        assertTrue(closingReasons.isEmpty());
    }

    @Test
    void testMinimumPercentageRangeRequirement() {
        AdaptiveBarAggregator aggregator = new AdaptiveBarAggregator(
            new BigDecimal("1000"), // High volume threshold
            100, // High trade count threshold
            new BigDecimal("0.001"),
            true, // priceRangeClosingEnabled
            new BigDecimal("1.0"), // Very low volatility multiplier
            new BigDecimal("1.0"), // minimumRangePercent = 1.0%
            5, // Low trade count requirement
            new BigDecimal("10.0") // Low absolute range requirement
        );

        MarketConditionEvent condition = createMarketCondition(new BigDecimal("200"));
        
        List<String> closingReasons = new ArrayList<>();
        aggregator.getStream().subscribe(bar -> closingReasons.add(bar.getClosingReason()));

        // Create trades with large absolute range but small percentage
        List<AggTradeEvent> trades = createTradesWithPriceRange(
            new BigDecimal("50000"), new BigDecimal("50100")); // $100 range = 0.2%

        for (AggTradeEvent trade : trades) {
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build(), condition);
        }

        // Should not close because percentage (0.2%) < minimum (1.0%)
        assertTrue(closingReasons.isEmpty());
    }

    @Test
    void testVolatilityMultiplierConfiguration() {
        // Test with high volatility multiplier (should be harder to trigger)
        AdaptiveBarAggregator aggregator = new AdaptiveBarAggregator(
            new BigDecimal("1000"), // High volume threshold
            100, // High trade count threshold
            new BigDecimal("0.001"),
            true, // priceRangeClosingEnabled
            new BigDecimal("10.0"), // High volatility multiplier
            new BigDecimal("0.01"), // Low percentage requirement
            5, // Low trade count requirement
            new BigDecimal("10.0") // Low absolute range requirement
        );

        MarketConditionEvent condition = createMarketCondition(new BigDecimal("50")); // Market volatility
        
        List<String> closingReasons = new ArrayList<>();
        aggregator.getStream().subscribe(bar -> closingReasons.add(bar.getClosingReason()));

        // Create trades with range = $400, but threshold = 50 * 10 = $500
        List<AggTradeEvent> trades = createTradesWithPriceRange(
            new BigDecimal("50000"), new BigDecimal("50400")); // $400 range

        for (AggTradeEvent trade : trades) {
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build(), condition);
        }

        // Should not close because range ($400) < threshold ($500)
        assertTrue(closingReasons.isEmpty());
    }

    @Test
    void testSuccessfulPriceRangeClosing() {
        AdaptiveBarAggregator aggregator = new AdaptiveBarAggregator(
            new BigDecimal("1000"), // High volume threshold
            100, // High trade count threshold
            new BigDecimal("0.001"),
            true, // priceRangeClosingEnabled
            new BigDecimal("2.0"), // volatilityMultiplier
            new BigDecimal("0.1"), // minimumRangePercent = 0.1%
            5, // minimumTradeCount
            new BigDecimal("50.0") // minimumAbsoluteRange
        );

        MarketConditionEvent condition = createMarketCondition(new BigDecimal("100")); // Market volatility
        
        List<String> closingReasons = new ArrayList<>();
        aggregator.getStream().subscribe(bar -> closingReasons.add(bar.getClosingReason()));

        // Create trades that meet all criteria:
        // - Range: $300 > $50 (absolute minimum)
        // - Percentage: 0.6% > 0.1% (percentage minimum)  
        // - Volatility: $300 > $200 (2 * $100 volatility)
        // - Trade count: Will be >= 5
        List<AggTradeEvent> trades = createTradesWithPriceRange(
            new BigDecimal("50000"), new BigDecimal("50300")); // $300 range = 0.6%

        for (AggTradeEvent trade : trades) {
            aggregator.onMarketData(MessageBuilder.withPayload(trade).build(), condition);
        }

        // Should close successfully
        assertFalse(closingReasons.isEmpty());
        String reason = closingReasons.get(0);
        assertTrue(reason.contains("PRICE_RANGE_SIGNIFICANT"));
        assertTrue(reason.contains("range=300.00"));
        assertTrue(reason.contains("pct=0.600%"));
        assertTrue(reason.contains("multiplier=2.0"));
    }

    // Helper methods
    private MarketConditionEvent createMarketCondition(BigDecimal volatility) {
        return MarketConditionEvent.builder()
            .timestamp(Instant.now())
            .rollingVolatility(volatility)
            .tradesPerSecond(5.0)
            .volumeRate(new BigDecimal("50"))
            .build();
    }

    private List<AggTradeEvent> createTradesWithPriceRange(BigDecimal lowPrice, BigDecimal highPrice) {
        List<AggTradeEvent> trades = new ArrayList<>();
        Instant baseTime = Instant.now();
        
        // Create 10 trades spanning the price range
        for (int i = 0; i < 10; i++) {
            BigDecimal price = lowPrice.add(
                highPrice.subtract(lowPrice).multiply(new BigDecimal(i)).divide(new BigDecimal(9))
            );
            AggTradeEvent trade = createTrade("BTCUSDT", price.toString(), "1.0", i % 2 == 0 ? "buy" : "sell");
            trade.setTimestamp(baseTime.plusMillis(i * 100));
            trades.add(trade);
        }
        
        return trades;
    }

    private AggTradeEvent createTrade(String symbol, String price, String quantity, String side) {
        AggTradeEvent trade = new AggTradeEvent();
        trade.setSymbol(symbol);
        trade.setPrice(new BigDecimal(price));
        trade.setQuantity(new BigDecimal(quantity));
        trade.setSide(side);
        trade.setTimestamp(Instant.now());
        return trade;
    }
}
