spring.application.name=cuan01

# Server Configuration
server.port=8080

# Database Configuration (TimescaleDB/PostgreSQL)
#spring.datasource.url=*******************************************
#spring.datasource.username=postgres
#spring.datasource.password=postgres
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
#spring.jpa.hibernate.ddl-auto=update
#spring.jpa.show-sql=false


