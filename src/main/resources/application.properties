spring.application.name=cuan01

# Server Configuration
server.port=8080

# Logging Configuration
logging.level.dev.riza.cuan=INFO

# Database Configuration (TimescaleDB/PostgreSQL)
#spring.datasource.url=*******************************************
#spring.datasource.username=postgres
#spring.datasource.password=postgres
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
#spring.jpa.hibernate.ddl-auto=update
#spring.jpa.show-sql=false

# Time-Based OHLC Bar Aggregator Configuration
aggregator.ohlc.timeInterval=1m
aggregator.ohlc.bufferSize=1000

# Adaptive Volume Bar Aggregator Configuration
aggregator.adaptive.baseVolumeThreshold=10.0
aggregator.adaptive.volatilityWindowSize=50
aggregator.adaptive.tpsWindowDurationSeconds=10
aggregator.adaptive.volumeRateWindowSize=30
aggregator.adaptive.metricsUpdateFrequency=5
aggregator.adaptive.volatilitySensitivity=1.0
aggregator.adaptive.tpsSensitivity=1.0
aggregator.adaptive.volumeRateSensitivity=0.5
aggregator.adaptive.minVolumeThreshold=1.0
aggregator.adaptive.maxVolumeThreshold=100.0


