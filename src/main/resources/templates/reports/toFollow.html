<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
	<head>
		<meta charset="utf-8" />
		<title>To Follow</title>
		<link th:href="${baseUrl + '/css/reports/request/fonts.css'}" href="../../static/css/reports/fonts.css" rel="stylesheet" />

		<style>
			@page {
				margin: 0 !important;
				size: 2480px 3508px;
			}

			html {
				background-color: #FFFFFF;
				padding: 0;
				margin: 0;
				font-family: '<PERSON><PERSON>';
			}

			body {
				width: 2480px;
				height: 3508px;
				overflow: visible !important;
				padding: 0;
				margin: 0;
				position: absolute;
				top: 0;
				left: 0;
				font-family: '<PERSON><PERSON>';
			}

			a {
				text-decoration: none;
				color: #000000;
			}

			* {
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
				font-family: 'Roboto';
				box-sizing: border-box;
				margin: 0;
			}

			body {
				padding: 0;
				margin: 0;
			}

			.PDF {
				width: 2480px;
				height: 3508px;
				overflow: hidden;
				background: #FFFFFF;
			}
			
			div.logo {
				position: absolute;
				top: 83px;
				left: 121px;
				width: 366px;
				height: 124px;
			}

			div.logo img {
				width: 100%;
				height: 100%;
				object-fit: fill;
			}

			p.healius {
				position: absolute;
				top: 260px;
				left: 123px;
				width: 1326px;
				font-size: 29px;
				font-weight: 400;
			}

			h1.title {
				position: absolute;
				top: 300px;
				left: 119px;
				width: 831px;
				font-size: 64px;
				font-weight: 500;
			}

			div.labs {
				float: left;
				width: 414px;
			}

			div.lab-sticker {
				position: absolute;
				top: 79px;
				left: 1530px;
				width: 530px;
				height: 317px;
				border: 4px solid #A6ADB3;
			}

			div.lab-sticker p {
				color: #A6ADB3;
				text-align: center;
				font-weight: bold;
				font-size: 42px;
				line-height: 71px;
				position: relative;
				top: 60px;
				text-transform: uppercase;
				margin: 0;
				padding: 0;
			}

			div.qr-code {
				position: absolute;
				top: 81px;
				left: 2090px;
				width: 269px;
				height: 313px;
				border: 3px solid black;
			}

			div.qr-code img {
				position: absolute;
				top: 32px;
				left: 32px;
				width: 201px;
				height: 201px;
			}

			div.qr-code span {
				position: absolute;
				top: 255px;
				left: 0px;
				width: 258px;
				text-align: center;
				display: block;
				font-size: 38px;
				font-weight: 400;
				line-height: 29px;
			}

			div.background {
				position: absolute;
				top: 441px;
				left: 89px;
				width: 2302px;
				height: 3034px;
				background-color: #F8F9FA;
				border-radius: 40px;
			}

			h2.title {
				position: absolute;
				top: 32px;
				left: 36px;
				width: 2166px;
				text-align: left;
				display: block;
				font-size: 48px;
				font-style: normal;
				font-weight: 500;
				line-height: 34px; /* 141.667% */
				letter-spacing: -0.24px;
				font-stretch: 95%;
			}

			p.outstanding {
				position: absolute;
				top: 120px;
				left: 36px;
				width: 2166px;
				text-align: left;
				display: block;
				font-size: 29px;
				font-weight: 400;
				letter-spacing: -0.5px !important;
			}

			div.section-1 {
				position: absolute;
				top: 194px;
				left: 32px;
				width: 2242px;
				height: 260px;
			}
			.block {
				display: block;
			}

			div.section-2 {
				position: absolute;
				top: 485px;
				left: 32px;
				width: 2242px;
				height: 2231px;
			}

			div.section-2 h2 {
				margin: 20px 40px 20px 40px;
				font-size: 52px;
				font-weight: 400;
			}

			div.section-3 {
				position: absolute;
				top: 2746px;
				left: 32px;
				width: 2242px;
				height: 256px;
			}

			div.bordered {
				border-radius: 19px;
				background: white;
				overflow: hidden;
			}

			div.bordered.bordered-thin {
				border: 3px solid black;
			}

			div.bordered.bordered-thick {
				border: 5px solid black;
			}

			div.bordered p {
				line-height: 72px;
				font-size: 37px;
				margin: 24px 35px 24px 35px;
				padding: 0;
			}

			div.background hr {
				margin: 80px 0 60px 0;
				width: 200%;
				height: 1px;
				border: 0;
				border-bottom: 3px solid black;
			}
			
			div.section-2 h2 {
				font-size: 50px;
				font-weight: normal;
				margin: 40px 35px 24px 35px;
			}

			div.section-3 h3 {
				font-size: 40px;
				font-weight: normal;
				position: absolute;
				top: 36px;
				left: 42px;
			}

			div.section-3 div {
				width: 260px;
				height: 92px;
				position: absolute;
				top: 120px;
				border-right: 3px solid black;
				font-weight: bold;
			}

			div.section-3 div p {
				margin: 0;
			}

			div.section-3 div span {
				font-size: 44px;
				font-weight: bold;
				position: absolute;
				top: 30px;
			}

			div.section-3 div span.title {
				font-size: 24px;
				font-weight: bold;
				text-transform: uppercase;			
				top: -10px;
			}

			/*xoffset: 175 */
			div.section-3 div.first {
				left: 38px;
			}

			div.section-3 div.second {
				left: 343px;
				width: 347px;
			}

			div.section-3 div.third {
				left: 740px;
				width: 568px;
			}

			div.section-3 div.last {
				left: 1360px;
				width: 820px;
				border: 0;
			}
			
			div.section-3 div p.title {
				position: absolute;
				top: -10px;
				font-size: 25px;
				text-transform: uppercase;
			}

			div.section-3 div p.non-title {
				position: absolute;
				top: 36px;
				font-size: 41px;
				font-weight: 400;
			}

			p.rev {
				position: absolute;
				top: 3040px;
				left: 1194px;
				width: 1214px;
				height: 33px;
				text-align: right;
				padding-right: 32px;
				font-size: 20px;
			}
			
		</style>


	</head>
	<body class="PDF" >
		<div class="header">
			<div class="logo">
				<img th:src="${buImage64}" src="../../static/images/logos/abbot_rgb.png" alt="" />
			</div>

			<p class="healius">Healius Pathology Pty Ltd (ABN **************) trading as <span th:text="${companyBU}">QML Pathology</span>&nbsp;&nbsp;&nbsp;. APA No.000042</p>
			<h1 class="title">Deferred test/s</h1>

			<div class="labs">
				<div class="lab-sticker"><p>Place new<br />lab number<br />here</p></div>
				<div id="qrcode" class="qr-code">
					<img th:src="|data:image/png;base64,${qrCode}|" src="../../static/images/reports/qrcode-sample.png" alt="" />
					<span class="code" th:text="${healiusId}">33ER66HA</span>
				</div>
			</div>
		</div>

		<div class="background">
			<h2 class="title">Please bring this form to one of our collection centres</h2>
			<p class="outstanding">You have an outstanding test/s. Please return to complete the process or drop off your self collect sample. To find a collection centre visit <a th:target="_new" th:href="|https://${companyWebsite}|" target="_blank" href="https://qml.com.au" th:text="${companyWebsite}">qml.com.au</a>.</p>

			<div class="section-1 bordered bordered-thin">
				<p>
					<th:block th:if="${!patientIsSingleName}">
						<span class="block" th:text="|Last name: ${patientFamilyNameValue}|">Last name: Smith</span>
						<span class="block" th:text="|First name: ${patientGivenNameValue}|">First name: Jane</span>
					</th:block>

					<th:block th:if="${patientIsSingleName}">
						<span class="block" th:text="|Single Name: ${patientNameValue}|">Single Name: Jane</span>
					</th:block>

					<span class="block" th:text="|Date of birth: ${patientDOBValue}|">Date of birth: 11/11/1990 (31Y)</span>
				</p>
			</div>

			<div class="section-2 bordered bordered-thick">
				<h2>Deferred test/s</h2>
				<p th:utext="|${toFollowValue}|">Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br />Magnesium, serum TNF Ipsum dolor<br /></p>

				<hr class="divider-1" />

				<h2>Referrer</h2>
				<p>
					<span th:utext="${referrerValue}">Finoa Wilson (1111222Y)<br />Toongabbie Medical Practice<br />55 Aurelia St. Toongabbie NSW 2146</span>
				</p>

				<hr class="divider-2" />

				<h2>Copy to</h2>
				<p>
					<span th:utext="${copyToValue}">Steven Kam (1111333Y)<br />Westmead Hospital<br />Cnr Hawkesbury Road and, Darcy Rd, Westmead NSW 2145</span>
				</p>
			</div>

			<div class="section-3 bordered bordered-thin">
				<h3>Original collection</h3>
				<div class="first">
					<p class="title">Original Lab No</p>
					<p class="non-title" th:text="${originalLabNumberValue}">33ER66HA</p>
				</div>

				<div class="second">
					<p class="title">Original request date</p>
					<p class="non-title" th:text="${requestDateValue}">01 Dec 2022</p>
				</div>

				<div class="third">
					<p class="title">Original Collection date &amp; Time</p>
					<p class="non-title" th:text="${collectionDateAndTimeValue}">21 Mar 2024 06:26 pm AEST</p>
				</div>

				<div class="last">
					<p class="title">Original Collection ACC</p>
					<p class="non-title" th:text="${cc}">NEWW Newcastle West</p>
				</div>
			</div>
		</div>
	</body>
</html>
