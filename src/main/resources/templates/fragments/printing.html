<!DOCTYPE html>

<html lang="en" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="printLabelModal" th:replace="~{fragments/modal::mediumModal('printLabelModal', 'Print labels', 'Print', ~{::printLabelForm})}"></div>
<div th:fragment="scanLabelModal" th:replace="~{fragments/modal::smallModal('scanLabelModal', 'Scan pre-printed lab number', 'Save', ~{::scanLabelForm})}"></div>
<div th:fragment="printReferralModal" th:replace="~{fragments/modal::smallModal('printReferralModal', '', 'Ok', ~{::printReferralForm})}"></div>

<form th:fragment="printLabelForm" id="printLabelForm" method="post">
    <div class="row pb-2 px-2">
        <div class="d-flex warning bg-warning-fill border-warning-fill-strong border-1 rounded-1" style="border-style: solid;">
            <div class="mb-0 ms-1 me-3 mt-3">
                <img src="/images/icon/warning.svg" alt="">
            </div>
            <div class="d-flex flex-column">
                <p class="body1 fw-500 text-warning-fill-strong mb-0">Person must match</p>
                <p class="mt-0">You must be the person taking blood and labelling the samples.</p>
            </div>
        </div>
    </div>
    <div class="row py-2 mb-3">
        <div class="col-12">
            <div class="form-col-12">
                <label class="pb-2" for="labelPrintUsername">Collector ID</label>
                <input class="form-control" autocomplete="off" placeholder="" id="labelPrintUsername" name="username" type="text" />
                <div class="x-clearer scan-userid">
                    <img class="" id="usernameStatusIcon" src="/images/icon/labelsDark.svg" width="24" alt=""/>
                    <img class="d-none" src="/images/icon/blueTick.svg" width="24" alt=""/>
                </div>
            </div>
            <!---<div class="form-col-12">
                <label class="pb-2" for="labelPrintUsername">Collector username</label>
                <input class="form-control" autocomplete="off" placeholder="firstname.lastname" id="labelPrintUsername" name="username" type="text"/>
            </div>--->
            <div class="pt-2" id="labelPrintSwitchUser">
                <div class="body2 text-primary-text cursor-pointer fw-bold text-decoration-underline">
                    Forgot or lost your pass?
                </div>
            </div>
        </div>
    </div>
    <div id="chooseDateTime" class="form-group row mx-0 p-2 pb-2 mb-0 bg-primary-fill-weak border-primary-fill-weak border-1 rounded-1" style="border-style: solid;">
        <div class="col-6">
            <label class="pb-2" for="labelPrintDate">Collection date</label>
            <input autocomplete="off" autofocus class="form-control bg-white" id="labelPrintDate" name="chooseDate" placeholder="dd-MM-yyyy" type="date" />
        </div>

        <div class="col-6">
            <label class="pb-2" for="labelPrintTime">Collection time</label>
            <input autocomplete="off" autofocus class="form-control bg-white" id="labelPrintTime" name="chooseTime" step="60" placeholder="HH:MM" type="time" />
        </div>
    </div>

    <div id="chooseDateTimeError" class="d-none row m-0 p-0 mt-1">
        <div class="col-12 p-0 m-0">
            <div class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
                <div data-field="chooseDateTime" data-validator="custom">Collection date and time must be in the past</div>
            </div>
        </div>
    </div>

    <div class="row py-2 mt-3">
        <div class="col-6">
            <div class="col-12">
                <label class="pb-2" for="labelPrintPrinters">
                    Label printer
                </label>
                <select class="form-select " id="labelPrintPrinters" name="printers"></select>
            </div>
        </div>

        <div class="col-6">
            <div class="col-12 pb-4">
                <label class="pb-2" for="labelPrintDpi">
                    Printer configuration
                </label>
                <select class="form-select " id="labelPrintDpi" name="dpi">
                    <option value="203">50 x 25 mm | 203 dpi printer</option>
                    <option value="300">50 x 25 mm | 300 dpi printer</option>
                </select>
            </div>

        </div>
    </div>

    <div class="row py-0">
        <div class="col-12">
            <h6 class="text-primary-text fw-bold pb-0 mb-0">Label type</h6>
        </div>

    </div>

    <div class="row pb-2">
        <div class="col-6">
            <div class="form-col-12">
                <label class="pb-2" for="labelPrintAmount">
                    Request form labels

                    <button type="button" class="btn btn-secondary px-1" data-toggle="tooltip" data-bs-html="true" data-bs-template="<div class='tooltip labels-tooltip opacity-100' role='tooltip'><div class='tooltip-arrow'></div><div class='tooltip-inner'></div></div>" data-html="true" title="<span class='d-block description'>Example label for<br />request form only</span><img class='pt-1 pb-2 w-100' src='/images/labels/requestLabelSample.jpg' alt='' />">
                        <img src="/images/icon/info-outline-neutral.svg" alt="info" />
                    </button>
                </label>
                <input class="form-control " autocomplete="off" id="labelPrintAmount" max="30" step="1"
                       name="printAmount" type="number" />
                <input id="barCodeVisitId" name="barCodeVisitId" type="hidden">
            </div>
        </div>
        <div class="col-6">
            <div class="form-col-12">
                <label class="pb-2" for="labelSpecimenPrintAmount">
                    Specimen labels

                    <button type="button" class="btn btn-secondary px-2" data-toggle="tooltip" data-bs-html="true" data-bs-template="<div class='tooltip labels-tooltip opacity-100' role='tooltip'><div class='tooltip-arrow'></div><div class='tooltip-inner'></div></div>" data-html="true" title="<span class='d-block description'>Example label for containers<br />and additional paperwork</span><img class='pt-1 pb-2 w-100' src='/images/labels/specimenLabelSample.jpg' alt='' />">
                        <img src="/images/icon/info-outline-neutral.svg" alt="info" />
                    </button>
                </label>
                <input class="form-control " autocomplete="off" id="labelSpecimenPrintAmount" max="30" step="1"
                       name="labelSpecimenPrintAmount" type="number">
            </div>
        </div>
    </div>

    <div class="row py-2 mb-3">
        <div class="col-12">
            <div class="form-check">
                <input class="form-check-input" id="labelsAcknowledgement" name="labelsAcknowledgement" type="checkbox" value="true" />
                <label class="form-check-label body2" for="labelsAcknowledgement">
                    I certify that the blood specimen accompanying this request was drawn from the stated patient as established by direct enquiry of the patient and/or inspection of the ID wrist band, and that the specimen was labelled immediately in the presence of the patient. I have also signed the sample tube. My electronic signature will appear on the sample labels.
                </label>
            </div>
        </div>
    </div>


        <!---<div class="collection-time-checkbox col-12 pb-0 pt-4">
            <input class="form-check-input" id="labelPrintUseCurrentTime"
                   name="labelPrintUseCurrentTime" checked
                   type="checkbox" value="true">
                <label class="form-check-label body2 ps-2" for="labelPrintUseCurrentTime">
                    Include current time
                </label>
        </div>

        <div id="labelPrintInputCurrentTime" class="collection-time col-12 pb-0 pt-4 d-none">
            <label class=" pb-2" for="labelPrintTime">
                Collection time
            </label>

            <input autocomplete="off" autofocus class="form-control bg-white"
                   id="labelPrintTime" name="chooseTime" placeholder="HH:MM" />
        </div>--->
</form>

<div th:fragment="scanLabelForm" id="scanLabelForm">
    <div class="form-group row mb-3">
        <label for="preprintedLabNumber" class="form-label body2 text-neutral-text">Pre-printed lab number</label>
        <div class="position-relative">
            <input class="form-control" id="preprintedLabNumber" name="preprintedLabNumber" placeholder="">
            <img th:src="@{/images/icon/labelsDark.svg}" class="position-absolute top-50 end-0 translate-middle pe-2" alt=""/>
        </div>
    </div>

    <div class="d-flex bg-info-fill p-3 align-content-start rounded-1">
        <img th:src="@{/images/icon/info-strong.svg}" class="align-self-start pe-2" alt=""/>
        <div>
            <div class="d-flex body1 text-info-text-dark fw-500 pb-2">Scan lab number only</div>
            <div class="description">
                NEVER type the barcode number as the risk of error could mean the specimen labels will not match the request form.
            </div>
        </div>
    </div>
</div>

<div th:fragment="printReferralForm" id="printReferralForm">
    <div class="text-center">
        <img alt="Error Icon" class="pb-4" src=""
             th:src="|https://web-assets.${@environment.getProperty('healius.digital.envName')}.healius.com.au/images/icon/info-outline.svg|"/>
    </div>
    <div class="text-center">
        <h5 class="text-neutral-text mb-2 fw-bolder">Print eReferral</h5>
    </div>
    <div class="text-center">
        <div class="body2 pb-4">
            Any changes you've made to this eReferral will NOT be reflected in the print out.
        </div>
    </div>
</div>
<div th:fragment="printReferralToFollowForm" id="printReferralToFollowForm">
    <div class="text-center">
        <img alt="Error Icon" class="pb-4" src=""
             th:src="|https://web-assets.${@environment.getProperty('healius.digital.envName')}.healius.com.au/images/icon/info-outline.svg|"/>
    </div>
    <div class="text-center">
        <h5 class="text-neutral-text mb-2 fw-bolder">Print eReferral</h5>
    </div>
    <div class="text-center">
        <div class="body2 pb-4">
            Any changes you've made to this eReferral will NOT be reflected in the print out.
        </div>
    </div>
</div>


<th:block th:fragment="printReferralToFollowModel">
    <div aria-hidden="true" aria-labelledby="modal-title" data-bs-backdrop="static" class="modal fade"
         id="printReferralToFollowModel" role="dialog" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-md d-flex justify-content-center">
            <div class="modal-content p-5 shadow-lg rounded-3 mx-auto">
                <div class="modal-body pt-5">
                    <button type="button" class="btn-close end-0 top-0 position-absolute"
                            data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    <div class="text-center mb-4">
                        <img alt="Printer Icon" src=""
                             th:src="|https://web-assets.${@environment.getProperty('healius.digital.envName')}.healius.com.au/images/icon/printerBlue.svg|"/>
                    </div>
                    <div class="text-center h5 text-neutral-text fw-500 mb-4">
                        What would you like to print?
                    </div>
                    <div
                        class="row gx-3 d-flex justify-content-center align-items-center text-center">
                        <div class="col-6 d-flex justify-content-center">
                            <div
                                class="border border-1 border-light rounded-2 p-4 d-flex flex-column align-items-center gap-2 w-100"
                                id="printOriginalEReferral">
                                <img alt="eReferral Icon" class="pb-1" src=""
                                     th:src="|https://web-assets.${@environment.getProperty('healius.digital.envName')}.healius.com.au/images/icon/eReferral.svg|"/>
                                <div class="caption fw-600 text-neutral-text pt-2">
                                    Original eReferral
                                </div>
                            </div>
                        </div>
                        <div class="col-6 d-flex justify-content-center">
                            <div
                                class="border border-1 border-light rounded-2 p-4 d-flex flex-column align-items-center gap-2 w-100"
                                id="printToFollowRequest">
                                <img alt="to follow Icon" class="pb-1" src=""
                                     th:src="|https://web-assets.${@environment.getProperty('healius.digital.envName')}.healius.com.au/images/icon/forward.svg|"/>
                                <div class="caption fw-600 text-neutral-text pt-2">
                                    To follow request
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>

<button th:fragment="printLabelsButton(visitId, labNumber, labNumberPlusYear, labNumberCheckDigit, action, canPrintLabels)" type="button"
        class="action-print btn btn-sm btn-primary-fill ms-auto"
        th:classappend="${action}"
        disabled title="No printers available"
        th:if="${canPrintLabels}"
        th:data-visit-id="${visitId}"
        th:data-lab-number="${labNumber}"
        th:data-lab-number-plus-year="${labNumberPlusYear}"
        th:data-lab-number-check-digit="${labNumberCheckDigit}">
    <img th:src="@{/images/icon/printer.svg}" alt=""/> Print labels
</button>

<button th:fragment="scanLabelsButton(visitId, labNumber, labNumberPlusYear, action, canPrintLabels)" type="button"
        class="btn btn-sm btn-primary-fill ms-auto"
        data-bs-toggle="modal" data-bs-target="#scanLabelModal"
        th:classappend="${action}"
        th:data-visit-id="${visitId}"
        th:data-lab-number="${labNumber}"
        th:data-lab-number-plus-year="${labNumberPlusYear}">
    <img th:src="@{/images/icon/labels.svg}" alt=""/> Scan pre-printed lab number
</button>

<button th:fragment="printReferralButton(orderId, isToFollow)" type="button"
        th:with="printAction = ${isToFollow ? 'action-print-referral' : 'action-show-print-referral'}"
        class="btn btn-neutral-fill"
        th:classappend="|${printAction}|"
        th:data-order-id="${orderId}">
    Print
</button>

</html>
