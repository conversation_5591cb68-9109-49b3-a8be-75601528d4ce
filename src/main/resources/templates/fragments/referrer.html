<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
</head>
<body>

<div th:fragment="view(visit, clinicalNotes)" th:remove="tag">
    <div th:replace="~{fragments/view::panel('Referrer', ~{::referrers('order-referrer')})}"></div>
    <div th:replace="~{fragments/view::panel('Copy To', ~{::referrers('order-copyTo')})}"></div>
    <div th:replace="~{fragments/view::panel('Doctor\'s Clinical Notes', ~{::clinicalNotes(${clinicalNotes})})}"></div>

    <div class="cp-panel pb-0 d-sm-flex">
        <div th:replace="~{fragments/view::halfPanel('Type of referral', ~{::referralTypeView(${visit.requestPriorityStatus}, ${clinicalNotes})}, true)}"></div>
        <div th:replace="~{fragments/view::halfPanel('Fasting status', ~{::referralFastingView(${clinicalNotes})}, false)}"></div>
    </div>
</div>

<div th:fragment="edit(visit, clinicalNotes)">
    <th:block th:if="${orderView.aiRequestForm && !toFollow}">
        <div th:replace="~{fragments/edit::editModalButton('Add', 'plus', '#modal-order-referrer')}"></div>
    </th:block>
    <div th:replace="~{fragments/view::panel('Referrer', ~{::referrers('order-referrer')})}"></div>

    <th:block th:if="${!toFollow}">
        <div th:replace="~{fragments/edit::editModalButton('Add', 'plus', '#modal-order-copyTo')}"></div>
    </th:block>
    <div th:replace="~{fragments/view::panel('Copy To', ~{::referrers('order-copyTo')})}"></div>

    <div th:replace="~{fragments/modal::extraLargeModal('modal-order-referrer', 'Add referrer', 'Save', ~{::referrerSearch('order-referrer')})}"></div>
    <div th:replace="~{fragments/modal::extraLargeModal('modal-order-copyTo', 'Add copy to', 'Save', ~{::referrerSearch('order-copyTo')})}"></div>

    <th:block th:if="${orderView.aiRequestForm && !toFollow}">
        <div th:replace="~{fragments/edit::editModalButton('Edit', 'plus', '#modal-clinical-notes')}"></div>
    </th:block>
    <div th:replace="~{fragments/view::panel('Doctor\'s Clinical Notes', ~{::clinicalNotes(${clinicalNotes})})}"></div>
    <form name="referreralForm" id="referreralForm">
        <div class="cp-panel pb-0 d-sm-flex">
            <div th:replace="~{fragments/view::halfPanel('Type of referral', ~{::referralTypeForm(${visit.requestPriorityStatus}, ${visit})}, true)}"></div>
            <div th:replace="~{fragments/view::halfPanel('Fasting status', ~{::referralFastingForm(${visit})}, false)}"></div>
        </div>
    </form>
</div>

<div th:fragment="referralType(requestPriorityStatus)" id="requestPriority" class="referralType bodyContent" th:text="${requestPriorityStatus?.name}">
</div>

<div th:fragment="referralTypeView(requestPriorityStatus, clinicalNotes)" class="referralType bodyContent">
    <div class="row pb-4">
        <div class="col">
            <p class="body2 text-neutral-text mb-3" th:text="${#strings.capitalize(requestPriorityStatus?.name)}"></p>
        </div>
    </div>
</div>

<div th:fragment="referralFastingView(visit)" class="referralType bodyContent">
    <div class="row pb-4">
        <div class="col">
            <p class="body2 text-neutral-text mb-3">No information provided</p>
        </div>
    </div>
</div>

<div th:fragment="referralTypeForm(requestPriorityStatus, visit)" class="referralType bodyContent">
    <div class="row pb-4">
        <div class="col">
            <div th:each="requestPriority : ${requestPriorities}" class="form-check checkbox-lg form-check-inline">
                <input class="form-check-input" type="radio"
                       th:id=|requestPriorityStatus-${requestPriority}| name="requestPriority"
                       th:value="${requestPriority}" th:data-request-priority="${requestPriority.name}" th:checked="${requestPriorityStatus == requestPriority}"/>
                <label class="form-check-label body2 ps-2 pe-4" th:for=|requestPriorityStatus-${requestPriority}|>[[ ${requestPriority.name} ]]</label>
            </div>
        </div>
    </div>
    <div class="row pt-4 border-top d-none" id="requestPriorityDetails">
        <div class="col-4">
            <label for="priorityPhone"> Phone </label><br />
            <input class="form-control" id="priorityPhone" name="priorityPhone" type="text" autocomplete="off" th:value="${visit.priorityPhone}" />
        </div>
        <div class="col-4">
            <label for="priorityFax"> Fax </label>
            <input class="form-control" id="priorityFax" name="priorityFax" type="text" autocomplete="off" th:value="${visit.priorityFax}" />
        </div>
        <div class="col-4">
            <label for="priorityDate"> Result by</label>
            <input class="form-control" id="priorityDate" name="priorityDate" type="text" autocomplete="off" th:value="${visit.priorityDate}" />
        </div>
    </div>

</div>

<div th:fragment="referralFastingForm(visit)" class="referralType bodyContent">
    <div class="row pb-4">
        <div class="col">
            <div class="form-check checkbox-lg form-check-inline">
                <input class="form-check-input" type="radio"
                       id="requestFastingStatus-fasted" name="requestFastingStatus" value="true" 
                       th:checked="${visit.fasting}"
                       data-request-fasting="true" />
                <label class="form-check-label body2 ps-2 pe-4" for="requestFastingStatus-fasted">Fasted</label>
            </div>
            <div class="form-check checkbox-lg form-check-inline">
                <input class="form-check-input" type="radio"
                       id="requestFastingStatus-notFasted" name="requestFastingStatus" value="false" 
                       th:checked="${!visit.fasting}"
                       data-request-fasting="false" />
                <label class="form-check-label body2 ps-2 pe-4" for="requestFastingStatus-notFasted">Not fasted</label>
            </div>
        </div>
    </div>
    <div class="row pt-4 border-top" id="requestFastedDetails" th:classappend="${visit.fasting ? '' : 'd-none'}">
        <div class="col-4 pt-2">
            <p><span class="fw-500">Last eat and/or drink</span><br /><span class="fst-italic">other than water</span></p>
        </div>
        <div class="col-4">
            <label for="fastedDate"> Date </label>
            <input class="form-control" id="fastedDate" name="fastedDate" type="text" autocomplete="off" 
                   th:value="${visit.fastingDate}" />
        </div>
        <div class="col-4">
            <label for="fastedTime"> Time</label>
            <input class="form-control" id="fastedTime" name="fastedTime" type="text" autocomplete="off" 
                   th:value="${visit.fastingTime}" />
        </div>
    </div>

</div>

<div th:fragment="referrers(type)" class="d-flex flex-column">
    <div class="pt-2" th:classappend="${type}">
        <div class="d-flex border-top w-100 bodyContent">
            <div class="placeholder col-2 my-3 me-2">AAA</div>
            <div class="placeholder col-2 my-3 me-2">BBB</div>
            <div class="placeholder col-4 my-3 me-2">CCC</div>
            <div class="placeholder col-2 my-3 me-2">DDD</div>
            <div class="placeholder col-1 my-3 me-2"></div>
        </div>
        <div class="d-flex border-top w-100 bodyContent">
            <div class="placeholder col-3 my-3 me-2">AAA</div>
            <div class="placeholder col-2 my-3 me-2">BBB</div>
            <div class="placeholder col-4 my-3 me-2">CCC</div>
            <div class="placeholder col-2 my-3 me-2">DDD</div>
            <div class="placeholder col-1 my-3 me-2"></div>
        </div>
    </div>
</div>

<div th:fragment="referrerSearch(id)" th:id="|referrerPicker-${id}|">
    <div class="body2 pb-4 text-neutral-text">
        Start search with provider number, if available.
    </div>
    <div th:id="|${id}-missingSearchCriteria|" class="d-none d-flex gap-3 bg-error-fill p-3 mb-3 rounded-1">
        <div><img th:src="@{/images/icon/alert.svg}" alt=""/></div>
        <div class="">
            <div class="body1 fw-500 text-error-text">Unable to search</div>
            <div class="body2 text-neutral-text">Please enter at least one search criteria.</div>
        </div>
    </div>
    <form class="referrer-search-form" autocomplete="off">
        <div class="pb-4">
            <div class="position-relative">
                <div class="d-flex gap-0 align-items-start">
                    <div class="d-flex gap-3 justify-content-between align-items-end w-90">
                        <div class="w-25">
                            <label class="form-label text-neutral-text" th:for=|${id}-searchProviderNumber|>Provider number</label>
                            <input class="form-control has-clear" th:id=|${id}-searchProviderNumber| name="search" type="text"
                                   autocomplete="new-password" />
                            <div class="x-clearer referrer-copyto-clearer d-none">
                                <button class="action-clear-search clearButtonStyle" type="button"><img src="/images/icon/x.svg" width="24" alt=""/></button>
                            </div>
                        </div>
                        <div class="w-25">
                            <label class="form-label text-neutral-text" th:for=|${id}-searchLastName|>Last name</label>
                            <input class="form-control has-clear" th:id=|${id}-searchLastName| name="search" type="text"
                                   autocomplete="new-password" />
                            <div class="x-clearer referrer-copyto-clearer d-none">
                                <button class="action-clear-search clearButtonStyle" type="button"><img src="/images/icon/x.svg" width="24" alt=""/></button>
                            </div>
                        </div>
                        <div class="w-25">
                            <label class="form-label text-neutral-text" th:for=|${id}-searchFirstName|>First name</label>
                            <input class="form-control has-clear" th:id=|${id}-searchFirstName| name="search" type="text"
                                   autocomplete="new-password" />
                            <div class="x-clearer referrer-copyto-clearer d-none">
                                <button class="action-clear-search clearButtonStyle" type="button"><img src="/images/icon/x.svg" width="24" alt=""/></button>
                            </div>
                        </div>
                        <div class="w-25">
                            <label class="form-label text-neutral-text" th:for=|${id}-searchPostCodeSuburb|>Postcode or suburb</label>
                            <input class="form-control has-clear" th:id=|${id}-searchPostCodeSuburb| name="search" type="text"
                                   autocomplete="new-password" />
                            <div class="x-clearer referrer-copyto-clearer d-none">
                                <button class="action-clear-search clearButtonStyle" type="button"><img src="/images/icon/x.svg" width="24" alt=""/></button>
                            </div>
                        </div>
                    </div>

                    <div class="ms-3 mt-auto w-10">
                        <button class="btn btn-lg btn-primary-fill action-provider-search" type="button">Search</button>
                    </div>
                </div>
            </div>
        </div>

        <!---<div class="body2 text-neutral-text d-none">
            Search location:
            <div class="form-check form-check-inline">
                <input class="form-check-input mx-2" type="radio" name="companyOnly" th:id="|${id}-accProviderList|" value="true" checked>
                <label class="form-check-label" th:for="|${id}-accProviderList|" th:text="|${company.shortName} provider list|"></label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input mx-2" type="radio" name="companyOnly" th:id="|${id}-nationalProviderList|" value="false">
                <label class="form-check-label" th:for="|${id}-nationalProviderList|">National provider list</label>
            </div>
        </div>--->
    </form>

    <div class="mt-3">
        <div th:id=|${id}-selectedReferrers| class="mt-3 d-none">
            <div class="body2 fw-bolder text-primary-text pb-2">
                Selected doctors
            </div>
            <div class="referrer-list d-flex flex-wrap">
            </div>
        </div>
    </div>
    <div class="mt-3">
        <div class="d-none row px-0" th:id=|${id}-searchResultsContainer|>
            <div class="col-12">
                <div class="body2 fw-bolder text-primary-text lh-lg pb-1 ps-3">
                    Search results (<span class="search-results-count"></span>)
                </div>
                <div class="search-results-header row  px-0 py-2 border-bottom text-primary-text text-uppercase fw-600">
                    <div class="col-2">Provider No.</div>
                    <div class="col-2">Last name</div>
                    <div class="col-2">First name</div>
                    <div class="col-1">Postcode</div>
                    <div class="col-2">Suburb</div>
                    <div class="col-3">Address</div>
                </div>
                <div class="panelScrollContent row">
                    <div class="text-neutral-text bg-primary-fill-weak rounded-1 p-3" th:id=|${id}-noSearchResultsContainer|>
                        Provider not found?
                        <button class="btn text-primary-text border-primary-border-strong px-2 mx-2 action-provider-notfound" type="button">
                            <img th:src="@{/images/icon/plus.svg}" alt=""/> Mark as provider not found
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div th:fragment="clinicalNotes(clinicalNotes)" class="bodyContent doctors-clinical-notes" th:with="details = ${clinicalNotes}">
    <div th:fragment="noClinicalNotes" th:if="${details == null}">
        <div class="body2 text-neutral-text mb-3">
            Not provided yet
        </div>
    </div>

    <div class="d-flex flex-row pt-4" th:if="${details != null}">
        <div class="d-flex flex-row col-3" th:if="${details.urgent}">
            <div class="d-flex flex-row col-2">
                <img src="/images/icon/urgent.svg" width="40" height="40" />
            </div>
            <div class="d-flex flex-row col-10">
                <div class="mt-2 ps-3">
                    <span class="body2 doctorClinicalNotesSubHeader fw-600">Urgent</span>
                    <th:block th:replace="~{fragments/view::displayStringIf('Phone: ', ${details.urgent && !#strings.isEmpty(details.urgentPhone)}, ${details.urgentPhone})}"/>
                    <th:block th:replace="~{fragments/view::displayStringIf('Fax: ', ${details.urgent && !#strings.isEmpty(details.urgentFax)}, ${details.urgentFax})}"/>
                    <th:block th:replace="~{fragments/view::displayStringIf('Result by: ', ${details.urgent && !#strings.isEmpty(details.urgentBy)}, ${details.urgentBy})}"/>
                </div>
            </div>
        </div>

        <div class="d-flex flex-row col-3" th:if="${details.pregnant}">
            <div class="d-flex flex-row col-2">
               <img src="/images/icon/pregnant.svg" width="40" height="40" />
            </div>
            <div class="d-flex flex-row col-10">
                <div class="mt-2 ps-3">
                    <span class="body2 doctorClinicalNotesSubHeader fw-600">Pregnant</span>
                    <th:block th:replace="~{fragments/view::displayStringIf('EDC: ', ${!#strings.isEmpty(details.edcString)}, ${details.edcString})}"/>
                    <th:block th:replace="~{fragments/view::displayStringIf('LMP: ', ${!#strings.isEmpty(details.lnmpString)}, ${details.lnmpString})}"/>
                </div>
            </div>

        </div>

        <div class="d-flex flex-row col-6" th:if="${details.cervix || details.hysterectomy}">
            <div class="d-flex flex-row col-1">
                <img src="/images/icon/cervical.svg" width="40" height="40" />
            </div>
            <div class="d-flex flex-row col-11">
                <div class="mt-2 ps-3">
                    <span class="body2 doctorClinicalNotesSubHeader fw-600">Cervical screening</span>
                    <!--- todo: need help with what fields go where and when to show them - should be in a ticket at some point --->
                    <th:block th:with="
                          site = ${details.cervix || details.suspicious || details.benign || details.ectropion},
                          siteList = ${ {
                                (details.cervix ? 'Cervix' : ''),
                                (details.suspicious ? 'Suspicious' : ''),
                                (details.benign ? 'Benign' : ''),
                                (details.ectropion ? 'Ectropion' : '')
                                } }">
                        <th:block th:replace="~{fragments/view::displayStringListIf('Site and appearance: ', ${site}, ${siteList})}" />
                    </th:block>
                    <th:block th:with="
                          symptomatic = ${details.postmenopausalBleeding || details.postcoitalBleeding || details.intemenstrualBleeding || details.vaginalDischarge || details.dyspareunia},
                          symptomaticList = ${ {
                                (details.postmenopausalBleeding ? 'Postmenopausal bleeding' : ''),
                                (details.postcoitalBleeding ? 'Postcoital bleeding' : ''),
                                (details.intemenstrualBleeding ? 'Intermenstrual bleeding' : ''),
                                (details.vaginalDischarge ? 'Vaginal discharge' : ''),
                                (details.dyspareunia ? 'Dyspareunia' : '')
                                } }">
                        <th:block th:replace="~{fragments/view::displayStringListIf('Symptomatic: ', ${symptomatic}, ${symptomaticList})}" />
                    </th:block>
                    <th:block th:with="
                          history = ${details.hysterectomy || details.adenocarcinomaInSitu || details.hsil || details.immunodeficient || details.desExposure || details.radioTherapy},
                          historyList = ${ {
                                (details.hysterectomy ? 'Hysterectomy' : ''),
                                (details.immunodeficient ? 'Immunodeficient' : ''),
                                (details.adenocarcinomaInSitu ? 'Adenocarcinoma in situ' : ''),
                                (details.hsil ? 'HSIL (Test of cure)' : ''),
                                (details.desExposure ? 'DES exposure' : ''),
                                (details.radioTherapy ? 'Radiotherapy' : '')
                                } }">
                        <th:block th:replace="~{fragments/view::displayStringListIf('Past history: ', ${history}, ${historyList})}" />
                    </th:block>
                    <th:block th:with="
                          other = ${details.postNatal || details.postMenopausal || details.radioTherapy || details.iucd || details.abnormalBleeding},
                          otherList = ${ {
                                (details.postNatal ? 'Post natal' : ''),
                                (details.postMenopausal ? 'Post menopausal' : ''),
                                (details.iucd ? 'IUCD' : ''),
                                (details.abnormalBleeding ? 'Abnormal bleeding' : '')
                                } }">
                        <th:block th:replace="~{fragments/view::displayStringListIf('Other: ', ${other}, ${otherList})}" />
                    </th:block>
                </div>
            </div>
        </div>
    </div><!---END1--->

    <div class="d-flex flex-row pt-4 doctor-notes" th:classappend="${details != null && !#strings.isEmpty(details.clinicalNotes) ? '' : 'd-none'}">
        <div class="border-top body2 text-neutral-text pt-5 w-100" th:text="${details != null ? details.clinicalNotes : ''}"></div>
    </div>
</div>
</body>
</html>