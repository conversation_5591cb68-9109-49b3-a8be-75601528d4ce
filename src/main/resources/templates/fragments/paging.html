<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
</head>
<body>

<th:block th:fragment="paging(url, page)">
    <div th:if="${page.empty}" class="col-md-12 pb-3 px-2">Showing no results</div>
    <div th:if="${!page.empty}" class="container-fluid container-xl">
        <div class="d-block d-md-flex">
            <th:block th:replace="~{::pageDetails(${page})}"/>
            <th:block th:replace="~{::pageLengthDropdown(${page})}"/>
            <div>
                <th:block th:replace="~{::pagination(${url}, ${page})}"/>
            </div>
        </div>
    </div>
</th:block>

<div th:fragment="pageDetails(page)" class="text-primary-fill me-auto mt-auto mb-3 mb-md-auto">
    Showing results [[${page.number * page.size + 1}]] - [[${page.number * page.size + page.numberOfElements}]] of [[${page.totalElements}]]
</div>

<div th:fragment="pageLengthDropdown(page)" class="me-3 mt-auto mb-3 mb-md-auto">
    <select aria-label="Records Per Page" class="pagingSelect" id="recordsPerPageSelect" name="recordsPerPageSelect" onchange="collectorPortalView.updatePageSize(this)" >
        <option th:each="i : ${pagingLengths}"
                th:id="|records${i}|"
                th:selected="${page.size == i}"
                th:value="${i}" th:text="${i}">
        </option>

    </select>
    <label for="recordsPerPageSelect" class="text-primary-fill ps-2">  [[${page}]] Results per page</label>
</div>

<nav th:fragment="pagination(url, results)" aria-label="Pagination navigation">
    <ul class="pagination justify-content-left justify-content-md-center mb-1"
        th:with="
            currentPage=${results.number},
            totalPages=${results.totalPages},
            startBlock=${T(java.lang.Math).max(currentPage - 1, 1)},
            endBlock=${T(java.lang.Math).min(currentPage + 1, totalPages - 2)}
        ">

        <!-- Previous -->
        <li class="page-item" th:classappend="${!results.hasPrevious} ? 'disabled'">
            <th:block th:replace="~{::pagingLinkWithLabel('&lsaquo;', ${url}, ${currentPage - 1})}"/>
        </li>
        <!-- First -->
        <li class="page-item" th:classappend="${currentPage == 0} ? 'active'">
            <th:block th:replace="~{::pagingLink(${url}, 0)}"/>
        </li>
        <!-- Dots -->
        <li class="page-item disabled" th:if="${currentPage - 2 > 0}">
            <span class="page-link" href="#">...</span>
        </li>
        <!-- Pages (block of 3 around current page) -->
        <li class="page-item" th:if="${endBlock >= startBlock}"
            th:each="page : ${#numbers.sequence(startBlock, endBlock)}"
            th:classappend="${page == results.number} ? 'active'">
            <th:block th:replace="~{::pagingLink(${url}, ${page})}"></th:block>
        </li>
        <!-- Dots -->
        <li class="page-item disabled" th:if="${currentPage + 2 < totalPages}">
            <span class="page-link" href="#">...</span>
        </li>
        <!-- Last -->
        <li class="page-item" th:if="${totalPages > 1}" th:classappend="${currentPage == totalPages - 1} ? 'active'">
            <th:block th:replace="~{::pagingLink(${url} ,${totalPages - 1})}"></th:block>
        </li>
        <!-- Next -->
        <li class="page-item" th:classappend="${!results.hasNext} ? 'disabled'">
            <th:block th:replace="~{::pagingLinkWithLabel('&rsaquo;', ${url}, ${currentPage + 1})}"/>
        </li>
    </ul>
</nav>

<a th:fragment="pagingLinkWithLabel(label, url, page)"
   class="page-link"
   th:href="@{${url}(page=${page}, sort=${param.sort}, size=${param.size})}"
   th:text="${label}">
</a>

<th:block th:fragment="pagingLink(url, page)" th:replace="~{::pagingLinkWithLabel(${page + 1}, ${url}, ${page})}"/>

</body>
</html>