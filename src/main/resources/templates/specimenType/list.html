<!DOCTYPE html>
<html class="h-100"
      lang="en"
      layout:decorate="~{core/layout/healius(
        header=~{core/components/header::copy_header('Specimen Type')},
        header_menu=~{fragments/sidebar::main_crumb('specimenType')}
      )}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
>

<head>
</head>
<div layout:fragment="content" th:remove="tag">
    <specimen-type-component></specimen-type-component>
    <div class="row pb-3">
        <div class="col-12" sec:authorize="hasAnyAuthority('APPROLE_DigitalAdmin')">
            <div class="d-flex">
                <div class="ms-auto">
                    <button class="modal-action btn btn-sm btn-primary-fill text-uppercase mt-auto mb-4" id="addToTable">
                        <img th:src="@{/images/icon/plusWhite.svg}" alt="add new"> Add new
                    </button>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="rounded border border-primary bg-white table-responsive">
                <table class="table" id="data-list-specimenType">
                    <thead>
                    <tr>
                        <!-- Updated table headers -->
                        <th class="text-uppercase col-2 ps-5">Specimen label</th>
                        <th class="text-uppercase col-9">Sequence</th>
                        <th class="text-uppercase col-1">Active</th>
                    </tr>
                    </thead>
                    <tbody id="table-body" class="border-bottom-transparent">
                    <tr>
                        <th>
                            <p class="placeholder-wave">
                                <span class="placeholder col-12"></span>
                            </p>
                        </th>
                        <th>
                            <p class="placeholder-wave">
                                <span class="placeholder col-12"></span>
                            </p>
                        </th>
                        <th>
                            <p class="placeholder-wave">
                                <span class="placeholder col-12"></span>
                            </p>
                        </th>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<th:block layout:fragment="modals">
    <th:block th:replace="~{fragments/modal::errorModal}"/>
    <th:block th:replace="~{fragments/modal::apiRedirectModal}"/>
    <div th:replace="~{core/components/modal::largeModalAdmin('specimenTypeModal', '', 'Save', ~{::#addEdit-specimen-type})}">
        <div id="addEdit-specimen-type">
            <div th:replace="~{fragments/modal::inUseWarning}"></div>
            <form id="specimen-type-form">
            </form>
        </div>
    </div>
</th:block>

<th:block layout:fragment="scripts" th:remove="tag">
    <script th:src="@{/js/specimenType.js}" type="module"></script>
</th:block>

</html>
