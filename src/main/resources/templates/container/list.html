<!DOCTYPE html>
<html class="h-100"
      lang="en"
      layout:decorate="~{core/layout/healius(
        header=~{core/components/header::copy_header('Containers')},
        header_menu=~{fragments/sidebar::main_crumb('container')}
      )}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
>


<head>
    <style>
        #imagePicker {
            display: none;
            height: 200px;
            overflow: scroll;
        }

        #imagePicker li {
            cursor: pointer;

            /* don't display the bullet for this list */
            list-style: none;
            text-align: left;
            padding-bottom: 8px;
        }

        #imagePicker li img {
            height: 24px;
            padding-right: 16px;
        }

        #select-image {
            cursor: pointer;
            list-style: none;
            text-align: left;
        }

        #select-image img {
            height: 24px;
            padding-right: 8px;
        }
    </style>
</head>

<div layout:fragment="content" th:remove="tag">
    <div class="row pb-3">
        <div class="col-12" sec:authorize="hasAnyAuthority('APPROLE_DigitalAdmin')">
            <div class="d-flex">
                <div class="ms-auto">
                    <button class="modal-action btn btn-sm btn-primary-fill text-uppercase mt-auto mb-4" id="addToTable">
                        <img th:src="@{/images/icon/plusWhite.svg}" alt="add new reason"> Add new
                    </button>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="rounded border border-primary bg-white table-responsive">
                <table class="table" id="data-list-container">
                    <thead>
                    <tr>
                        <th class="text-uppercase col-1 ps-5">BU</th>
                        <th class="text-uppercase col-1">Type</th>
                        <th class="text-uppercase col-7">Description</th>
                        <th class="text-uppercase col-1">Common</th>
                        <th class="text-uppercase col-1">Active</th>
                    </tr>
                    </thead>
                    <tbody id="table-body" class="border-bottom-transparent">
                        <tr>
                            <th>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                            </th>
                            <th>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                            </th>
                            <th>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                            </th>
                            <th>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                            </th>
                            <th>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                                <p class="placeholder-wave">
                                    <span class="placeholder col-12"></span>
                                </p>
                            </th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div th:replace="~{core/components/modal::largeModalAdmin('containerModal', '', 'Save', ~{::#addEdit-container})}">
        <div id="addEdit-container">
            <form id="container-form" class="border-bottom-transparent">
                <tr>
                    <th>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                    </th>
                    <th>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                    </th>
                    <th>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                    </th>
                    <th>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                        <p class="placeholder-wave">
                            <span class="placeholder col-12"></span>
                        </p>
                    </th>
                </tr>
            </form>
        </div>
    </div>
</div>

<th:block layout:fragment="scripts" th:remove="tag">
    <script th:src="@{/js/container.js}" type="module"></script>
    <script th:src="@{/js/selectWithImage.js}" defer></script>
</th:block>

<th:block layout:fragment="modals">
    <th:block th:replace="~{fragments/modal::errorModal}"/>
</th:block>

</html>
