<!DOCTYPE html>
<html class="h-100" lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta th:content="${_csrf.token}" name="_csrf"/>
    <meta th:content="${_csrf.parameterName}" name="_csrf_parameterName"/>
    <title>Healius Collectors Portal</title>
    <th:block th:insert="~{fragments/styles::copy_styles}"/>
    <th:block th:insert="~{fragments/styles::copy_styles_validation}"/>
    <style>

        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .form-container {
            max-width: 500px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .form-header {
            text-align: center;
            margin-bottom: 30px;
            color: #0063AF;
        }
        .btn-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
    </style>
</head>

<body>
<div>
    <div class="mainContentACC">
        <div class="container">
            <div class="form-container">
                <div class="logo">
                    <img th:src="@{/images/logos/healius.svg}" alt="Healius Logo" style="max-width: 200px;">
                </div>
                <h2 class="form-header">Patient Referral</h2>

                <div class="alert alert-error-fill-strong" th:if="${successMessage}" th:text="${successMessage}"></div>
                <div class="alert alert-warning-fill-strong" th:if="${errorMessage}" th:text="${errorMessage}"></div>

                <form id="referralForm" action="#" th:action="@{/referralGeneratorTraining/submit}" th:object="${referralForm}" method="post">
                    <div class="mb-3">
                        <label for="mobilePhone" class="form-label">Mobile Number</label>
                        <input type="tel" class="form-control" id="mobilePhone" th:field="*{mobilePhone}"
                               placeholder="04XX XXX XXX" required
                               pattern="^04[0-9]{2}\s?[0-9]{3}\s?[0-9]{3}$"
                               title="Please enter a valid Australian mobile number (format: 04XX XXX XXX)">
                    </div>

                    <p class="text-muted mb-4">Clinical details will be automatically generated by the system.</p>

                    <div class="btn-container">
                        <button type="submit" class="btn btn-primary-fill" id="singleButton">Generate</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="mt-auto acc-envBand">
        <th:block th:insert="~{core/components/enviromentTag::envStatement}"/>
        <div class="pt-3 text-center">
            <th:block th:insert="~{core/components/enviromentTag::version}"/>
        </div>
    </div>
</div>
<th:block th:insert="~{fragments/fragments::logoutContainer}"/>
<th:block th:insert="~{fragments/scripts::copy_scripts}"/>
<th:block th:insert="~{fragments/scripts::copy_scripts_validator}"/>
</body>
</html>