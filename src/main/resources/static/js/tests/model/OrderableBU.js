/*
 * SVC Tests API
 * Healius Test api
 *
 * OpenAPI spec version: 1.0.0
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 *
 * Swagger Codegen version: 3.0.68
 *
 * Do not edit the class manually.
 *
 */
import ApiClient from '../ApiClient.js';

/**
 * The OrderableBU model module.
 * @module model/OrderableBU
 * @version 1.0.0
 */
export default class OrderableBU {
  /**
   * Constructs a new <code>OrderableBU</code>.
   * Business unit details to create
   * @alias module:model/OrderableBU
   * @class
   */
  constructor() {
  }

  /**
   * Constructs a <code>OrderableBU</code> from a plain JavaScript object, optionally creating a new instance.
   * Copies all relevant properties from <code>data</code> to <code>obj</code> if supplied or a new instance if not.
   * @param {Object} data The plain JavaScript object bearing properties of interest.
   * @param {module:model/OrderableBU} obj Optional instance to populate.
   * @return {module:model/OrderableBU} The populated <code>OrderableBU</code> instance.
   */
  static constructFromObject(data, obj) {
    if (data) {
      obj = obj || new OrderableBU();
      if (data.hasOwnProperty('id'))
        obj.id = ApiClient.convertToType(data['id'], 'String');
      if (data.hasOwnProperty('orderableId'))
        obj.orderableId = ApiClient.convertToType(data['orderableId'], 'String');
      if (data.hasOwnProperty('companyCode'))
        obj.companyCode = ApiClient.convertToType(data['companyCode'], 'String');
      if (data.hasOwnProperty('name'))
        obj.name = ApiClient.convertToType(data['name'], 'String');
      if (data.hasOwnProperty('code'))
        obj.code = ApiClient.convertToType(data['code'], 'String');
      if (data.hasOwnProperty('specimenRequired'))
        obj.specimenRequired = ApiClient.convertToType(data['specimenRequired'], 'Number');
      if (data.hasOwnProperty('collectionFrequency'))
        obj.collectionFrequency = ApiClient.convertToType(data['collectionFrequency'], 'String');
      if (data.hasOwnProperty('enabled'))
        obj.enabled = ApiClient.convertToType(data['enabled'], 'Boolean');
      if (data.hasOwnProperty('buSortDestination'))
        obj.buSortDestination = ApiClient.convertToType(data['buSortDestination'], 'String');
      if (data.hasOwnProperty('clinicalNotesSpecificText'))
        obj.clinicalNotesSpecificText = ApiClient.convertToType(data['clinicalNotesSpecificText'], 'String');
      if (data.hasOwnProperty('generalClinicalNotes'))
        obj.generalClinicalNotes = ApiClient.convertToType(data['generalClinicalNotes'], 'Boolean');
      if (data.hasOwnProperty('sendAwayTest'))
        obj.sendAwayTest = ApiClient.convertToType(data['sendAwayTest'], 'Boolean');
      if (data.hasOwnProperty('sendAwayTestDestination'))
        obj.sendAwayTestDestination = ApiClient.convertToType(data['sendAwayTestDestination'], 'String');
      if (data.hasOwnProperty('recordSiteOfCollection'))
        obj.recordSiteOfCollection = ApiClient.convertToType(data['recordSiteOfCollection'], 'Boolean');
      if (data.hasOwnProperty('mustBeFasting'))
        obj.mustBeFasting = ApiClient.convertToType(data['mustBeFasting'], 'Boolean');
      if (data.hasOwnProperty('collectionSOP'))
        obj.collectionSOP = ApiClient.convertToType(data['collectionSOP'], 'String');
      if (data.hasOwnProperty('questionnaire'))
        obj.questionnaire = ApiClient.convertToType(data['questionnaire'], 'String');
      if (data.hasOwnProperty('patientPreparation1'))
        obj.patientPreparation1 = ApiClient.convertToType(data['patientPreparation1'], 'String');
      if (data.hasOwnProperty('patientPreparation2'))
        obj.patientPreparation2 = ApiClient.convertToType(data['patientPreparation2'], 'String');
      if (data.hasOwnProperty('handlingAndSpecialInstructions'))
        obj.handlingAndSpecialInstructions = ApiClient.convertToType(data['handlingAndSpecialInstructions'], 'String');
      if (data.hasOwnProperty('containerRule'))
        obj.containerRule = ApiClient.convertToType(data['containerRule'], 'String');
      if (data.hasOwnProperty('containers'))
        obj.containers = ApiClient.convertToType(data['containers'], [Object]);
    }
    return obj;
  }
}

/**
 * @member {String} id
 */
OrderableBU.prototype.id = undefined;

/**
 * @member {String} orderableId
 */
OrderableBU.prototype.orderableId = undefined;

/**
 * @member {String} companyCode
 */
OrderableBU.prototype.companyCode = undefined;

/**
 * @member {String} name
 */
OrderableBU.prototype.name = undefined;

/**
 * @member {String} code
 */
OrderableBU.prototype.code = undefined;

/**
 * @member {Number} specimenRequired
 */
OrderableBU.prototype.specimenRequired = undefined;

/**
 * Allowed values for the <code>collectionFrequency</code> property.
 * @enum {String}
 * @readonly
 */
OrderableBU.CollectionFrequencyEnum = {
  /**
   * value: "HOURS"
   * @const
   */
  HOURS: "HOURS",

  /**
   * value: "DAYS"
   * @const
   */
  DAYS: "DAYS"
};
/**
 * @member {module:model/OrderableBU.CollectionFrequencyEnum} collectionFrequency
 */
OrderableBU.prototype.collectionFrequency = undefined;

/**
 * @member {Boolean} enabled
 */
OrderableBU.prototype.enabled = undefined;

/**
 * @member {String} buSortDestination
 */
OrderableBU.prototype.buSortDestination = undefined;

/**
 * @member {String} clinicalNotesSpecificText
 */
OrderableBU.prototype.clinicalNotesSpecificText = undefined;

/**
 * @member {Boolean} generalClinicalNotes
 */
OrderableBU.prototype.generalClinicalNotes = undefined;

/**
 * @member {Boolean} sendAwayTest
 */
OrderableBU.prototype.sendAwayTest = undefined;

/**
 * @member {String} sendAwayTestDestination
 */
OrderableBU.prototype.sendAwayTestDestination = undefined;

/**
 * @member {Boolean} recordSiteOfCollection
 */
OrderableBU.prototype.recordSiteOfCollection = undefined;

/**
 * @member {Boolean} mustBeFasting
 */
OrderableBU.prototype.mustBeFasting = undefined;

/**
 * @member {String} collectionSOP
 */
OrderableBU.prototype.collectionSOP = undefined;

/**
 * @member {String} questionnaire
 */
OrderableBU.prototype.questionnaire = undefined;

/**
 * @member {String} patientPreparation1
 */
OrderableBU.prototype.patientPreparation1 = undefined;

/**
 * @member {String} patientPreparation2
 */
OrderableBU.prototype.patientPreparation2 = undefined;

/**
 * @member {String} handlingAndSpecialInstructions
 */
OrderableBU.prototype.handlingAndSpecialInstructions = undefined;

/**
 * @member {String} containerRule
 */
OrderableBU.prototype.containerRule = undefined;

/**
 * @member {Array.<Object>} containers
 */
OrderableBU.prototype.containers = undefined;
