import {PlacesAutocomplete} from "./placesAutocomplete.js";

/**
 * @class PatientResultsComponent
 * @description Web Component for displaying patient and eReferral results with filterable views and expandable details
 * @extends HTMLElement
 */
class PatientResultsComponent extends HTMLElement {
    /**
     * @constructor
     * @description Initialize the component and set up the DOM
     */
    constructor() {
        super();
        this.isLoading = true;
        this.hasAttemptedLoad = false;
        this.collectorPortalPlacesAutocomplete = null;
        setTimeout(() => this.initialize(), 100);
    }

    /**
     * @description Initialize the component after data is available
     */
    initialize() {
        this.render();
        this.adoptExternalStyles();
        this.showLoadingState();
        this.processServerData();
        this.initializeEventListeners();
        this.initializeFilters();
        this.initializePatientTable();
        this.filterContent('patients');
        this.updateFilterCounts();
        this.hideLoadingState();
        this.hasAttemptedLoad = true;
        this.updateSearchCriteria();
        this.initPlacesAutoComplete();
        
        // Initialize empty states and create profile section after everything else is done
        setTimeout(() => {
            this.initializeEmptyStates();
            this.initializeCreateProfileSection();
        }, 100);
    }

    /**
     * @description Lifecycle callback when element is added to the DOM
     */
    connectedCallback() {
        if (window.location.pathname === '/patient/lookup') {
            this.populateFormFieldsFromURL();
        }

        setTimeout(() => {
            this.updateFilterCounts();
        }, 100);
    }

    /**
     * @description Lifecycle callback when element is removed from the DOM
     */
    disconnectedCallback() {
        // Reserved for future cleanup if needed
    }

    /**
     * Render the search criteria section showing what was searched for
     * @returns {string} HTML string for the search criteria section
     */
    renderSearchCriteria() {
        const storedData = localStorage.getItem('patientSearchData');
        if (!storedData) return '';

        try {
            const searchData = JSON.parse(storedData);
            const values = [];
            if (searchData.familyName) values.push(searchData.familyName);
            if (searchData.givenName) values.push(searchData.givenName);
            if (searchData.mobilePhone) values.push(searchData.mobilePhone);
            if (searchData.dateOfBirth) values.push(searchData.dateOfBirth);
            if (searchData.medicareNo) values.push(searchData.medicareNo);
            if (searchData.sex) values.push(searchData.sex);

            if (values.length === 0) return '';

            return `
                <div class="col-12 mb-4">
                    <div class="d-flex align-items-center bg-white shadow-offset-medium rounded-2 py-3 px-5">
                        <div class="d-flex align-items-center flex-fill">
                            <div class="description text-neutral-text">
                                Showing results for <span class="fw-600">${values.join(', ')}</span>
                            </div>
                        </div>
                        <button class="action-return-to-search btn btn-sm text-primary-text border-primary-border-strong" type="button">
                            Refine search
                        </button>
                    </div>
                </div>
            `;
        } catch (error) {
            logger.error('Error parsing stored search data:', error);
            return '';
        }
    }

    /**
     * Renders the component's HTML structure.
     * Creates a table for displaying test results and a container for e-referrals.
     */
    render() {
        this.innerHTML = `
            <style id="adopted-styles"></style>
            <style>
                .border-primary-border-strong {
                    border: 1px solid #033572 !important;
                }
                .component-container {
                    opacity: 0;
                    transition: opacity 0.4s ease-in-out;
                }
                .component-container.rendered {
                    opacity: 1;
                }
                .loading-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10;
                    transition: opacity 0.4s ease-in-out;
                }
                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #033572;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .placeholder {
                    background-color: #e9ecef;
                    animation: placeholder-glow 2s ease-in-out infinite;
                }
                @keyframes placeholder-glow {
                    50% {
                        opacity: 0.5;
                    }
                }
            </style>
            <div id="results-container" class="component-container">
                <div class="loading-overlay">
                    <div class="loading-spinner"></div>
                </div>
                <div id="search-criteria-container">
                    ${this.isLoading ? this.renderPlaceholderSearchCriteria() : this.renderSearchCriteria()}
                </div>
                <div id="filter-container" class="col-12">
                    ${this.isLoading ? this.renderPlaceholderFilters() : this.renderFilterButtons()}
                </div>
                <div id="patient-lookup-table-container" class="col-12 rounded-2 bg-white shadow-offset-medium overflow-hidden">
                    <table class="table custom-table mb-0">
                        ${this.renderTableHeader()}
                        <tbody>
                            ${this.isLoading ? Array(3).fill(this.renderPlaceholderRow()).join('') : ''}
                        </tbody>
                    </table>
                </div>
                <div id="ereferrals-container" class="col-12 d-none"></div>
                <div id="create-profile-container" class="d-none"></div>
            </div>
        `;
    }

    /**
     * @description Copy external styles from the document into the DOM (noop for now)
     */
    adoptExternalStyles() {
        // No Shadow DOM, so nothing to do here
    }

    /**
     * Initialize empty state messages when no data is available
     */
    initializeEmptyStates() {
        const patientTableContainer = this.querySelector('#patient-lookup-table-container');
        const eReferralsContainer = this.querySelector('#ereferrals-container');
        
        // Don't show empty states until loading is complete
        if (!this.hasAttemptedLoad || this.isLoading) return;

        if (patientTableContainer) {
            const tbody = patientTableContainer.querySelector('tbody');
            if (tbody && (!this.patients || this.patients.length === 0)) {
                tbody.innerHTML = this.renderEmptyState('patient profile');
            }
        }

        if (eReferralsContainer) {
            const tbody = eReferralsContainer.querySelector('tbody');
            if (tbody && (!this.eReferrals || this.eReferrals.length === 0)) {
                tbody.innerHTML = this.renderEmptyState('eReferral');
            }
        }
    }

    /**
     * Render the empty state message for a given entity type
     *
     * @param {string} entityType - The type of entity (e.g., 'patient profile', 'eReferral')
     * @returns {string} HTML string for the empty state
     */
    renderEmptyState(entityType) {
        return `
            <tr>
                <td colspan="7" class="p-0">
                    <div class="d-flex flex-column justify-content-center align-items-center w-100"
                         style="padding-top: 96px; padding-bottom: 96px">
                        <div class="d-flex flex-column align-items-center text-center w-100">
                            <h6 class="fw-600 text-neutral-text mb-2">
                                Sorry, we are unable to find a matching ${entityType}
                            </h6>
                            <p class="body2 text-neutral-text mb-0">
                                Please enter more search criteria
                            </p>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Render the table header columns
     *
     * @returns {string} HTML string for the table header
     */
    renderTableHeader() {
        return `
            <thead>
                <tr class="border-bottom border-light">
                    <th scope="col" class="col-1 text-primary-text caption fw-600 text-uppercase px-4 py-3">Type</th>
                    <th scope="col" class="col-2 text-primary-text caption fw-600 text-uppercase px-4 py-3">Last Name</th>
                    <th scope="col" class="col-2 text-primary-text caption fw-600 text-uppercase px-4 py-3">First Name</th>
                    <th scope="col" class="col-1 text-primary-text caption fw-600 text-uppercase px-4 py-3">Birth Sex</th>
                    <th scope="col" class="col-2 text-primary-text caption fw-600 text-uppercase px-4 py-3">DOB</th>
                    <th scope="col" class="col-3 text-primary-text caption fw-600 text-uppercase px-4 py-3">Medicare</th>
                    <th scope="col" class="col-1 text-primary-text caption px-4 py-3"></th>
                </tr>
            </thead>
        `;
    }

    /**
     * @description Process data from the server into a standardized format
     */
    processServerData() {
        try {
            this.patients = this.normalizeData(typeof patients !== 'undefined' ? patients : null);
            this.eReferrals = this.normalizeData(typeof eReferrals !== 'undefined' ? eReferrals : null);
        } catch (error) {
            this.patients = [];
            this.eReferrals = [];
        }
    }

    /**
     * @description Normalize data to ensure it's always an array
     * @param {*} data - Data to normalize
     * @returns {Array} Normalized data as array
     */
    normalizeData(data) {
        if (!data) return [];
        if (Array.isArray(data)) return data;
        if (typeof data === 'object') return [data];
        return [];
    }

    /**
     * Render the filter buttons to toggle between patients and eReferrals
     *
     * @returns {string} HTML string for the filter buttons
     */
    renderFilterButtons() {
        const patientCount = this.patients ? this.patients.length : 0;
        const eRefCount = this.eReferrals ? this.eReferrals.length : 0;

        return `
            <div class="body1 text-primary-text">Search Results</div>
            <div class="d-flex input-group py-3">
                <button class="btn btn-sm btn-outline-primary-fill text-primary-text rounded-end-0 filter-btn"
                        type="button"
                        data-filter-type="ereferrals">
                    eReferrals <span>(${eRefCount})</span>
                </button>
                <button class="btn btn-sm btn-primary-fill text-white rounded-start-0 filter-btn active"
                        type="button"
                        data-filter-type="patients">
                    Patients <span>(${patientCount})</span>
                </button>
            </div>
        `;
    }

    /**
     * Render a single eReferral row
     *
     * @param {Object} eRef - The eReferral data object
     * @returns {string} HTML string for the eReferral row
     */
    renderEReferralRow(eRef) {
        return `
            <td class="col-1 px-4 py-3 align-middle">
                <img src="/images/icon/ereferral-red.svg" alt="eReferral" />
            </td>
            <td class="col-2 px-4 py-3 body2 fw-500 align-middle">${eRef.referredPatient?.familyName}</td>
            <td class="col-2 px-4 py-3 body2 align-middle">${eRef.referredPatient?.givenName}</td>
            <td class="col-1 px-4 py-3 body2 align-middle">${eRef.referredPatient?.sex}</td>
            <td class="col-2 px-4 py-3 body2 align-middle">${eRef.referredPatient?.dateOfBirth}</td>
            <td class="col-3 px-4 py-3 body2 align-middle">${eRef.referredPatient?.medicareNo}</td>
            <td class="col-1 px-4 py-3 text-end align-middle">
                <img src="/images/icon/chevron-down-blue.svg" alt="Expand/Collapse" class="chevron-icon"/>
            </td>
        `;
    }

    /**
     * Format a date string to the format 'DD MMM YYYY'
     *
     * @param {string} dateString - The date string to format
     * @returns {string} Formatted date string
     */
    formatDate(dateString) {
        if (!dateString) {
            return '';
        }

        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return dateString;
        }

        const day = String(date.getDate()).padStart(2, '0');
        const month = date.toLocaleString('en-US', {month: 'short'});
        const year = date.getFullYear();

        return `${day} ${month} ${year}`;
    }

    /**
     * Render the expanded details view for an eReferral
     *
     * @param {Object} eRef - The eReferral data object
     * @returns {string} HTML string for the eReferral details
     */
    renderEReferralDetails(eRef) {
        return `
            <td colspan="7" class="p-0">
                <div class="bg-neutral-fill p-4">
                    <div class="row border-bottom pb-3 mb-3">
                        <div class="col-6 d-flex align-items-center">
                            <div class="body2 text-accent-blue">e-Referral</div>
                        </div>
                        <div class="col-6 text-end">
                            <span class="body2 text-neutral-text">Request date: ${this.formatDate(
            eRef?.requestedDate)}</span>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-8">
                            <div class="row mb-2">
                                <div class="col-4">
                                    <div class="d-flex align-items-center">
                                        <img src="/images/icon/qrSmall.svg" alt="QR Code" class="me-3 icon-sm" />
                                        <span class="body2 text-neutral-text">Request code</span>
                                    </div>
                                </div>
                                <div class="col-8">
                                    <span class="body2 text-neutral-text">${eRef?.healiusId
        || ''}</span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4">
                                    <div class="d-flex align-items-center">
                                        <img src="/images/icon/tube-sample.svg" alt="Tests" class="me-3 icon-sm" />
                                        <span class="body2 text-neutral-text">Tests</span>
                                    </div>
                                </div>
                                <div class="col-8">
                                    <span class="body2 text-neutral-text">${eRef?.orderables?.map(
            i => i.name).join(', ')}</span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4">
                                    <div class="d-flex align-items-center">
                                        <img src="/images/icon/stethoscope.svg" alt="Referrer" class="me-3 icon-sm" />
                                        <span class="body2 text-neutral-text">Referrer</span>
                                    </div>
                                </div>
                                <div class="col-8">
                                    <span class="body2 text-neutral-text">${eRef?.referrer?.title
        || ''} ${eRef?.referrer?.givenName || ''} ${eRef?.referrer?.familyName
        || ''}${eRef?.referrer?.providerNumb
            ? ` (${eRef.referrer.providerNumb})` : ''}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-4 d-flex justify-content-end align-items-end">
                            <button class="btn btn-outline-primary-fill text-primary-text action-select-ereferral"
                                    type="button"
                                    data-ereferral-id="${eRef.id}">Select</button>
                        </div>
                    </div>
                </div>
            </td>
        `;
    }

    /**
     * Render the eReferrals table structure
     *
     * @param {boolean} hasEReferrals - Whether there are eReferrals to display
     * @returns {string} HTML string for the eReferrals table
     */
    renderEReferralsTable(hasEReferrals) {
        if (hasEReferrals) {
            return `
                <div id="ereferral-lookup-table-container" class="rounded-2 bg-white shadow-offset-medium overflow-hidden">
                    <table class="table custom-table mb-0">
                        ${this.renderTableHeader()}
                        <tbody></tbody>
                    </table>
                </div>
            `;
        } else {
            return `
                <div id="ereferral-lookup-table-container" class="rounded-2 bg-white shadow-offset-medium overflow-hidden">
                    <table class="table custom-table mb-0">
                        ${this.renderTableHeader()}
                        <tbody>
                            ${this.renderEmptyState('eReferral')}
                        </tbody>
                    </table>
                </div>
            `;
        }
    }

    /**
     * Initialize event listeners for interactive elements
     */
    initializeEventListeners() {
        const patientTableContainer = this.querySelector('#patient-lookup-table-container');
        if (patientTableContainer) {
            const collapseTriggers = patientTableContainer.querySelectorAll('.patient-row-toggle');

            collapseTriggers.forEach((triggerEl) => {
                const targetId = triggerEl.getAttribute('data-bs-target');
                if (targetId) {
                    const targetEl = this.querySelector(targetId);
                    const chevron = triggerEl.querySelector('.chevron-icon');

                    if (targetEl) {
                        targetEl.classList.add('d-none');
                    }

                    triggerEl.addEventListener('click', () => {
                        const isExpanded = triggerEl.getAttribute('aria-expanded') === 'true';

                        triggerEl.setAttribute('aria-expanded', !isExpanded);
                        if (chevron) {
                            chevron.src = isExpanded
                                ? '/images/icon/chevron-down-blue.svg'
                                : '/images/icon/chevron-up-blue.svg';
                        }

                        if (targetEl) {
                            targetEl.classList.toggle('d-none');
                        }
                    });
                }
            });
        }

        // Add click handlers for all return to search buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.action-return-to-search')) {
                e.preventDefault();
                window.location.href = '/patient/lookup';
            }
        });
    }

    /**
     * Initialize filter buttons for switching between patients and eReferrals views
     */
    initializeFilters() {
        const filterContainer = this.querySelector('#filter-container');
        if (!filterContainer) return;

        filterContainer.innerHTML = this.renderFilterButtons();

        const buttons = filterContainer.querySelectorAll('.filter-btn');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                // Reset all buttons
                buttons.forEach(btn => {
                    this.setButtonInactive(btn);
                });

                // Set the active button
                this.setButtonActive(button);

                // Apply the filter
                const filterType = button.getAttribute('data-filter-type');
                this.filterContent(filterType);
            });
        });
    }

    /**
     * @description Set a button to active state
     * @param {Element} button - The button element to activate
     */
    setButtonActive(button) {
        button.classList.add('active');
        button.classList.remove('btn-outline-primary-fill', 'text-primary-text');
        button.classList.add('btn-primary-fill', 'text-white');
    }

    /**
     * @description Set a button to inactive state
     * @param {Element} button - The button element to deactivate
     */
    setButtonInactive(button) {
        button.classList.remove('active');
        button.classList.remove('btn-primary-fill', 'text-white');
        button.classList.add('btn-outline-primary-fill', 'text-primary-text');
    }

    /**
     * @description Filter content based on the selected filter type
     * @param {string} filterType - The type of filter to apply ('patients' or 'ereferrals')
     */
    filterContent(filterType) {
        const patientTableContainer = this.querySelector('#patient-lookup-table-container');
        const eReferralsContainer = this.querySelector('#ereferrals-container');

        if (!patientTableContainer || !eReferralsContainer) return;

        // Initialize eReferrals container if needed
        if (eReferralsContainer.children.length === 0) {
            this.initializeEReferralsContainer(eReferralsContainer);
        }

        this.updateFilterCounts();

        // Add transition classes
        patientTableContainer.style.transition = 'opacity 0.3s ease-in-out';
        eReferralsContainer.style.transition = 'opacity 0.3s ease-in-out';

        // Show/hide appropriate container with fade effect
        if (filterType === 'ereferrals') {
            patientTableContainer.style.opacity = '0';
            setTimeout(() => {
                patientTableContainer.classList.add('d-none');
                eReferralsContainer.classList.remove('d-none');
                eReferralsContainer.style.opacity = '1';
            }, 300);
        } else {
            eReferralsContainer.style.opacity = '0';
            setTimeout(() => {
                eReferralsContainer.classList.add('d-none');
                patientTableContainer.classList.remove('d-none');
                patientTableContainer.style.opacity = '1';
            }, 300);
        }
    }

    /**
     * @description Initialize the eReferrals container
     * @param {Element} container - The container element to initialize
     */
    initializeEReferralsContainer(container) {
        const hasEReferrals = this.eReferrals && this.eReferrals.length > 0;
        container.innerHTML = this.renderEReferralsTable(hasEReferrals);

        if (hasEReferrals) {
            const tbody = container.querySelector('tbody');
            this.eReferrals.forEach((eRef, index) => {
                const [mainRow, detailRow] = this.createEReferralRow(eRef, index);
                tbody.appendChild(mainRow);
                tbody.appendChild(detailRow);
            });

            this.addEReferralClickHandlers();
        }
    }

    /**
     * @description Create an eReferral row and its details row
     * @param {Object} eRef - The eReferral data
     * @param {number} index - The index of the eReferral
     * @returns {Array} Array containing [mainRow, detailRow]
     */
    createEReferralRow(eRef, index) {
        const mainRow = document.createElement('tr');
        mainRow.className = 'border-top border-light ereferral-row-toggle';
        mainRow.setAttribute('data-bs-target', `#eReferralDetails-${index}`);
        mainRow.setAttribute('aria-expanded', 'false');
        mainRow.setAttribute('aria-controls', `eReferralDetails-${index}`);
        mainRow.style.cursor = 'pointer';
        mainRow.innerHTML = this.renderEReferralRow(eRef);

        const detailRow = document.createElement('tr');
        detailRow.id = `eReferralDetails-${index}`;
        detailRow.classList.add('d-none');
        detailRow.innerHTML = this.renderEReferralDetails(eRef);

        return [mainRow, detailRow];
    }

    /**
     * @description Add click event handlers for row expansion and selection
     * @param {Element} container - The container element
     * @param {string} rowSelector - Selector for toggle rows
     * @param {Function} selectHandler - Handler for select button clicks
     */
    addRowEventListeners(container, rowSelector, selectHandler) {
        if (!container) return;

        // Add expand/collapse functionality to rows
        container.querySelectorAll(rowSelector).forEach((triggerEl) => {
            const targetId = triggerEl.getAttribute('data-bs-target');
            if (!targetId) return;

            const targetEl = this.querySelector(targetId);
            if (!targetEl) return;

            const chevron = triggerEl.querySelector('.chevron-icon');

            // Check if it uses collapse class (patient rows) or d-none class (eReferral rows)
            const usesCollapseClass = targetEl.classList.contains('collapse');

            // Set initial state
            if (usesCollapseClass) {
                targetEl.style.display = 'none';
            } else {
                targetEl.classList.add('d-none');
            }

            triggerEl.addEventListener('click', () => {
                const isExpanded = triggerEl.getAttribute('aria-expanded') === 'true';
                const newExpandedState = !isExpanded;

                triggerEl.setAttribute('aria-expanded', newExpandedState);

                if (chevron) {
                    chevron.src = newExpandedState
                        ? '/images/icon/chevron-up-blue.svg'
                        : '/images/icon/chevron-down-blue.svg';
                }

                if (usesCollapseClass) {
                    // For patient rows using collapse class
                    targetEl.style.display = newExpandedState ? 'table-row' : 'none';
                } else {
                    // For eReferral rows using d-none class
                    targetEl.classList.toggle('d-none');
                }
            });
        });

        // Set up select button handlers if provided
        if (selectHandler && typeof selectHandler === 'function') {
            selectHandler(container);
        }
    }

    /**
     * @description Add click handlers to eReferral rows
     */
    addEReferralClickHandlers() {
        setTimeout(() => {
            const eReferralsContainer = this.querySelector('#ereferrals-container');

            this.addRowEventListeners(
                eReferralsContainer,
                '.ereferral-row-toggle',
                container => {
                    const selectButtons = container.querySelectorAll('.action-select-ereferral');
                    selectButtons.forEach(button => {
                        button.addEventListener('click', (e) => {
                            e.stopPropagation();
                            const eRefId = button.getAttribute('data-ereferral-id');
                            if (!eRefId) return;

                            const selectedERef = this.findEReferralById(eRefId);
                            if (selectedERef) {
                                try {
                                    sessionStorage.setItem('selectedEReferral', JSON.stringify(selectedERef));
                                    if (selectedERef.referredPatient) {
                                        sessionStorage.setItem('selectedPatient', JSON.stringify(selectedERef.referredPatient));
                                    }
                                } catch (err) {
                                    // Handle storage error silently
                                }
                            }

                            const healiusId = selectedERef?.healiusId;
                            if (healiusId) {
                                window.location.href = `/collection/ref/${encodeURIComponent(healiusId)}`;
                            }
                        });
                    });
                }
            );
        }, 100);
    }

    /**
     * Find an eReferral by its ID in the eReferrals array
     *
     * @param {string} eRefId - The ID of the eReferral to find
     * @returns {Object|null} The eReferral object if found, null otherwise
     */
    findEReferralById(eRefId) {
        if (!this.eReferrals || !Array.isArray(this.eReferrals)) {
            return null;
        }

        return this.eReferrals.find(eRef => eRef.id === eRefId);
    }

    /**
     * Update the count indicators in filter buttons
     */
    updateFilterCounts() {
        const eReferralsBtn = this.querySelector('button[data-filter-type="ereferrals"]');
        const patientsBtn = this.querySelector('button[data-filter-type="patients"]');

        if (eReferralsBtn) {
            const countSpan = eReferralsBtn.querySelector('span');
            if (countSpan) {
                const eRefCount = this.eReferrals ? this.eReferrals.length : 0;
                countSpan.textContent = `(${eRefCount})`;
            }
        }

        if (patientsBtn) {
            const countSpan = patientsBtn.querySelector('span');
            if (countSpan) {
                const patientCount = this.patients ? this.patients.length : 0;
                countSpan.textContent = `(${patientCount})`;
            }
        }
    }

    /**
     * Populate form fields from URL parameters
     */
    populateFormFieldsFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const paramToFieldMap = {
            'healiusId': 'digital-pass',
            'familyName': 'patientLastName',
            'givenName': 'patientFirstName',
            'dateOfBirth': 'patientDOB',
            'mobilePhone': 'patientMobile',
            'medicareNo': 'patientMedicare',
            'sex': 'patientSex'
        };
        for (const [param, fieldId] of Object.entries(paramToFieldMap)) {
            if (urlParams.has(param)) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.value = urlParams.get(param);
                }
            }
        }
    }

    /**
     * @description Add or update patient rows in the table based on the data
     */
    initializePatientTable() {
        const patientTableContainer = this.querySelector('#patient-lookup-table-container');
        if (!patientTableContainer) return;

        const tbody = patientTableContainer.querySelector('tbody');
        if (!tbody) return;

        if (!this.patients || this.patients.length === 0) {
            tbody.innerHTML = this.renderEmptyState('patient profile');
            return;
        }
        if (!this.patients || this.patients.length >= 20) {
            tbody.innerHTML = this.renderTooManyResults();
            return;
        }

        tbody.innerHTML = '';
        this.patients.forEach((patient, index) => {
            const [mainRow, detailRow] = this.createPatientRow(patient, index);
            tbody.appendChild(mainRow);
            tbody.appendChild(detailRow);
        });

        this.addPatientRowEventListeners();
    }

    /**
     * Create a patient row and its details row
     *
     * @param {Object} patient - The patient data
     * @param {number} index - The index of the patient
     * @returns {Array} Array containing [mainRow, detailRow]
     */
    createPatientRow(patient, index) {
        const mainRow = document.createElement('tr');
        mainRow.className = 'border-top border-light patient-row-toggle border-bottom';
        mainRow.setAttribute('data-bs-target', `#patientDetails-${index}`);
        mainRow.setAttribute('aria-expanded', 'false');
        mainRow.setAttribute('aria-controls', `patientDetails-${index}`);
        mainRow.innerHTML = `
            <td class="col-1 px-4 py-3 align-middle">
                <img src="/images/icon/profileType.svg" alt="Profile" />
            </td>
            <td class="col-2 px-4 py-3 body2 fw-500 align-middle">${patient.lastName || ''}</td>
            <td class="col-2 px-4 py-3 body2 align-middle">${patient.firstName || ''}</td>
            <td class="col-1 px-4 py-3 body2 align-middle">${patient.sex || ''}</td>
            <td class="col-2 px-4 py-3 body2 align-middle">${patient.dateOfBirth || ''}</td>
            <td class="col-3 px-4 py-3 body2 align-middle">${patient.medicareNumber || ''}</td>
            <td class="col-1 px-4 py-3 text-end align-middle">
                <img src="/images/icon/chevron-down-blue.svg" alt="Expand/Collapse" class="chevron-icon"/>
            </td>
        `;

        const detailRow = document.createElement('tr');
        detailRow.id = `patientDetails-${index}`;
        detailRow.classList.add('collapse');
        detailRow.innerHTML = `
            <td colspan="7" class="p-0">
                <div class="bg-neutral-fill p-4">
                    <div class="row">
                        <div class="col-12 mb-3 border-bottom">
                            <div class="m-0 body2 text-accent-blue-medium fw-4 pb-3">Patient Profile</div>
                        </div>
                        <div class="col-8">
                            <div class="row g-3">
                                <div class="col-12 d-flex align-items-center">
                                    <img src="/images/icon/id.svg" alt="ID Icon" class="me-2 icon-sm"/>
                                    <span class="body2 fw-500 text-neutral-text me-4 detail-label">Healius ID</span>
                                    <span class="body2 text-neutral-text">${patient.friendlyId || ''}</span>
                                </div>
                                <div class="col-12 d-flex align-items-center">
                                    <img src="/images/icon/mobile.svg" alt="Mobile Icon" class="me-2 icon-sm"/>
                                    <span class="body2 fw-500 text-neutral-text me-4 detail-label">Mobile</span>
                                    <span class="body2 text-neutral-text">${patient.mobilePhone || ''}</span>
                                </div>
                                <div class="col-12 d-flex align-items-center">
                                    <img src="/images/icon/home.svg" alt="Address Icon" class="me-2 icon-sm"/>
                                    <span class="body2 fw-500 text-neutral-text me-4 detail-label">Address</span>
                                    <span class="body2 text-neutral-text">${this.formatAddress(patient)}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-4 d-flex align-items-end justify-content-end">
                            <button class="btn btn-sm btn-outline-primary-fill text-primary-text action-select-patient"
                                    type="button"
                                    data-patient-id="${patient.friendlyId}">Select</button>
                        </div>
                    </div>
                </div>
            </td>
        `;
        return [mainRow, detailRow];
    }

    /**
     * @description Add event listeners to patient rows
     */
    addPatientRowEventListeners() {
        const patientTableContainer = this.querySelector('#patient-lookup-table-container');

        this.addRowEventListeners(
            patientTableContainer,
            '.patient-row-toggle',
            container => {
                const selectButtons = container.querySelectorAll('.action-select-patient');
                selectButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const patientId = button.getAttribute('data-patient-id');
                        if (!patientId) return;

                        const selectedPatient = this.findPatientById(patientId);
                        if (selectedPatient) {
                            try {
                                sessionStorage.setItem('selectedPatient', JSON.stringify(selectedPatient));
                            } catch (err) {
                                // Handle storage error silently
                            }
                        }

                        window.location.href = `/patient/search/${encodeURIComponent(patientId)}`;
                    });
                });
            }
        );
    }

    /**
     * Find a patient by their ID in the patients array
     *
     * @param {string} patientId - The ID of the patient to find
     * @returns {Object|null} The patient object if found, null otherwise
     */
    findPatientById(patientId) {
        if (!this.patients || !Array.isArray(this.patients)) {
            return null;
        }

        return this.patients.find(patient =>
            patient.friendlyId === patientId ||
            patient.id === patientId);
    }

    /**
     * Format a patient's address based on available fields
     * Follows the same formatting logic as the Thymeleaf fragment
     *
     * @param {Object} patient - The patient object
     * @returns {string} Formatted address or default message
     */
    formatAddress(patient) {
        if (!patient) return '';
        
        let formattedAddress = '';
        
        // Check if each field exists
        const hasStreet = patient.streetAddress && patient.streetAddress.trim() !== '';
        const hasStreet2 = patient.streetAddress2 && patient.streetAddress2.trim() !== '';
        const hasCity = patient.city && patient.city.trim() !== '';
        const hasState = patient.state && patient.state.trim() !== '';
        const hasPostCode = patient.postCode && patient.postCode.trim() !== '';
        const hasCountry = patient.country && patient.country.trim() !== '' && 
                          patient.country !== 'AU' && patient.country !== 'Australia';
        
        // Street Address
        if (hasStreet) {
            formattedAddress += patient.streetAddress;
        }
        
        // Street Address 2 with comma if needed
        if (hasStreet && hasStreet2) {
            formattedAddress += ', ';
        }
        if (hasStreet2) {
            formattedAddress += patient.streetAddress2;
        }
        
        // City with comma if there's a street address
        if ((hasStreet || hasStreet2) && hasCity) {
            formattedAddress += ', ';
        }
        if (hasCity) {
            formattedAddress += patient.city;
        }
        
        // State with space after city or comma if no city
        if (hasCity && hasState) {
            formattedAddress += ' ';
        } else if (!hasCity && (hasStreet || hasStreet2) && hasState) {
            formattedAddress += ', ';
        }
        if (hasState) {
            formattedAddress += patient.state;
        }
        
        // Post Code with space after state
        if (hasState && hasPostCode) {
            formattedAddress += ' ';
        } else if (!hasState && (hasCity || hasStreet || hasStreet2) && hasPostCode) {
            formattedAddress += ', ';
        }
        if (hasPostCode) {
            formattedAddress += patient.postCode;
        }
        
        // Country with comma if there's any address part before it
        if ((hasStreet || hasStreet2 || hasCity || hasState || hasPostCode) && hasCountry) {
            formattedAddress += ', ';
        }
        if (hasCountry) {
            formattedAddress += patient.country;
        }
        
        return formattedAddress;
    }

    showLoadingState() {
        const container = this.querySelector('#results-container');
        if (container) {
            container.classList.remove('rendered');
            const loadingOverlay = container.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        }
    }

    hideLoadingState() {
        const container = this.querySelector('#results-container');
        if (container) {
            const loadingOverlay = container.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
            container.classList.add('rendered');
        }
    }

    renderPlaceholderRow() {
        return `
            <tr class="placeholder-glow">
                <td class="col-1 px-4 py-3 align-middle">
                    <span class="placeholder col-12 rounded-circle" style="width: 24px; height: 24px;"></span>
                </td>
                <td class="col-2 px-4 py-3 align-middle">
                    <span class="placeholder col-12"></span>
                </td>
                <td class="col-2 px-4 py-3 align-middle">
                    <span class="placeholder col-12"></span>
                </td>
                <td class="col-1 px-4 py-3 align-middle">
                    <span class="placeholder col-12"></span>
                </td>
                <td class="col-2 px-4 py-3 align-middle">
                    <span class="placeholder col-12"></span>
                </td>
                <td class="col-3 px-4 py-3 align-middle">
                    <span class="placeholder col-12"></span>
                </td>
                <td class="col-1 px-4 py-3 align-middle">
                    <span class="placeholder col-12 rounded-circle" style="width: 24px; height: 24px;"></span>
                </td>
            </tr>
        `;
    }

    renderPlaceholderSearchCriteria() {
        return `
            <div class="col-12 mb-4">
                <div class="d-flex align-items-center bg-white shadow-offset-medium rounded-2 py-3 px-5">
                    <div class="d-flex align-items-center flex-fill">
                        <div class="description text-neutral-text">
                            <span class="placeholder col-6"></span>
                        </div>
                    </div>
                    <span class="placeholder col-2"></span>
                </div>
            </div>
        `;
    }

    renderPlaceholderFilters() {
        return `
            <div class="body1 text-primary-text">Search Results</div>
            <div class="d-flex input-group py-3">
                <span class="placeholder col-3 me-2"></span>
                <span class="placeholder col-3"></span>
            </div>
        `;
    }

    updateSearchCriteria() {
        const container = this.querySelector('#search-criteria-container');
        if (container) {
            container.innerHTML = this.renderSearchCriteria();
        }
    }

    initializeCreateProfileSection() {
        const container = this.querySelector('#create-profile-container');
        if (!container) return;

        // Always show create profile section
        container.innerHTML = this.patients.length < 20 ? this.renderCreateProfileSection() : '';
        container.classList.remove('d-none');
        this.initializeCreateButton();
    }

    renderCreateProfileSection() {
        return `
            <div class="d-flex flex-column align-items-center" style="padding-top: 100px">
                <div class="d-flex flex-column align-items-center text-center">
                    <h5 class="fw-600 text-neutral-text">
                        Didn't find a matching patient profile?
                    </h5>
                    <p class="body1 text-neutral-text">
                        Please create a new patient profile
                    </p>
                    <button class="btn btn-primary-fill d-flex align-items-center gap-1"
                            id="createPatientProfileBtn"
                    >
                        <img src="/images/icon/plusWhite.svg" alt="Add"/>
                        <span>Create new patient profile</span>
                    </button>
                </div>
            </div>
        `;
    }

    renderTooManyResults() {
        return `
            <tr>
                <td colspan="7" class="p-0">
                    <div class="d-flex flex-column justify-content-center align-items-center w-100"
                         style="padding-top: 96px; padding-bottom: 96px">
                        <div class="d-flex flex-column align-items-center text-center w-100">
                            <h6 class="fw-600 text-neutral-text mb-2">
                                Too many search results found
                            </h6>
                            <p class="body2 text-neutral-text mb-0">
                                Please narrow the search criteria
                            </p>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    initializeCreateButton() {
        // Show the modal when the create profile button is clicked
        const openModalBtn = this.querySelector('#createPatientProfileBtn');
        if (openModalBtn) {
            openModalBtn.addEventListener('click', function(event) {
                event.preventDefault();
                const modalEl = document.getElementById('addPatientProfileModal');
                if (modalEl) {
                    // Get search data from localStorage
                    const searchData = JSON.parse(localStorage.getItem('patientSearchData') || '{}');
                    
                    // Pre-fill form fields
                    const form = modalEl.querySelector('#patientForm');
                    if (form) {
                        // Map search fields to form fields
                        const fieldMapping = {
                            'familyName': 'familyName',
                            'givenName': 'givenName',
                            'dateOfBirth': 'dateOfBirth',
                            'mobilePhone': 'mobilePhone',
                            'medicareNo': 'medicareNo',
                            'sex': 'sex'
                        };

                        // Set values and trigger change events
                        Object.entries(fieldMapping).forEach(([searchField, formField]) => {
                            const input = form.querySelector(`[name="${formField}"]`);
                            if (input && searchData[searchField]) {
                                input.value = searchData[searchField];
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                        });

                        // Initialize phone input if mobile number exists
                        const phoneInput = form.querySelector('#mobilePhone');
                        if (phoneInput && searchData.mobilePhone) {
                            const iti = window.intlTelInputGlobals.getInstance(document.querySelector('#mobilePhone'))
                            iti.setNumber(searchData.mobilePhone.startsWith('+') ? searchData.mobilePhone : `+${searchData.mobilePhone}`);
                        }
                    }

                    const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
                    modal.show();
                }
            });
        }

        const createButton = document.getElementById('addPatientProfileModal')?.querySelector('.modal-action');
        if (!createButton) return;

        createButton.addEventListener('click', async function(event) {
            event.preventDefault();
            const patientForm = document.getElementById('patientForm');
            if (!patientForm) return;

            // Validate using the validation service
            try {
                const status = await validationService.validators.patientForm.validate();
                console.log('Validation status:', status);
                
                if (status !== 'Valid') {
                    // Find the first invalid field and focus it
                    const firstInvalid = patientForm.querySelector('.is-invalid');
                    if (firstInvalid) {
                        firstInvalid.focus();
                        // Scroll to the error
                        firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                    return;
                }

                // Collect form data
                const formData = new FormData(patientForm);
                const formValues = {};
                for (let [key, value] of formData.entries()) {
                    formValues[key] = value;
                }

                // Get the requested date from the form
                const requestedDate = formValues.requestedDate || dayjs().format('YYYY-MM-DD');
                delete formValues.requestedDate; // Remove from body since it's in URL

                formValues.mobilePhone = window.intlTelInputGlobals.getInstance(document.querySelector('#mobilePhone')).getNumber().replace('+', '');

                // POST to create order with requestedDate in URL
                const response = await fetch(`/api/v1/order?requestedDate=${encodeURIComponent(requestedDate)}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': healiusAPI.getCsrfToken()
                    },
                    body: JSON.stringify(formValues)
                });

                if (!response.ok) {
                    throw new Error('Failed to create order');
                }

                const healiusId = await response.text();

                // Store the created patient data in session storage
                try {
                    sessionStorage.setItem('selectedPatient', JSON.stringify(formValues));
                } catch (err) {
                    console.warn('Failed to store patient data in session storage:', err);
                }

                // Close modal and redirect to collection page
                bootstrap.Modal.getInstance(document.getElementById('addPatientProfileModal')).hide();
                window.location.href = `/collection/ref/${encodeURIComponent(healiusId)}`;
            } catch (error) {
                console.error('Error:', error);
                alert('Error creating order: ' + error.message);
            }
        });

        // Set up modal event listeners
        const modalEl = document.getElementById('addPatientProfileModal');
        if (modalEl) {
            modalEl.addEventListener('hidden.bs.modal', () => {
                // Reset the form without destroying the phone input
                const form = document.getElementById('patientForm');
                if (form) {
                    form.reset();
                }
                
                // Just clear the phone input value instead of destroying it
                const phoneInput = document.getElementById('mobilePhone');
                if (phoneInput) {
                    phoneInput.value = '';
                    phoneInput.classList.remove('is-valid', 'is-invalid');
                }
            });

            // Initialize validation only once when the page loads, not every time the modal is shown
            validationService.setupPatientProfileValidation();
        }
    }

    initPlacesAutoComplete() {
        const patientAUOptions = {
            'additionalEvent': function() { collectorPortalPatient.hideAddressAutocomplete("pac") },
            'resultsRestrictions': 'aus',
            'dataAddressElements': 'mapAddressAUInput,apartmentInput,patientSuburbInput,patientStateInput,patientPostcodeInput'
        };

        const patientOSOptions = {
            'resultsRestrictions': '',
            'dataAddressElements': 'mapAddressOverseas'
        };

        this.collectorPortalPlacesAutocompleteConfig = {
            'patientAU': patientAUOptions,
            'patientOS': patientOSOptions,
        };

        this.collectorPortalPlacesAutocomplete = [];

        this.collectorPortalPlacesAutocomplete.autocompleteService = [];
        this.collectorPortalPlacesAutocomplete.placesService = [];
        this.collectorPortalPlacesAutocomplete.autocompletes = [];

        const placeInputs = document.querySelectorAll('input.places-autocomplete');
        placeInputs.forEach(elem => {
            const elemName = elem.getAttribute('id');
            this.collectorPortalPlacesAutocomplete.autocompletes[elemName] = new PlacesAutocomplete(elemName, this);
        });
    }
}

// Initialize the custom patient profile component and register it
customElements.define('patient-results', PatientResultsComponent);

/**
 * @description Initialize patient results component if not already present
 */
function initializePatientResults() {
    if (document.querySelector('patient-results')) return;

    const resultsContainer = document.querySelector('#results-container');
    if (resultsContainer) {
        const patientResults = document.createElement('patient-results');
        resultsContainer.parentNode.replaceChild(patientResults, resultsContainer);
    } else {
        document.body.appendChild(document.createElement('patient-results'));
    }
}

// Initialize everything when the document is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Allow a small delay to ensure other scripts have initialized
    setTimeout(initializePatientResults, 100);
});