const squareSetupDelay = 5000;
let collectorPortalViewPayment = {}

collectorPortalViewPayment.payments = null;
collectorPortalViewPayment.card = null;
collectorPortalViewPayment.fv = null;
collectorPortalViewPayment.fvAmount = null;
collectorPortalViewPayment.paymentInProgressModal = null;
collectorPortalViewPayment.checkStatusInterval = null;
collectorPortalViewPayment.processSMSLinkStatusModal = null;
collectorPortalViewPayment.paymentModal = null;
collectorPortalViewPayment.squareLocationId = null;
collectorPortalViewPayment.terminalEnabled = null;
collectorPortalViewPayment.tryAgainMethod = undefined;
collectorPortalViewPayment.isInitialized = false;

collectorPortalViewPayment.setupPayment = async function() {
    if (collectorPortalViewPayment.isInitialized) {
        return;
    }

    try {
        // Load Square.js only when needed
        await loadSquareJS();
        
        await collectorPortalViewPayment.startSquare();
        collectorPortalViewPayment.startTerminalSquare();
        collectorPortalViewPayment.setupSMS();
        
        collectorPortalViewPayment.isInitialized = true;
    } catch (error) {
        console.error('Error setting up payment:', error);
        throw error; // Re-throw to handle in the calling function
    }
}

collectorPortalViewPayment.getToken = async (card) => {
    const tokenResult = await card.tokenize();
    if (tokenResult.status === 'OK') {
        return tokenResult.token;
    }

    // Failed

    console.log(tokenResult);
    const statusContainer = document.getElementById(
        'payment-status-container',
    );
    statusContainer.innerHTML = "Payment Failure";
    statusContainer.classList.remove('is-success');
    statusContainer.classList.add('is-failure');
}

collectorPortalViewPayment.startSquare = async function () {
    console.log('Starting Square initialization');
    if (!window.Square) {
        throw new Error('Square.js failed to load properly');
    }

    collectorPortalViewPayment.paymentInProgressModal = new bootstrap.Modal(document.getElementById('payments-progress-modal'), {
        keyboard: false,
        backdrop: 'static'
    });

    let payments;
    try {
        payments = window.Square.payments(squareAppId, collectorPortalViewPayment.squareLocationId);
    } catch (error) {
        console.error('Failed to initialize Square payments:', error);
        const statusContainer = document.getElementById('payment-status-container');
        statusContainer.className = 'missing-credentials';
        statusContainer.style.visibility = 'visible';
        throw new Error('Failed to initialize Square payments');
    }

    let card;
    try {
        card = await collectorPortalViewPayment.initializeCard(payments);
    } catch (error) {
        console.error('Initializing Card failed:', error);
        throw new Error('Failed to initialize card payment');
    }

    function isPaymentComplete(orderPayment) {
        return orderPayment.paymentState == "COMPLETED";
    }

    async function handlePaymentMethodSubmission(event, card) {
        event.preventDefault();

        cardButton.disabled = true;

        try {
            const token = await collectorPortalViewPayment.getToken(card);
            if (token) {
                const orderPayment = await collectorPortalViewPayment.createPayment(token);

                console.debug('Payment completed', orderPayment);
                collectorPortalViewPayment.paymentModal.hide();
                if (isPaymentComplete(orderPayment)) {
                    await collectorPortalViewPayment.completePayment(orderPayment);
                    collectorPortalViewPayment.processStatus('SUCCESS', '', undefined);
                } else {
                    collectorPortalViewPayment.processStatus('FAILED', '', undefined);
                }
            }
        } catch (e) {
            console.log(e);
            collectorPortalViewPayment.paymentModal.hide();
            collectorPortalViewPayment.processStatus('FAILED', e.message, undefined);
        }
        cardButton.disabled = false;
    }

    const cardButton = document.getElementById('card-button');
    cardButton.addEventListener('click', async function (event) {
        await handlePaymentMethodSubmission(event, card);
    });
}

collectorPortalViewPayment.startTerminalSquare = function () {
    console.log('start square terminal');
    const sendPaymentTerminal = document.getElementById('sendPaymentTerminal')

    sendPaymentTerminal.addEventListener('click', collectorPortalViewPayment.terminalPayment);
}

collectorPortalViewPayment.setupSMS = function (){
    collectorPortalViewPayment.fvInit()
    const sendPaymentLink = document.getElementById('sendPaymentLink')

    sendPaymentLink.addEventListener('click', async () => {
        const valid = await validationService.ifValid(collectorPortalViewPayment.fv, collectorPortalViewPayment.sendSMS);
        if (valid) healiusPortal.modals['payment-modal'].hide();
    });
}

collectorPortalViewPayment.sendSMS = () => {
    document.getElementById("sendPaymentLink").disabled = true;
    collectorPortalViewPayment.paymentInProgressModal.show();
    collectorPortalViewPayment.paymentModal.hide();
    collectorPortalViewPayment.api.smsPayment(orderView.healiusId,
        document.getElementById("patientPhoneNumber").value,
        document.getElementById("paymentAmount").value)
        .then(response => {
            console.log(response);
            if (response?.ok) {
                collectorPortalViewPayment.checkStatusInterval = setInterval(collectorPortalViewPayment.checkSMSLinkStatus, 3000);
            } else {
                collectorPortalViewPayment.cancelPayment();
                collectorPortalViewPayment.paymentInProgressModal.hide()
                collectorPortalViewPayment.processStatus('FAILED', '', collectorPortalViewPayment.sendSMS);
            }
        })
        .catch(() => {
            collectorPortalViewPayment.cancelPayment();
            collectorPortalViewPayment.paymentInProgressModal.hide()
            collectorPortalViewPayment.processStatus('FAILED', '', collectorPortalViewPayment.sendSMS)
        });

}

collectorPortalViewPayment.initializeCard = async function (payments){
    const card = await payments.card();
    await card.attach('#card-container');

    return card;
}

collectorPortalViewPayment.createPayment = async function (token) {
    let formData = new FormData();

    formData.set("sourceId", token)


    const paymentResponse = await fetch(`/api/v1/payment/${orderView.healiusId}/form`, {
        method: 'POST',
        body: formData,
        headers: {"X-CSRF-TOKEN": healiusAPI.getCsrfToken()},
    });

    if (paymentResponse.ok) {
        return paymentResponse.json();
    }

    const errorBody = await paymentResponse.text();
    throw new Error(errorBody);
}

collectorPortalViewPayment.processStatus = function (status, message, tryAgain) {
    console.log('processStatus', status);
    collectorPortalViewPayment.tryAgainMethod = tryAgain;

    const modalEl = document.getElementById('payments-status-modal');
    collectorPortalViewPayment.updateTryAgainButton(modalEl, tryAgain);
    modalEl.querySelectorAll(`.status`).forEach(el =>
        el.classList.add("d-none"));
    modalEl.querySelectorAll(`.status-${status}`).forEach(el =>
        el.classList.remove("d-none"));
    modalEl.querySelector('.status-message').innerText = message;
    healiusPortal.modals['payments-status-modal'].show()
}

collectorPortalViewPayment.showModalMoney = async function() {
    try {
        fetch(`/api/v1/payment/location`, {
            headers: {"X-CSRF-TOKEN": healiusAPI.getCsrfToken()}
        })
        .then(response => response.json())
        .then(content => {
            collectorPortalViewPayment.squareLocationId = content.locationId;
            collectorPortalViewPayment.terminalEnabled = content.terminalEnabled;

            let ausDollar = new Intl.NumberFormat('en-AU', {style: 'currency', currency: 'AUD'});
            document.getElementById("paymentAmountModal").innerText = ausDollar.format(content.totalCharge);
            collectorPortalViewPayment.closeAllPayment();
            collectorPortalViewPayment.paymentModal = new bootstrap.Modal(document.getElementById('payment-modal'), {
                keyboard: false,
                backdrop: 'static'
            });
            collectorPortalViewPayment.paymentModal.show();

            const radioButtons = document.querySelectorAll('input[name="paymentOptions"]');
            for(const radioButton of radioButtons) {
                radioButton.addEventListener('change', collectorPortalViewPayment.togglePaymentOptions);
            }

            if (collectorPortalViewPayment.terminalEnabled) {
                document.getElementById("terminal").checked = true;
                collectorPortalViewPayment.showTerminal();
            } else {
                document.getElementById("link").checked = true;
                collectorPortalViewPayment.showSMS();
            }
        });
    } catch (error) {
        console.error('Failed to show payment modal:', error);
        collectorPortalViewPayment.processStatus('FAILED', 'Failed to show payment modal. Please try again.', collectorPortalViewPayment.showModalMoney);
    }
}

collectorPortalViewPayment.showModalMoneyCommercial = async function() {
    try {
        // This is to allow the change of payment, needs to be better
        let commercialPrice = Math.round(document.getElementById('paymentAmountCommercial').value * 100);
        document.getElementById('paymentAmount').value = commercialPrice;

        await collectorPortal.api.commercialTotal(commercialPrice);
        await collectorPortalViewPayment.showModalMoney();
    } catch (error) {
        console.error('Failed to show commercial payment modal:', error);
        collectorPortalViewPayment.processStatus('FAILED', 'Failed to show payment modal. Please try again.', collectorPortalViewPayment.showModalMoneyCommercial);
    }
}

collectorPortalViewPayment.togglePaymentOptions = function (){
    collectorPortalViewPayment.closeAllPayment();
    if (this.checked) {
        if (this.value === "terminal"){
            collectorPortalViewPayment.showTerminal();
        } else if (this.value === "link"){
            collectorPortalViewPayment.showSMS();
        } else if (this.value === "form"){
            document.getElementById("paymentForm").classList.remove("d-none");
        }
    }
}

collectorPortalViewPayment.showTerminal = function (){
    document.getElementById("paymentTerminal").classList.remove("d-none");
}

collectorPortalViewPayment.showSMS = function (){
    document.getElementById("paymentSMS").classList.remove("d-none");
}

collectorPortalViewPayment.closeAllPayment = function (){
    document.getElementById("paymentTerminal").classList.add("d-none");
    document.getElementById("paymentSMS").classList.add("d-none");
    document.getElementById("paymentForm").classList.add("d-none");
    document.getElementById("sendPaymentLink").disabled = false;
}

collectorPortalViewPayment.fvInit = function () {
    collectorPortalViewPayment.fv = FormValidation.formValidation(
        document.getElementById('smsForm'),
        {
            fields: {
                patientPhoneNumber: {
                    validators: {
                        notEmpty: {
                            message: 'Please enter phone number'
                        }
                    }
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
            },
        }
    )
}

collectorPortalViewPayment.checkSMSLinkStatus = async () => {
    try {
        const response = await collectorPortalViewPayment.api.smsStatus(orderView.healiusId);
        if (response.ok) {
            const orderPayment = await response.json();
            if (orderPayment.paymentTaken) {
                await collectorPortalViewPayment.completePayment(orderPayment);
            } else if (!orderPayment.paymentProvider) {
                clearInterval(collectorPortalViewPayment.checkStatusInterval);
                collectorPortalViewPayment.paymentInProgressModal.hide()
            }
        } else {
            console.log(response);
        }
    } catch (error) {
        console.log(error);
    }
}

collectorPortalViewPayment.terminalPayment = async () => {
    document.getElementById("sendPaymentTerminal").disabled = true;
    const terminalSelectedId = document.getElementById("terminalSelect").value;
    let terminalSelectedText;
    let a = document.getElementById("terminalSelect");
    for (let i = 0; i < a.length; i++) {
        let option = a.options[i];
        if (option.value === terminalSelectedId) {
            terminalSelectedText = option.text;
        }
    }

    const response = await collectorPortalViewPayment.api.terminalPayment(
        orderView.healiusId,
        document.getElementById("paymentAmount").value,
        terminalSelectedId
    );
    if (response.ok) {
        document.getElementById("paymentProcessingContainer").innerText = "Payment Processing on " + terminalSelectedText
        collectorPortalViewPayment.paymentInProgressModal.show()
        collectorPortalViewPayment.checkStatusInterval = setInterval(collectorPortalViewPayment.checkTerminalStatus, 3000);
    } else {
        document.getElementById("paymentProcessingContainer").innerText = "Payment Processing"
        collectorPortalViewPayment.cancelPayment();
        collectorPortalViewPayment.terminalFailed('FAILED', await response.text());
    }
}

collectorPortalViewPayment.checkTerminalStatus = async () => {

    const response = await collectorPortalViewPayment.api.terminalStatus(orderView.healiusId);
    if (response.ok) {
        const orderPayment = await response.json();
        if (orderPayment.paymentTaken) {
            await collectorPortalViewPayment.completePayment(orderPayment);
        } else if (orderPayment.paymentMethod === null) {
            clearInterval(collectorPortalViewPayment.checkStatusInterval);
            collectorPortalViewPayment.terminalFailed('CANCELED', '');
        }
        document.getElementById("sendPaymentTerminal").disabled = false;
    } else {
        console.log(response.text);
    }
}

collectorPortalViewPayment.cancelPayment = () => {
    collectorPortalViewPayment.api.cancelPayment(orderView.healiusId);
}

collectorPortalViewPayment.terminalFailed = (status, message) => {
    document.getElementById("sendPaymentTerminal").disabled = false;
    collectorPortalViewPayment.paymentInProgressModal.hide();
    collectorPortalViewPayment.processStatus(status, message, collectorPortalViewPayment.terminalPayment);
}

collectorPortalViewPayment.completePayment = async (orderPayment) => {
    console.log('completePayment');
    clearInterval(collectorPortalViewPayment.checkStatusInterval);
    collectorPortalViewPayment.paymentInProgressModal.hide();

    document.getElementById("paymentEdit").classList.remove("d-none");
    const commercialEl = document.getElementById('commercialSection');
    if (commercialEl) {
        commercialEl.querySelector('div.stage-control').classList.remove('d-none');
    }

    collectorPortalViewPayment.togglePaymentEdit();
    let receiptNumber = orderPayment.receiptNumber;
    if (orderPayment.paymentMethod === 'TERMINAL'){
        document.getElementById("squareTerminalName").innerText =  " (" + orderPayment.squareTerminalName +")";
    } else {
        let method = orderPayment.paymentMethod;
        if (method === 'SMS'){
            method = "Link"
        }

        
    }

    collectorPortalViewPayment.processStatus("SUCCESS", '', undefined);
    await collectorPortalViewPayment.paymentCompleted(orderPayment);
}

collectorPortalViewPayment.paymentCompleted = async function (orderPayment) {
    collectorPortalViewPayment.toggleCollectionWarning();
    collectorPortalViewPayment.canCompleteStage('collectionSection', {
        paymentTaken: orderPayment.paymentAmount > 0,
        receiptNumber: orderPayment.receiptNumber || '',
    });

     if (orderPayment.paymentAmount > 0) {
        await collectorPortalViewPayment.updatePaymentReceipt(orderPayment, true);
        collectorPortalViewPayment.lock();

        const collectionSectionEl = document.getElementById('collectionSection');
        const commercialSectionEl = document.getElementById('commercialSection');
        if (collectionSectionEl) {
            collectionSectionEl.dispatchEvent(new CustomEvent("specimens-locked", {}));
        }
        if (commercialSectionEl) {
            commercialSectionEl.dispatchEvent(new CustomEvent("specimens-locked", {}));
        }
     }
};

collectorPortalViewPayment.updatePaymentReceipt = async function (orderPayment, paymentTaken) {
    collectorPortalViewPayment.paymentTaken = paymentTaken;
    const ausDollar = new Intl.NumberFormat('en-AU', {style: 'currency', currency: 'AUD',});
    document.querySelectorAll("#receiptNumber").forEach(i => {
        i.innerHTML = `${orderPayment.receiptNumber} (${orderPayment.paymentMethod === 'TERMINAL' ?  orderPayment.squareTerminalName : orderPayment.paymentMethod.toUpperCase()})`;
     })

     document.querySelectorAll("#paymentReceiptPaymentAmount").forEach(i => {
        i.innerHTML = ausDollar.format(orderPayment.paymentAmount / 100.00);
     })

    if(paymentTaken) {
        document.getElementById('cancel-button').classList.add('d-none');
    }
}

collectorPortalViewPayment.lock = () => {
    console.log('abc123 - lock collection');
    const commercialOptionsEl = document.getElementById('commercialPaymentOptions');
    if (commercialOptionsEl) {
        commercialOptionsEl.querySelectorAll('input')
            .forEach(input => input.disabled = true);
    }
    collectorPortalViewPayment.toggleSelectTests();
}

collectorPortalViewPayment.canCompleteStage = (stageId, state = {}, completeAction, addClass, removeClass) => {
    const stageElement = document.getElementById(stageId);
    if (!stageElement) {
        console.error(`Stage element with ID "${stageId}" not found.`);
        return;
    }

    stageElement.dispatchEvent(new CustomEvent("stage-can-complete", {
        detail: {
            state: state,
            action: completeAction || null,
            addClass: addClass || [],
            removeClass: removeClass || [],
        },
    }));
};

collectorPortalViewPayment.api = {
    terminalPayment: (healiusId, paymentAmount, terminal) => {
        const formData = new FormData()
        formData.set("price", paymentAmount);
        formData.set("terminal", terminal);

        return fetch(`/api/v1/payment/${healiusId}/terminal`, {
            method: 'POST',
            body: formData,
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        });
    },

    terminalStatus: (healiusId) =>
        fetch(`/api/v1/payment/${healiusId}/terminal`,{
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

    smsPayment: (healiusId, phoneNumber, paymentAmount) => {
        const formData = new FormData()
        formData.set("price", paymentAmount);
        formData.set("number", phoneNumber);
        return fetch(`/api/v1/payment/${healiusId}/sms`, {
            method: 'POST',
            body: formData,
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        })
    },

    smsStatus: (healiusId) =>
        fetch(`/api/v1/payment/${healiusId}/sms`,{
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

    bPointPayment: (healiusId, commercialType, bPointReceiptNo, bPointAmountPaid) => {
        const formData = new FormData();
        formData.append('commercialPaymentType', commercialType);
        if (bPointAmountPaid != null) {
            formData.append('bPointReceiptNo', bPointReceiptNo);
            formData.append('bPointAmountPaid', bPointAmountPaid);
        }

        return fetch(`/api/v1/payment/${healiusId}/bpoint`, {
            method: 'POST',
            body: formData,
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }).then(response => response.json());
    },

    commercialPaymentType: (healiusId, commercialType) => {
        const formData = new FormData();
        formData.append('commercialPaymentType', commercialType);

        return fetch(`/api/v1/payment/${healiusId}/commercialpaymenttype`, {
            method: 'POST',
            body: formData,
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }).then(response => response.json());
    },

    cancelPayment: (healiusId) =>
        fetch(`/api/v1/payment/${healiusId}`,{
            method: 'DELETE',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

    cancelTerminalPayment: (healiusId) =>
        fetch(`/api/v1/payment/${healiusId}/terminal`,{
            method: 'DELETE',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

    cancelSMSPayment: (healiusId) =>
        fetch(`/api/v1/payment/${healiusId}/sms`,{
            method: 'DELETE',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

}

collectorPortalViewPayment.initMoneyButton = function() {
    // Add event listeners for all payment-related buttons
    const paymentButtons = [
        'commercialTakePayment',
        'cancelTransactionButton',
        'payments-status-modal .action-try-again',
        'payment-taken-modal-button'
    ];

    paymentButtons.forEach(buttonId => {
        const element = document.getElementById(buttonId) || document.querySelector(buttonId);
        if (element) {
            if (buttonId === 'commercialTakePayment') {
                element.addEventListener('click', async () => {
                    try {
                        // Initialize payment system if not already initialized
                        if (!collectorPortalViewPayment.isInitialized) {
                            await collectorPortalViewPayment.setupPayment();
                        }
                        await collectorPortalViewPayment.showModalMoneyCommercial();
                    } catch (error) {
                        console.error('Failed to handle commercial payment:', error);
                        collectorPortalViewPayment.processStatus('FAILED', 'Failed to initialize payment system. Please try again.', () => {
                            collectorPortalViewPayment.showModalMoneyCommercial();
                        });
                    }
                });
            } else if (buttonId === 'payment-taken-modal-button') {
                element.addEventListener('click', async () => {
                    try {
                        // Initialize payment system if not already initialized
                        if (!collectorPortalViewPayment.isInitialized) {
                            await collectorPortalViewPayment.setupPayment();
                        }
                        await collectorPortalViewPayment.showModalMoney();
                    } catch (error) {
                        console.error('Failed to handle commercial payment:', error);
                        collectorPortalViewPayment.processStatus('FAILED', 'Failed to initialize payment system. Please try again.', () => {
                            collectorPortalViewPayment.showModalMoney();
                        });
                    }
                });
            } else if (buttonId === 'cancelTransactionButton') {
                element.addEventListener('click', collectorPortalViewPayment.cancelPayment);
            } else if (buttonId === 'payments-status-modal .action-try-again') {
                element.addEventListener('click', async () => {
                    try {
                        // Initialize payment system if not already initialized
                        if (!collectorPortalViewPayment.isInitialized) {
                            await collectorPortalViewPayment.setupPayment();
                        }
                        collectorPortalViewPayment.tryAgainMethod();
                    } catch (error) {
                        console.error('Failed to retry payment:', error);
                        collectorPortalViewPayment.processStatus('FAILED', 'Failed to initialize payment system. Please try again.', () => {
                            collectorPortalViewPayment.tryAgainMethod();
                        });
                    }
                });
            }
        }
    });
}

collectorPortalViewPayment.updateTryAgainButton = function(modalEl, tryAgain) {
    collectorPortalDOM.displayIf(modalEl.querySelector('.action-try-again'), tryAgain !== undefined);
}

collectorPortalViewPayment.togglePaymentEdit = function() {
    collectorPortalDOM.toggle('#paymentEdit, .commercial-payment-PRE_PAYMENT_REQUIRED', '#paymentPayed, #additionalCollection');
}

collectorPortalViewPayment.toggleCollectionWarning = function() {
    collectorPortalDOM.toggle('#collection-warning-banner', '#additionalCollection');
}

collectorPortalViewPayment.toggleSelectTests = function() {
    collectorPortalDOM.toggle('#selectTests, #protocolPanel', "#additionalCollection");
}
