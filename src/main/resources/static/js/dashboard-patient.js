const collectorPortalPatient = {};

collectorPortalPatient.init =  (patient) => {
    collectorPortalPatient.initForm(patient);

    const renderPhone = (patient) => {
        return ``;
    }

    const renderDateOfBirth = (patient) => dayjs(patient.dateOfBirth).format('DD/MM/YYYY');
    const renderDateOfBirthAndAge = (patient) => {
        const dob = renderDateOfBirth(patient);
        const age = patient.age;
        return `${dob} (${age} y)`
    };
    const join = (parts, sep) => parts.filter(part => part && part.length > 0).join(sep);

    const setValue = (elem, val) => {
        if (elem.tagName.toUpperCase() === "INPUT" || elem.tagName.toUpperCase() === "SELECT") {
            elem.value = val;
        } else {
            elem.innerHTML = val;
        }
    }

    const renderAddressAU = (patient) => {
        document.querySelectorAll('.pac-placeid-aus').forEach(el =>
            setValue(el, collectorPortalPatient.patient()['autocompletePlaceId'])
        );
        document.querySelectorAll('.patient-full-address').forEach(el =>
            setValue(el, collectorPortalPatient.patient()['autocompleteStreetAddress'])
        );

        if (patient.city === null || patient.city.length === 0) {
            return patient.streetAddress;
        }

        return join([join([patient.streetAddress, patient.streetAddress2, patient.city], ', '), patient.state, patient.postCode], ' ') + ', Australia';
    }

    collectorPortalPatient.patientRenderer = [
        new PropertyRenderer((patient) => patient.givenName, '[name="givenName"]', '.patient-given-name', '.medicare-givenName'),
        new PropertyRenderer((patient) => patient.familyName, '[name="familyName"]', '.patient-family-name', '.medicare-familyName'),
        new PropertyRenderer((patient) => patient.middleNames, '[name="middleNames"]', '.patient-middle-names'),

        new PropertyRenderer((patient) => patient.sex, '[name="sex"]', '.patient-sex'),
        new PropertyRenderer(renderDateOfBirth, '[name="dateOfBirth"]', '.patient-dob'),
        new PropertyRenderer(renderDateOfBirthAndAge, '.patient-dob-and-age'),

        new PropertyRenderer((patient) => patient.medicareNo, '[name="medicareNo"]', '[name="medicareNumber"]'),
        new PropertyRenderer((patient) => patient.medicareIndex, '[name="medicareIndex"]', '.patient-medicare-index'),
        new PropertyRenderer((patient) => `${patient.medicareNo} ${patient.medicareIndex} `, '.patient-medicare'),
        new PropertyRenderer((patient) => patient.dvaCardType, '[name="dvaCardType"]'),
        new PropertyRenderer((patient) => patient.dvaCardTypeLabel, '.patient-dva-card-type'),
        new PropertyRenderer((patient) => patient.dvaNo || '', '[name="dvaNo"]', '.patient-dva-no'),
        new PropertyRenderer((patient) => patient.healthCareCard, '[name="healthCareCard"', '.patient-health-care-card'),
        new PropertyRenderer((patient) => patient.healthFundNo, '[name="healthFundNo"', '.patient-health-fund-no'),

        new PropertyRenderer((patient) => patient.homeEmail, '[name="homeEmail"]', '.patient-home-email'),
        new PropertyRenderer((patient) => patient.mobilePhone, '[name="mobilePhone"]', '.patient-mobile-phone'),
        new PropertyRenderer((patient) => patient.homePhone, '[name="homePhone"]', '.patient-home-phone'),
        new PropertyRenderer((patient) => patient.businessPhone, '[name="businessPhone"]', '.patient-business-phone'),

        new PropertyRenderer((patient) => patient.streetAddress, '[name="streetAddressAU"]'),
        new PropertyRenderer((patient) => patient.streetAddress2, '[name="streetAddress2"]'),
        new PropertyRenderer((patient) => patient.city, '[name="city"]', '.patient-city'),
        new PropertyRenderer((patient) => patient.state, '[name="state"]', '.patient-state'),
        new PropertyRenderer((patient) => patient.postCode, '[name="postCode"]', '.patient-postcode'),

        new PropertyRenderer((patient) => patient.race, '[name="race"]'),
        new PropertyRenderer((patient) => patient.raceLabel, '.patient-race'),

        new PropertyRenderer((patient) => patient.autocompleteStreetAddress, '.patient-invalid-selector'),

        new PropertyRenderer(renderAddressAU, '.patient-full-address'),

    ]
}

collectorPortalPatient.overseasAddress = () => {
    if (collectorPortalPatient.patient()['country'] === 'Overseas') {
        document.getElementById('mapAddressAUBilling').value = '';
        document.getElementById('mapAddressAUBillingPlace').value = '';
        document.getElementById('mapAddressAUBillingClearer').classList.add('d-none');
    }
    return true;
}

collectorPortalPatient.showAddressAutocomplete = () => {
    document.getElementById('mapAddressAU').value = "";
    document.getElementById('mapAddressAUInput').value = '';
    document.getElementById('apartmentInput').value = '';
    document.getElementById('patientSuburbInput').value = '';
    document.getElementById('patientStateInput').value = '';
    document.getElementById('patientPostcodeInput').value = '';
    collectorPortalDOM.toggle('.address-manual, .patient-manual', '.address-auto, .patient-pac');

    collectorPortalDOM.toggle('#mapAddressAU-parent .x-clearer', 'undefined');
}

collectorPortalPatient.hideAddressAutocomplete = (source = "pac") => {
    if (source === 'pac') {
        document.getElementById('mapAddressAUInput').value = document.getElementById('mapAddressAU').value;
        document.getElementById('apartmentInput').value = '';
        document.getElementById('patientSuburbInput').value = '';
        document.getElementById('patientStateInput').value = '';
        document.getElementById('patientPostcodeInput').value = '';
    }

    collectorPortalDOM.toggle('.address-auto, .patient-pac', '.address-manual, .patient-manual');

    return true;
}

collectorPortalPatient.displayPatient = (patient) => {
    collectorPortalPatient.patientRenderer.forEach(renderer => {
        try {
            renderer.render(patient);
        } catch (error) {
            console.error(error);
        }
    });

    //todo: fix this up to not be dodgy
    document.querySelectorAll('.patient-mobile').forEach(el => {
        el.innerHTML = patient.mobilePhone;
        if (patient.mobilePhone === '') {
            el.parentElement.classList.add('d-none');
        } else {
            el.parentElement.classList.remove('d-none');
        }
    });

    document.querySelectorAll('.patient-phone').forEach(el => {
        el.innerHTML = patient.homePhone;
        if (patient.homePhone === '') {
            el.parentElement.classList.add('d-none');
        } else {
            el.parentElement.classList.remove('d-none');
        }
    });


}

collectorPortalPatient.initForm = (patient) => {
    const formEl = document.getElementById('patientForm');

    collectorPortalPatient.patientName = new PatientName(formEl);

    if (patient) {
        collectorPortalPatient.patientName.setName(patient);
    }
    document.querySelectorAll('.phone-input').forEach(input => {
        window.intlTelInput(input, {
            preferredCountries: ['au'],
            initialCountry: 'au',
            separateDialCode: true,
            formatOnDisplay: false, // Change to false to prevent auto-formatting while typing
            autoPlaceholder: 'off', // Disable auto placeholder to prevent focus issues
            utilsScript:
                'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js'
        });
    });

    validationService.setRequestDateMask('requestedDate');
    validationService.setDateMask('dateOfBirth');

    const patientAutoCompleteEl = document.getElementById('autocomplete-toggle-auto');
    const patientManualCompleteEl = document.getElementById('autocomplete-toggle-manual');

    if(patientAutoCompleteEl){
        patientAutoCompleteEl.addEventListener("click", function () {
            collectorPortalPatient.showAddressAutocomplete();
        });
    }

    if(patientManualCompleteEl){
        patientManualCompleteEl.addEventListener("click", function () {
            collectorPortalPatient.hideAddressAutocomplete('href');
        });
    }
}

collectorPortalPatient.verifyPatient = async (orderId, visitId) => {
    return collectorPortal.api.verifyPatient(visitId)
        .then(() => {
            return true;
        });
}

collectorPortalPatient.createOrder = async (patientId) => {
    console.log('createOrder : ', patientId);

    // Create and show loading overlay
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loadingOverlay';
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;

    const spinner = document.createElement('div');
    spinner.style.cssText = `
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    `;

    const loadingText = document.createElement('div');
    loadingText.textContent = 'Creating patient profile...';
    loadingText.style.cssText = `
        font-size: 18px;
        color: #333;
        font-weight: 500;
    `;

    // Add keyframes for spinner animation if not already added
    if (!document.querySelector('#spinner-style')) {
        const style = document.createElement('style');
        style.id = 'spinner-style';
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    loadingOverlay.appendChild(spinner);
    loadingOverlay.appendChild(loadingText);
    document.body.appendChild(loadingOverlay);

    let patient;
    let requestedDate;

    if (!patientId) {
        const form = document.getElementById("patientForm");
        if (!form) {
            console.error('Patient form not found');
            loadingOverlay.remove();
            return false;
        }

        console.log('Validating form...');
        try {
            const status = await validationService.validators.patientForm.validate();
            console.log('Validation status:', status);
            if (status !== 'Valid') {
                console.log('Form validation failed');
                loadingOverlay.remove();
                return false;
            }
        } catch (error) {
            console.error('Validation error:', error);
            loadingOverlay.remove();
            return false;
        }

        // Get form values safely
        const getFormValue = (name) => {
            const element = form.elements[name];
            return element ? element.value || '' : '';
        };

        // Get patient name data
        const patientNameData = collectorPortalPatient.patientName.getName();
        console.log('Patient name data:', patientNameData);

        // Get all form values
        const formValues = {
            dateOfBirth: getFormValue('dateOfBirth'),
            sex: getFormValue('sex'),
            mobilePhone: collectorPortalPatient.getFullMobileNumber(),
            medicareNo: getFormValue('medicareNo'),
            medicareIndex: getFormValue('medicareIndex'),
            mapAddressAUPlace: getFormValue('mapAddressAUPlace'),
            streetAddressAU: getFormValue('streetAddressAU'),
            streetAddress2: getFormValue('streetAddress2'),
            city: getFormValue('city'),
            state: getFormValue('state'),
            postCode: getFormValue('postCode')
        };

        console.log('Form values:', formValues);

        // Construct patient object
        patient = {
            ...patientNameData,
            ...formValues
        };

        console.log('Final patient object:', patient);

        requestedDate = getFormValue('requestedDate');
    } else {
        patient = {
            healiusId: patientId
        };
        requestedDate = document.getElementById('requestedDateInput')?.value;
    }

    try {
        console.log('Sending order creation request...');

        const csrfToken = healiusAPI.getCsrfToken();

        console.log('Request details:', {
            url: `/api/v1/order?requestedDate=${requestedDate}`,
            method: 'POST',
            headers: {
                "X-CSRF-TOKEN": csrfToken,
                "Content-Type": "application/json",
            },
            body: patient
        });

        const response = await fetch(`/api/v1/order?requestedDate=${requestedDate}`, {
            method: 'POST',
            body: JSON.stringify(patient),
            headers: {
                "X-CSRF-TOKEN": csrfToken,
                "Content-Type": "application/json",
            }
        });

        if (response.ok) {
            const healiusId = await response.text();
            console.log('Created', healiusId);
            loadingText.textContent = 'Profile created successfully! Redirecting...';
            setTimeout(() => {
                window.location.href = `/collection/ref/${healiusId}/collecting`;
            }, 1000);
        } else {
            console.error('Order creation failed:', response.status);
            const errorText = await response.text();
            console.error('Error details:', errorText);

            if (response.status === 403) {
                loadingText.textContent = 'Session expired. Please refresh the page and try again.';
                setTimeout(() => {
                    loadingOverlay.remove();
                }, 3000);
            } else {
                loadingText.textContent = 'Error creating profile. Please try again.';
                setTimeout(() => {
                    loadingOverlay.remove();
                }, 3000);
            }
        }
    } catch (error) {
        console.error('Error creating order:', error);
        loadingText.textContent = 'An error occurred. Please try again.';
        setTimeout(() => {
            loadingOverlay.remove();
        }, 3000);
    }

    return true;
}

collectorPortalPatient.createCommercialOrder = async (patientId) => {
    console.log('Patient.js: createCommercialOrder called with patientId:', patientId);

    // Show the modal loader and update heading to 'Creation in progress'
    const collectingModal = document.getElementById('collectingModal');
    if (collectingModal) {
        collectingModal.style.display = 'block';
        const heading = collectingModal.querySelector('h4.text-neutral-text');
        if (heading) heading.textContent = 'Creation in progress';
    }

    let patient;
    let requestedDate;

    if (!patientId) {
        const form = document.getElementById("patientForm");
        if (!form) {
            console.error('Patient.js: patientForm not found');
            if (collectingModal) collectingModal.style.display = 'none';
            return false;
        }

        console.log('Patient.js: Validating form...');
        try {
            const status = await validationService.validators.patientForm.validate();
            console.log('Patient.js: Validation status:', status);
            if (status !== 'Valid') {
                console.log('Patient.js: Form validation failed');
                if (collectingModal) collectingModal.style.display = 'none';
                return false;
            }
        } catch (error) {
            console.error('Patient.js: Validation error:', error);
            if (collectingModal) collectingModal.style.display = 'none';
            return false;
        }

        // Get form values safely
        const getFormValue = (name) => {
            const element = form.elements[name];
            if (!element) {
                console.warn(`Patient.js: Form element ${name} not found`);
                return '';
            }
            return element.value || '';
        };

        // Get patient name data
        const patientNameData = collectorPortalPatient.patientName.getName();
        console.log('Patient.js: Patient name data:', patientNameData);

        // Get all form values
        const formValues = {
            ...patientNameData,
            dateOfBirth: getFormValue('dateOfBirth'),
            sex: getFormValue('sex'),
            requestedDate: getFormValue('requestedDate'),
            workflow: 'COMMERCIAL'
        };

        // Ensure required fields are present
        const requiredFields = ['familyName', 'dateOfBirth', 'sex', 'requestedDate'];
        const missingFields = requiredFields.filter(field => !formValues[field]);

        if (missingFields.length > 0) {
            console.error('Patient.js: Missing required fields:', missingFields);
            if (collectingModal) {
                const heading = collectingModal.querySelector('h4.text-neutral-text');
                if (heading) heading.textContent = 'Please fill in all required fields.';
            }
            setTimeout(() => {
                if (collectingModal) collectingModal.style.display = 'none';
            }, 3000);
            return false;
        }

        patient = {
            ...formValues,
            billingType: 'COMMERCIAL'
        };
        console.log('Patient.js: Created patient object:', patient);

        requestedDate = formValues.requestedDate;
    } else {
        patient = {
            id: patientId,
            billingType: 'COMMERCIAL'
        };
        requestedDate = document.getElementById('requestedDateInput')?.value || '';
    }

    try {
        console.log('Patient.js: Sending commercial order creation request...');

        const csrfToken = healiusAPI.getCsrfToken();

        console.log('Patient.js: Request details:', {
            url: `/api/v1/order/commercial?requestedDate=${requestedDate}`,
            method: 'POST',
            headers: {
                "X-CSRF-TOKEN": csrfToken,
                "Content-Type": "application/json",
            },
            body: patient
        });

        // Log CSRF token details
        console.log('Patient.js: CSRF Token details:', {
            exists: !!healiusPortal,
            hasCsrf: !!healiusPortal?.csrf,
            tokenLength: csrfToken.length,
            tokenValue: csrfToken || 'not set',
            originalValue: healiusPortal?.csrf
        });

        const response = await fetch(`/api/v1/order/commercial?requestedDate=${requestedDate}`, {
            method: 'POST',
            body: JSON.stringify(patient),
            headers: {
                "X-CSRF-TOKEN": csrfToken,
                "Content-Type": "application/json",
            }
        });

        if (response.ok) {
            const healiusId = await response.text();
            console.log('Patient.js: Created commercial order:', healiusId);
            // Update loading text before redirect
            if (collectingModal) {
                const heading = collectingModal.querySelector('h4.text-neutral-text');
                if (heading) heading.textContent = 'Order created successfully! Redirecting...';
            }
            setTimeout(() => {
                window.location.href = `/collection/ref/${healiusId}/collecting`;
            }, 1000);
        } else {
            console.error('Patient.js: Order creation failed:', response.status);
            const errorText = await response.text();
            console.error('Patient.js: Error details:', errorText);

            // Log response headers for debugging
            console.log('Patient.js: Response headers:', {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries())
            });

            // If we get a 403, try to refresh the page to get a new CSRF token
            if (response.status === 403) {
                console.log('Patient.js: Received 403, suggesting page refresh to get new CSRF token');
                if (collectingModal) {
                    const heading = collectingModal.querySelector('h4.text-neutral-text');
                    if (heading) heading.textContent = 'Session expired. Please refresh the page and try again.';
                }
                setTimeout(() => {
                    if (collectingModal) collectingModal.style.display = 'none';
                }, 3000);
            } else {
                if (collectingModal) {
                    const heading = collectingModal.querySelector('h4.text-neutral-text');
                    if (heading) heading.textContent = 'Error creating order. Please try again.';
                }
                setTimeout(() => {
                    if (collectingModal) collectingModal.style.display = 'none';
                }, 3000);
            }
        }
    } catch (error) {
        console.error('Patient.js: Exception during order creation:', error);
        if (collectingModal) {
            const heading = collectingModal.querySelector('h4.text-neutral-text');
            if (heading) heading.textContent = 'Unexpected error. Please try again.';
            setTimeout(() => {
                collectingModal.style.display = 'none';
            }, 3000);
        }
        return false;
    }
    if (collectingModal) collectingModal.style.display = 'none';
    return true;
}

collectorPortalPatient.save = function (orderId) {
    console.log("save patient", orderId);
    validationService.validators.patientForm
        .validate()
        .then(status => {
            if (status === 'Valid') {



                collectorPortal.api.savePatient(orderId, collectorPortalPatient.patient())
                    .then(referredPatient => {
                        collectorPortalPatient.displayPatient(referredPatient);
                    });
                healiusPortal.modals['patientModal'].hide();
                return true;
            }

            return false;
        });
}

collectorPortalPatient.patient =  () => {
    const form = document.getElementById('patientForm').elements;
    let patient = {
        ...collectorPortalPatient.patientName.getName(),
        sex: form['sex'].value,
        dateOfBirth: form['dateOfBirth'].value,
        medicareNo: form['medicareNo'].value,
        medicareIndex: form['medicareIndex'].value,
        healthFundNo: form['healthFundNo'].value,
        dvaCardType: form['dvaCardType'].value,
        dvaNo: form['dvaNo'].value,
        mobilePhone: collectorPortalPatient.getFullMobileNumber(),
        homePhone: form['homePhone'].value,
        businessPhone: form['businessPhone'].value,
        homeEmail: form['homeEmail'].value,
        country: form['country'].value,
        race: form['race'].value,
    };
    if (patient.country === 'AU') {
        patient = {
            ...patient,
            streetAddress: form['mapAddressAUInput'].value,
            streetAddress2: form['apartmentInput'].value,
            city: form['patientSuburbInput'].value,
            state: form['patientStateInput'].value,
            postCode: form['patientPostcodeInput'].value,
            autocompleteStreetAddress: form['mapAddressAU'].value,
            autocompletePlaceId: form['mapAddressAUPlace'].value
        }
    } else {
        patient = {
            ...patient,
            streetAddressOverseas: form['mapAddressOverseas'].value,
            autocompleteStreetAddress: form['mapAddressOverseas'].value,
            autocompletePlaceId: form['mapAddressOverseasPlace'].value
        }
    }

    return patient;
}

collectorPortalPatient.getFullMobileNumber = function() {
    const input = document.querySelector('#mobilePhone');
    if (!input) return null;

    const iti = window.intlTelInputGlobals.getInstance(input);
    if (!iti) return null;

    return iti.getNumber().replace('+', '');
}



/**
 * PatientName class manages the display and interaction with patient name information.
 * This class handles the rendering and manipulation of patient name data in the UI.
 */
class PatientName {
    /**
     * Constructs a new PatientName instance.
     * @param {HTMLElement} el - The parent element containing the patient name form
     */
    constructor(el) {
        this.formEl = el;
        this.fullNamePanel = el.querySelector('.full-name');
        console.log('PatientName initialized with form:', {
            formExists: !!el,
            formElements: el ? Array.from(el.elements).map(e => e.name) : []
        });
    }

    addEventListener = (type, listener) => {
        this.formEl.addEventListener(type, listener);
    }

    setName = (patient) => {
        try {
            const form = this.formEl.elements;
            console.log('Setting name with patient data:', patient);

            if (form['familyName']) {
                form['familyName'].value = patient.familyName || '';
                console.log('Set familyName:', form['familyName'].value);
            }

            if (form['givenName']) {
                form['givenName'].value = patient.givenName || '';
                console.log('Set givenName:', form['givenName'].value);
            }

            if (form['middleNames']) {
                form['middleNames'].value = patient.middleNames || '';
                console.log('Set middleNames:', form['middleNames'].value);
            }
        } catch (error) {
            console.error('Error in setName:', error);
        }
    }

    getName = () => {
        try {
            const form = this.formEl.elements;
            console.log('Getting name from form elements:', {
                familyName: form['familyName']?.value,
                givenName: form['givenName']?.value,
                middleNames: form['middleNames']?.value
            });

            const nameData = {
                familyName: form['familyName']?.value || '',
                givenName: form['givenName']?.value || '',
                middleNames: form['middleNames']?.value || ''
            };

            console.log('Returning name data:', nameData);
            return nameData;
        } catch (error) {
            console.error('Error in getName:', error);
            return {
                familyName: '',
                givenName: '',
                middleNames: ''
            };
        }
    }
}

class PatientService {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupNameValidation();
            this.setupDateMasks();
        });
    }

    setupNameValidation() {
        const nameTypeSelect = document.getElementById('nameType');
        if (nameTypeSelect) {
            nameTypeSelect.addEventListener('change', (event) => {
                const formVal = validationService.validators.patientForm;
                formVal.enableValidator('familyName');
                formVal.enableValidator('givenName');
                formVal.validate();
            });
        }
    }

    setupDateMasks() {
        validationService.setRequestDateMask('requestedDate');
        validationService.setDateMask('dateOfBirth');
    }

    async validatePatient() {
        const status = await validationService.validators.patientForm.validate();
        return status === 'Valid';
    }
}