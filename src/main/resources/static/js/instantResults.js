import ApiClient from "./tests/ApiClient.js";
import InstantResultsApi from "./tests/api/InstantResultsApi.js";
import InstantResultsTypeImport from "./tests/model/InstantResultsType.js";

const InstantResultsType = InstantResultsTypeImport.InstantResultsType;

/**
 * @class InstantResultsComponent
 * @extends HTMLElement
 * @description Web component for instant results management
 */
class InstantResultsComponent extends HTMLElement {
  /**
   * @constructor
   */
  constructor() {
    super();
    this.instantResultsTable = null;
    this.instantResultsApi = null;
    this.fvForm = null;
  }

  /**
   * @method connectedCallback
   * @description Lifecycle method when element is added to DOM
   */
  connectedCallback() {
    this.initApi();
    if (window.location.pathname === "/tests/instantResult") {
      this.renderTable("table-body");
    }
    
    document.getElementById("addToTable")?.addEventListener("click", () => 
      this.renderNewModal("instantResultModal"));
  }

  /**
   * @method initApi
   * @description Initialize API client
   */
  initApi() {
    const defaultClient = ApiClient.instance;
    defaultClient.basePath = healiusPortal.baseUri;
    defaultClient.authentications["bearerAuth"] = {
      type: "oauth2",
      accessToken: healiusPortal.token
    };

    this.instantResultsApi = new InstantResultsApi(defaultClient);
  }

  /**
   * @method renderTable
   * @async
   * @param {string} id - Target element ID for the table
   * @description Render the instant results table
   */
  async renderTable(id) {
    const element = document.getElementById(id);
    if (!element) return;
    
    this.instantResultsTable = id;
    const response = await this.api.list();
    
    element.innerHTML = response.map((item, index) => 
      this.renderRow(item, index === response.length - 1)).join('');

    document.querySelectorAll('.clickable-row').forEach(row => {
      row.addEventListener('click', () => {
        window.location.href = `/tests/instantResult/${row.dataset.instantresultId}`;
      });
    });
  }

  /**
   * @method renderNewModal
   * @param {string} modalName - Modal element ID
   * @description Render modal for creating a new instant result
   */
  renderNewModal(modalName) {
    const modal = healiusPortal.modals[modalName];
    if (!modal) return;
    
    const bindingFormEl = modal._element;
    bindingFormEl.querySelector('h5').innerText = 'Add instant result';
    
    const formEl = bindingFormEl.querySelector('#addEdit-instantResult');
    
    formEl.innerHTML = `
      <form id="instantResult-form">
        <input type="hidden" name="id" value="" />
        <div class="row mt-3">
          <div class="col-4">
            <label class="col-form-label pt-0" for="name">Name</label>
            <input class="form-control" id="name" name="name" type="text" autocomplete="off" value=""
              data-fv-not-empty="true"
              data-fv-not-empty___message="Required field" />
          </div>
          <div class="col-4">
            <label class="col-form-label pt-0" for="type">Type</label>
            <select class="form-select" aria-label="Type" name="type" id="type"
              data-fv-not-empty="true"
              data-fv-not-empty___message="Required field">
              <option selected disabled hidden></option>
              <option value="DECODE">Decode</option>
              <option value="DATE">Date</option>
              <option value="TIME">Time</option>
              <option value="TEXT">Text</option>
              <option value="NUMERIC">Numeric</option>
            </select>
          </div>
        </div>
        
        <div id="type-specific-options"></div>
        
        <div class="row mt-3">
          <div class="col-12">
            <label class="col-form-label pt-0" for="errorMessage">Error message</label>
            <input class="form-control" id="errorMessage" name="errorMessage" type="text" value=""
              data-fv-not-empty="true"
              data-fv-not-empty___message="Required field" />
          </div>
        </div>
      </form>
    `;
    
    const form = formEl.querySelector('#instantResult-form');
    const typeSelect = form.querySelector('#type');
    const typeOptionsContainer = form.querySelector('#type-specific-options');
    
    if (typeSelect) {
      typeSelect.addEventListener('change', () => {
        const selectedType = typeSelect.value;
        
        if (form._validationInstance) {
          form._validationInstance.destroy();
          form._validationInstance = null;
        }
        
        typeOptionsContainer.innerHTML = '';
        
        if (selectedType === 'DECODE') {
          typeOptionsContainer.innerHTML = `
            <div id="decodeOptions" class="row mt-3">
              <div class="col-12">
                <div class="bg-primary-fill-weak rounded-2">
                  <div class="p-4">
                    <div class="row align-items-center">
                      <div class="col-4 neutral-text body2 text-start">
                        Display type
                      </div>
                      <div class="col-6 d-flex gap-4 align-bottom">
                        <div class="form-check">
                          <input class="form-check-input" type="radio" name="displayType" id="displayTypeDropdown" value="DROPDOWN"
                            data-fv-not-empty="true"
                            data-fv-not-empty___message="Display type is required">
                          <label class="body2 text-neutral-text" for="displayTypeDropdown">Dropdown</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="radio" name="displayType" id="displayTypeRadio" value="RADIO">
                          <label class="body2 text-neutral-text" for="displayTypeRadio">Radio buttons</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          `;
        } else if (selectedType === 'NUMERIC') {
          typeOptionsContainer.innerHTML = `
            <div id="numericOptions">
              <div class="row mt-3">
                <div class="col-4">
                  <label class="col-form-label pt-0" for="measurement">Measurement</label>
                  <select class="form-select" aria-label="Measurement" name="measurement" id="measurement"
                    data-fv-not-empty="true"
                    data-fv-not-empty___message="Measurement is required">
                    <option selected="selected" disabled hidden></option>
                    <option value="grams">grams</option>
                    <option value="kgs">kgs</option>
                    <option value="cms">cms</option>
                    <option value="hrs">hrs</option>
                  </select>
                </div>
                <div class="col-4">
                  <label class="col-form-label pt-0" for="minValue">Min value</label>
                  <input class="form-control" id="minValue" name="minValue" type="number" value=""
                    data-fv-numeric="true"
                    data-fv-numeric___message="Min value must be a number"/>
                </div>
                <div class="col-4">
                  <label class="col-form-label pt-0" for="maxValue">Max value</label>
                  <input class="form-control" id="maxValue" name="maxValue" type="number" value=""
                    data-fv-numeric="true"
                    data-fv-numeric___message="Max value must be a number"/>
                </div>
              </div>
              <div class="row mt-3">
                <div class="col-4">
                  <label class="col-form-label pt-0" for="decimal">Decimal</label>
                  <input class="form-control" id="decimal" name="decimal" type="number" min="0" value="0"
                    data-fv-numeric="true"
                    data-fv-numeric___message="Decimal must be a number"/>
                </div>
              </div>
            </div>
          `;
        }
        
        this.initFormValidation(form);
      });
    }

    const saveButton = bindingFormEl.querySelector(".modal-action");
    const newSaveButton = saveButton.cloneNode(true);
    saveButton.parentNode.replaceChild(newSaveButton, saveButton);
    
    newSaveButton.addEventListener("click", () => {
      this.processInstantResult(modalName);
    });

    modal.show();
    this.initFormValidation(form);
  }

  /**
   * @method handleTypeChange
   * @param {HTMLElement} formContainer - The form container element
   * @param {Event} event - Change event
   * @description Handle type dropdown change
   */
  handleTypeChange(formContainer, event) {
    const selectedType = event.target.value;
    const formWrapper = formContainer.querySelector('#instantResult-form');
    if (!formWrapper) return;
    
    const form = formWrapper.querySelector('form');
    if (!form) return;
    
    const formData = {
      id: form.elements.id?.value || '',
      name: form.elements.name?.value || '',
      type: selectedType,
      errorMessage: form.elements.errorMessage?.value || ''
    };
    
    formWrapper.innerHTML = this.renderForm(formData);
    
    // Re-attach change event listener to the new type select element
    const newTypeSelect = formWrapper.querySelector('#type');
    if (newTypeSelect) {
      newTypeSelect.addEventListener('change', this.handleTypeChange.bind(this, formContainer));
    }
  }

  /**
   * @method initFormValidation
   * @param {HTMLFormElement} form - Form to initialize validation on
   * @description Initialize FormValidation on a form
   */
  initFormValidation(form) {
    if (!form) return null;
    
    if (form._validationInstance) {
      form._validationInstance.destroy();
      form._validationInstance = null;
    }
    
    setTimeout(() => {
      const validationInstance = FormValidation.formValidation(form, {
        plugins: {
          trigger: new FormValidation.plugins.Trigger(),
          bootstrap5: new FormValidation.plugins.Bootstrap5(),
          declarative: new FormValidation.plugins.Declarative({html5Input: true})
        }
      });
      
      form._validationInstance = validationInstance;
    }, 100);
  }

  /**
   * @method validateForm
   * @async
   * @param {HTMLFormElement} form - Form to validate
   * @param {string} type - Type of instant result
   * @returns {Promise<boolean>} Validation result
   * @description Validate form data with FormValidation
   */
  async validateForm(form, type) {
    if (!form) return false;
    
    if (form._validationInstance) {
      form._validationInstance.destroy();
      form._validationInstance = null;
    }
    
    const normalizedType = (type || '').toUpperCase();
    
    const validationFields = {
      name: {
        validators: {
          notEmpty: { message: 'Name is required' }
        }
      },
      type: {
        validators: {
          notEmpty: { message: 'Type is required' }
        }
      },
      errorMessage: {
        validators: {
          notEmpty: { message: 'Error message is required' }
        }
      }
    };

    if (normalizedType === 'NUMERIC') {
      Object.assign(validationFields, {
        measurement: {
          validators: {
            notEmpty: { message: 'Measurement is required' }
          }
        },
        minValue: {
          validators: {
            numeric: { message: 'Min value must be a number' }
          }
        },
        maxValue: {
          validators: {
            numeric: { message: 'Max value must be a number' }
          }
        },
        decimal: {
          validators: {
            numeric: { message: 'Decimal must be a number' }
          }
        }
      });
    } else if (normalizedType === 'DECODE') {
      validationFields.displayType = {
        validators: {
          notEmpty: { message: 'Display type is required' }
        }
      };
    }

    const validationInstance = FormValidation.formValidation(form, {
      fields: validationFields,
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5(),
        declarative: new FormValidation.plugins.Declarative({ html5Input: true })
      }
    });
    
    form._validationInstance = validationInstance;
    
    try {
      validationInstance.validate();
      const result = await validationInstance.validate();
      return result === 'Valid';
    } catch (error) {
      return false;
    }
  }

  /**
   * @method processInstantResult
   * @async
   * @param {string} modalName - Modal element ID
   * @description Process and save instant result form data
   */
  async processInstantResult(modalName) {
    const modal = healiusPortal.modals[modalName];
    if (!modal) return;
    
    const bindingFormEl = modal._element;
    const form = bindingFormEl.querySelector('#instantResult-form');
    
    if (!form) return;
    
    const typeField = form.querySelector('#type');
    const type = typeField?.value;
    
    const nameField = form.querySelector('#name');
    const errorMessageField = form.querySelector('#errorMessage');
    const idField = form.querySelector('input[name="id"]');

    const data = {
      name: nameField?.value || '',
      type: type ? type.toUpperCase() : '',
      active: bindingFormEl.querySelector('#modalCheck')?.checked || false,
      errorMessage: errorMessageField?.value || ''
    };

    if (idField && idField.value && idField.value !== 'undefined') {
      data.id = idField.value;
    }

    if (type === 'NUMERIC') {
      const measurementField = form.querySelector('#measurement');
      const minValueField = form.querySelector('#minValue');
      const maxValueField = form.querySelector('#maxValue');
      const decimalField = form.querySelector('#decimal');
      
      data.measurement = measurementField?.value || '';
      data.minValue = minValueField?.value ? parseFloat(minValueField.value) : 0;
      data.maxValue = maxValueField?.value ? parseFloat(maxValueField.value) : 0;
      data.decimals = decimalField?.value ? parseInt(decimalField.value) : 0;
    } else if (type === 'DECODE') {
      const displayTypeRadios = form.querySelectorAll('input[name="displayType"]');
      
      for (const radio of displayTypeRadios) {
        if (radio.checked) {
          data.displayType = radio.value;
          break;
        }
      }
    }
    
    const validationResult = await this.validateForm(form, type || '');
    if (!validationResult) return;

    try {
      const isNew = !data.id;
      
      let result;
      
      if (isNew) {
        try {
          const response = await this.api.create(data);
          if (!response.ok) {
            throw new Error(`Failed to create: ${response.status} ${response.statusText}`);
          }
          result = await response.json();
          modal.hide();
          this.renderTable(this.instantResultsTable);
          return result;
        } catch (error) {
          return null;
        }
      } else {
        try {
          const response = await this.api.update(data);
          if (!response.ok) {
            throw new Error(`Failed to update: ${response.status} ${response.statusText}`);
          }
          result = await response.json();
          modal.hide();

          if (window.location.pathname.includes('/instantResult/')) {
            return result;
          }
          
          this.renderTable(this.instantResultsTable);
          return result;
        } catch (error) {
          return null;
        }
      }
    } catch (error) {
      return null;
    }
  }

  /**
   * @method renderEditModal
   * @async
   * @param {string} id - Instant result ID
   * @param {string} modalName - Modal element ID
   * @description Render modal for editing an instant result
   */
  async renderEditModal(id, modalName) {
    if (!id) {
      return;
    }

    try {
      const response = await this.api.get(id);
      const modal = healiusPortal.modals[modalName];
      if (!modal) return;
      
      const bindingFormEl = modal._element;
      bindingFormEl.querySelector('h5').innerText = 'Edit instant result';
      
      const typeValue = (response.type || '').toUpperCase();
      
      const formContainer = bindingFormEl.querySelector('#instantResult-form');
      
      formContainer.innerHTML = `
        <form>
          <input type="hidden" name="id" value="${response.id || ''}" />
          <div class="row mt-3">
            <div class="col-4">
              <label class="col-form-label pt-0" for="name">Name</label>
              <input class="form-control" id="name" name="name" type="text" autocomplete="off" value="${response.name || ''}"
                data-fv-not-empty="true"
                data-fv-not-empty___message="Required field" />
            </div>
            <div class="col-4">
              <label class="col-form-label pt-0" for="type">Type</label>
              <select class="form-select" aria-label="Type" name="type" id="type"
                data-fv-not-empty="true"
                data-fv-not-empty___message="Required field" disabled>
                <option ${!typeValue ? 'selected' : ''} disabled hidden></option>
                <option value="DECODE" ${typeValue === 'DECODE' ? 'selected' : ''}>Decode</option>
                <option value="DATE" ${typeValue === 'DATE' ? 'selected' : ''}>Date</option>
                <option value="TIME" ${typeValue === 'TIME' ? 'selected' : ''}>Time</option>
                <option value="TEXT" ${typeValue === 'TEXT' ? 'selected' : ''}>Text</option>
                <option value="NUMERIC" ${typeValue === 'NUMERIC' ? 'selected' : ''}>Numeric</option>
              </select>
            </div>
          </div>
          
          <div id="type-specific-options"></div>
          
          <div class="row mt-3">
            <div class="col-12">
              <label class="col-form-label pt-0" for="errorMessage">Error message</label>
              <input class="form-control" id="errorMessage" name="errorMessage" type="text" value="${response.errorMessage || ''}"
                data-fv-not-empty="true"
                data-fv-not-empty___message="Required field" />
            </div>
          </div>
        </form>
      `;
      
      const typeOptionsContainer = formContainer.querySelector('#type-specific-options');
      
      if (typeValue === 'DECODE') {
        typeOptionsContainer.innerHTML = `
          <div id="decodeOptions" class="row mt-3">
            <div class="col-12">
              <div class="bg-primary-fill-weak rounded-2">
                <div class="p-4">
                  <div class="row align-items-center">
                    <div class="col-4 neutral-text body2 text-start">
                      Display type
                    </div>
                    <div class="col-6 d-flex gap-4 align-bottom">
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="displayType" id="displayTypeDropdown" value="DROPDOWN" ${response.displayType === 'DROPDOWN' ? 'checked' : ''}>
                        <label class="body2 text-neutral-text" for="displayTypeDropdown">Dropdown</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="displayType" id="displayTypeRadio" value="RADIO" ${response.displayType === 'RADIO' ? 'checked' : ''}>
                        <label class="body2 text-neutral-text" for="displayTypeRadio">Radio buttons</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
      } else if (typeValue === 'NUMERIC') {
        typeOptionsContainer.innerHTML = `
          <div id="numericOptions">
            <div class="row mt-3">
              <div class="col-4">
                <label class="col-form-label pt-0" for="measurement">Measurement</label>
                <select class="form-select" aria-label="Measurement" name="measurement" id="measurement">
                  <option selected="selected" disabled hidden></option>
                  <option value="grams" ${response.measurement === 'grams' ? 'selected' : ''}>grams</option>
                  <option value="kgs" ${response.measurement === 'kgs' ? 'selected' : ''}>kgs</option>
                  <option value="cms" ${response.measurement === 'cms' ? 'selected' : ''}>cms</option>
                  <option value="hrs" ${response.measurement === 'hrs' ? 'selected' : ''}>hrs</option>
                </select>
              </div>
              <div class="col-4">
                <label class="col-form-label pt-0" for="minValue">Min value</label>
                <input class="form-control" id="minValue" name="minValue" type="number" value="${response.minValue || ''}"/>
              </div>
              <div class="col-4">
                <label class="col-form-label pt-0" for="maxValue">Max value</label>
                <input class="form-control" id="maxValue" name="maxValue" type="number" value="${response.maxValue || ''}"/>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-4">
                <label class="col-form-label pt-0" for="decimal">Decimal</label>
                <input class="form-control" id="decimal" name="decimal" type="number" min="0" value="${response.decimals || 0}"/>
              </div>
            </div>
          </div>
        `;
      }
      
      const typeSelect = formContainer.querySelector('#type');
      
      if (typeSelect) {
        typeSelect.addEventListener('change', () => {
          const selectedType = typeSelect.value;
          
          typeOptionsContainer.innerHTML = '';
          
          if (selectedType === 'DECODE') {
            typeOptionsContainer.innerHTML = `
              <div id="decodeOptions" class="row mt-3">
                <div class="col-12">
                  <div class="bg-primary-fill-weak rounded-2">
                    <div class="p-4">
                      <div class="row align-items-center">
                        <div class="col-4 neutral-text body2 text-start">
                          Display type
                        </div>
                        <div class="col-6 d-flex gap-4 align-bottom">
                          <div class="form-check">
                            <input class="form-check-input" type="radio" name="displayType" id="displayTypeDropdown" value="DROPDOWN">
                            <label class="body2 text-neutral-text" for="displayTypeDropdown">Dropdown</label>
                          </div>
                          <div class="form-check">
                            <input class="form-check-input" type="radio" name="displayType" id="displayTypeRadio" value="RADIO">
                            <label class="body2 text-neutral-text" for="displayTypeRadio">Radio buttons</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            `;
          } else if (selectedType === 'NUMERIC') {
            typeOptionsContainer.innerHTML = `
              <div id="numericOptions">
                <div class="row mt-3">
                  <div class="col-4">
                    <label class="col-form-label pt-0" for="measurement">Measurement</label>
                    <select class="form-select" aria-label="Measurement" name="measurement" id="measurement">
                      <option selected="selected" disabled hidden></option>
                      <option value="grams" ${response.measurement === 'grams' ? 'selected' : ''}>grams</option>
                      <option value="kgs" ${response.measurement === 'kgs' ? 'selected' : ''}>kgs</option>
                      <option value="cms" ${response.measurement === 'cms' ? 'selected' : ''}>cms</option>
                      <option value="hrs" ${response.measurement === 'hrs' ? 'selected' : ''}>hrs</option>
                    </select>
                  </div>
                  <div class="col-4">
                    <label class="col-form-label pt-0" for="minValue">Min value</label>
                    <input class="form-control" id="minValue" name="minValue" type="number" value=""/>
                  </div>
                  <div class="col-4">
                    <label class="col-form-label pt-0" for="maxValue">Max value</label>
                    <input class="form-control" id="maxValue" name="maxValue" type="number" value=""/>
                  </div>
                </div>
                <div class="row mt-3">
                  <div class="col-4">
                    <label class="col-form-label pt-0" for="decimal">Decimal</label>
                    <input class="form-control" id="decimal" name="decimal" type="number" min="0" value="0"/>
                  </div>
                </div>
              </div>
            `;
          }
          
          const form = formContainer.querySelector('form');
          this.initFormValidation(form);
        });
      }

      const saveButton = bindingFormEl.querySelector(".modal-action");
      const newSaveButton = saveButton.cloneNode(true);
      saveButton.parentNode.replaceChild(newSaveButton, saveButton);
      
      newSaveButton.addEventListener("click", async () => {
        try {
          const result = await this.processInstantResult(modalName);
          if (result && result.id && window.location.pathname.includes('/instantResult/')) {
            modal.hide();
            return await this.api.get(result.id);
          }
        } catch (error) {
        }
      });

      modal.show();
      bindingFormEl.querySelector('#modalCheck').checked = response.active;
      
      setTimeout(() => {
        const form = formContainer.querySelector('form');
        this.initFormValidation(form);
      }, 200);
      
      return new Promise((resolve, reject) => {
        modal._element.addEventListener('hidden.bs.modal', () => {
          if (id) {
            this.api.get(id).then(resolve).catch(reject);
          } else {
            reject(new Error('Missing ID for API call'));
          }
        }, {once: true});
      });
    } catch (error) {
      return null;
    }
  }

  /**
   * @method renderRow
   * @param {Object} instantResult - Instant result data
   * @param {boolean} last - Whether this is the last row
   * @returns {string} HTML for table row
   */
  renderRow(instantResult, last) {
    return `
      <tr data-instantresult-id="${instantResult.id}" class="clickable-row ${last ? '' : 'border-bottom border-primary-border'}">
        <td class="col-1 ps-5">${instantResult.name}</td>
        <td class="col-1">${InstantResultsType[instantResult.type]}</td>
        <td class="col-1"><img src="/tests/images/icon/${instantResult.active ? 'greenTick.svg' : 'redX.svg'}" /></td>
      </tr>
    `;
  }

  /**
   * @method renderForm
   * @param {Object} data - Instant result data
   * @param {boolean} readOnly - Whether the form should be read-only
   * @returns {string} HTML for form
   */
  renderForm(data, readOnly = false) {
    const typeValue = (data?.type || '').toUpperCase();
    const isNumeric = typeValue === 'NUMERIC';
    const isDecode = typeValue === 'DECODE';
    
    return `
      <form>
        <input type="hidden" name="id" value="${data?.id || ''}" />
        <div class="row mt-3">
          <div class="col-4">
            <label class="col-form-label pt-0" for="name">Name</label>
            <input class="form-control" id="name" name="name" type="text" autocomplete="off" value="${data?.name || ''}"
              data-fv-not-empty="true"
              data-fv-not-empty___message="Required field" />
          </div>
          <div class="col-4">
            <label class="col-form-label pt-0" for="type">Type</label>
            <select class="form-select" aria-label="Type" name="type" id="type"
              data-fv-not-empty="true"
              data-fv-not-empty___message="Required field" ${readOnly ? 'disabled' : ''}>
              <option ${!typeValue ? 'selected' : ''} disabled hidden></option>
              <option value="DECODE" ${isDecode ? 'selected' : ''}>Decode</option>
              <option value="DATE" ${typeValue === 'DATE' ? 'selected' : ''}>Date</option>
              <option value="TIME" ${typeValue === 'TIME' ? 'selected' : ''}>Time</option>
              <option value="TEXT" ${typeValue === 'TEXT' ? 'selected' : ''}>Text</option>
              <option value="NUMERIC" ${isNumeric ? 'selected' : ''}>Numeric</option>
            </select>
          </div>
        </div>

        ${typeValue ? this.renderTypeOptions(data) : ''}

        <div class="row mt-3">
          <div class="col-12">
            <label class="col-form-label pt-0" for="errorMessage">Error message</label>
            <input class="form-control" id="errorMessage" name="errorMessage" type="text" value="${data?.errorMessage || ''}"
              data-fv-not-empty="true"
              data-fv-not-empty___message="Required field" />
          </div>
        </div>
      </form>
    `;
  }

  /**
   * @method renderTypeOptions
   * @param {Object} data - Instant result data
   * @returns {string} HTML for type-specific options
   */
  renderTypeOptions(data) {
    const typeValue = (data.type || '').toUpperCase();
    const isNumeric = typeValue === 'NUMERIC';
    const isDecode = typeValue === 'DECODE';
    
    if (isDecode) {
      return `
        <div id="decodeOptions" class="row mt-3">
          <div class="col-12">
            <div class="bg-primary-fill-weak rounded-2">
              <div class="p-4">
                <div class="row align-items-center">
                  <div class="col-4 neutral-text body2 text-start">
                    Display type
                  </div>
                  <div class="col-6 d-flex gap-4 align-bottom">
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="displayType" id="displayTypeDropdown" value="DROPDOWN" ${data.displayType === 'DROPDOWN' ? 'checked' : ''}>
                      <label class="body2 text-neutral-text" for="displayTypeDropdown">Dropdown</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="displayType" id="displayTypeRadio" value="RADIO" ${data.displayType === 'RADIO' ? 'checked' : ''}>
                      <label class="body2 text-neutral-text" for="displayTypeRadio">Radio buttons</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
    } else if (isNumeric) {
      return `
        <div id="numericOptions">
          <div class="row mt-3">
            <div class="col-4">
              <label class="col-form-label pt-0" for="measurement">Measurement</label>
              <select class="form-select" aria-label="Measurement" name="measurement" id="measurement">
                <option selected="selected" disabled hidden></option>
                <option value="grams" ${data.measurement === 'grams' ? 'selected' : ''}>grams</option>
                <option value="kgs" ${data.measurement === 'kgs' ? 'selected' : ''}>kgs</option>
                <option value="cms" ${data.measurement === 'cms' ? 'selected' : ''}>cms</option>
                <option value="hrs" ${data.measurement === 'hrs' ? 'selected' : ''}>hrs</option>
              </select>
            </div>
            <div class="col-4">
              <label class="col-form-label pt-0" for="minValue">Min value</label>
              <input class="form-control" id="minValue" name="minValue" type="number" value="${data.minValue || ''}"/>
            </div>
            <div class="col-4">
              <label class="col-form-label pt-0" for="maxValue">Max value</label>
              <input class="form-control" id="maxValue" name="maxValue" type="number" value="${data.maxValue || ''}"/>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-4">
              <label class="col-form-label pt-0" for="decimal">Decimal</label>
              <input class="form-control" id="decimal" name="decimal" type="number" min="0" value="${data.decimals || 0}"/>
            </div>
          </div>
        </div>
      `;
    }
    
    return '';
  }

  /**
   * @method renderReadOnly
   * @param {Object} data - Instant result data 
   * @returns {string} HTML for read-only view
   */
  renderReadOnly(data) {
    const typeValue = (data.type || '').toUpperCase();
    const isNumeric = typeValue === 'NUMERIC';
    const isDecode = typeValue === 'DECODE';

    return `
      <div class="row">
        <div class="col-8">
          <div class="d-flex align-items-center">
            <div><img src="/tests/images/icon/trm.svg" alt="trm"/></div>
            <div class="body2 text-primary-text fw-600 ps-3">Instant result details</div>
            <div class="ms-3 px-2 py-1 bg-success-fill rounded-1 d-flex align-items-center gap-1 ${data.active ? '' : 'd-none'}" data-icon-prefix-visible="true" data-icon-suffix-visible="false" data-size="Default" data-state="Completed">
              <div class="align-items-center">
                <img src="/tests/images/icon/tick.svg" alt="Active"/>
              </div>
              <div class="caption text-success">Active</div>
            </div>
          </div>
        </div>
        <div class="col-4 text-end">
          <button class="btn btn-outline-primary-fill edit-orderable-button" type="button" data-test-id="${data.id}">
            <img src="/tests/images/icon/edit.svg" data-test-id="${data.id}" alt="Edit"/>
            Edit
          </button>
        </div>
      </div>
      <div class="row pt-3">
        <div class="form-group col-3">
          <label class="body2 text-neutral-text-medium fw-normal pb-2">Name</label>
          <div class="body2 fw-600 text-neutral-text">${data.name}</div>
        </div>
        <div class="form-group col-3">
          <label class="body2 text-neutral-text-medium fw-normal pb-2">Type</label>
          <div class="body2 fw-600 text-neutral-text">${this.getTypeLabel(data.type)}</div>
        </div>
        <div class="form-group col-3 ${data.displayType ? '' : 'd-none'}">
          <label class="body2 text-neutral-text-medium fw-normal pb-2">Display type </label>
          <div class="body2 fw-600 text-neutral-text">${this.getDisplayLabel(data.displayType)}</div>
        </div>
      </div>
      ${isNumeric ? this.renderNumericReadOnly(data) : ''}
      ${isDecode && data.active ? this.renderDecodeReadOnly(data) : ''}
      <div class="row pt-3">
        <div class="form-group col-12">
          <label class="body2 text-neutral-text-medium fw-normal pb-2">Error message</label>
          <div class="body2 fw-600 text-neutral-text">${data.errorMessage}</div>
        </div>
      </div>
    `;
  }

  /**
   * @method renderNumericReadOnly
   * @param {Object} data - Instant result data
   * @returns {string} HTML for numeric read-only view
   */
  renderNumericReadOnly(data) {
    return `
      <div class="row g-4 mt-2">
        <div class="col-3">
          <div class="d-flex flex-column gap-2">
            <label class="body2 text-neutral-text-medium fw-normal pb-2">Measurement</label>
            <div class="body2 fw-600 text-neutral-text">${data.measurement}</div>
          </div>
        </div>
        <div class="col-3">
          <div class="d-flex flex-column gap-2">
            <label class="body2 text-neutral-text-medium fw-normal pb-2">Min value</label>
            <div class="body2 fw-600 text-neutral-text">${data.minValue}</div>
          </div>
        </div>
        <div class="col-3">
          <div class="d-flex flex-column gap-2">
            <label class="body2 text-neutral-text-medium fw-normal pb-2">Max value</label>
            <div class="body2 fw-600 text-neutral-text">${data.maxValue}</div>
          </div>
        </div>
        <div class="col-3">
          <div class="d-flex flex-column gap-2">
            <label class="body2 text-neutral-text-medium fw-normal pb-2">Decimal</label>
            <div class="body2 fw-600 text-neutral-text">${data.decimals}</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * @method renderDecodeReadOnly
   * @param {Object} data - Instant result data
   * @returns {string} HTML for decode read-only view
   */
  renderDecodeReadOnly(data) {
    return `
      <div class="row g-4 mt-2">
        <div class="col-3">
          <div class="d-flex flex-column gap-2">
            <label class="body2 text-neutral-text-medium fw-normal pb-2">Display type</label>
            <div class="body2 fw-600 text-neutral-text">${data.displayType === 'dropdown' ? 'Dropdown' : 'Radio buttons'}</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * @method getDisplayLabel
   * @param {string} type - Display type code
   * @returns {string} Display type label
   */
  getDisplayLabel(type) {
    return type === 'DROPDOWN' ? 'Dropdown' : 
           type === 'RADIO' ? 'Radio buttons' : 
           "";
  }

  /**
   * @method getTypeLabel
   * @param {string} type - Instant result type code 
   * @returns {string} Type label
   */
  getTypeLabel(type) {
    if (typeof type !== 'string') return type;
    
    const typeMap = {
      'DECODE': 'Decode',
      'DATE': 'Date',
      'TIME': 'Time',
      'TEXT': 'Text',
      'NUMERIC': 'Numeric'
    };
    
    return typeMap[type] || type;
  }

  /**
   * @method fetchApi
   * @async
   * @param {string} url - API endpoint
   * @param {Object} options - Fetch options
   * @returns {Promise<Response>} Fetch response
   * @description Helper method for API calls
   */
  async fetchApi(url, options = {}) {
    const defaultOptions = {
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-TOKEN": healiusPortal.csrf
      }
    };
    
    return fetch(url, {...defaultOptions, ...options});
  }

  /**
   * @readonly
   * @returns {Object} API methods
   */
  get api() {
    return {
      list: async () => {
        try {
          return await healius.api.getPromise(callback => 
            this.instantResultsApi.listInstantResults(callback));
        } catch (error) {
          return [];
        }
      },
      get: async (id) => {
        if (!id) {
          throw new Error('Missing required ID parameter');
        }
        try {
          return await healius.api.getPromise(callback => 
            this.instantResultsApi.getInstantResult(id, callback));
        } catch (error) {
          throw error;
        }
      },
      updateCompanyInstantResults: async (data) => {
        try {
          return this.fetchApi('/tests/v1/instantResult/bu', {
            method: "PUT",
            body: JSON.stringify(data)
          });
        } catch (error) {
          throw error;
        }
      },
      addOrderables: async (id, orderables) => {
        if (!id) {
          throw new Error('Missing required ID parameter');
        }
        try {
          return this.fetchApi(`/tests/v1/instantResult/${id}/orderables`, {
            method: "POST",
            body: JSON.stringify(orderables)
          });
        } catch (error) {
          throw error;
        }
      },
      removeOrderable: async (id, orderableId) => {
        if (!id || !orderableId) {
          throw new Error('Missing required parameters');
        }
        try {
          return this.fetchApi(`/tests/v1/instantResult/${id}/orderables/${orderableId}`, {
            method: "DELETE"
          });
        } catch (error) {
          console.error(`Error removing orderable ${orderableId} from ${id}:`, error);
          throw error;
        }
      },
      create: async (data) => {
        try {
          return this.fetchApi("/tests/v1/instantResult", {
            method: "POST",
            body: JSON.stringify(data)
          });
        } catch (error) {
          console.error('Error creating instant result:', error);
          throw error;
        }
      },
      update: async (data) => {
        if (!data.id) {
          throw new Error('Missing required ID for update');
        }
        try {
          return this.fetchApi("/tests/v1/instantResult", {
            method: "PUT",
            body: JSON.stringify(data)
          });
        } catch (error) {
          console.error('Error updating instant result:', error);
          throw error;
        }
      }
    };
  }
}

customElements.define('instant-results-component', InstantResultsComponent);

document.addEventListener('DOMContentLoaded', () => {
  if (window.location.pathname === "/tests/instantResult" && !document.querySelector('instant-results-component')) {
    document.body.appendChild(document.createElement('instant-results-component'));
  }
});

export default InstantResultsComponent;

