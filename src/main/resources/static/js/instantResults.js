export class InstantResults {
    api;
  paperCollect;
    #active = true;

    constructor(orderId, visitId, toFollow, readOnly = false, notificationOnly = false) {
        this.api = new InstantResultsAPI(healiusAPI.getCsrfToken(), '/api/v1/order', orderId, visitId);
        this.orderId = orderId;
        this.visitId = visitId;
        this.toFollow = toFollow;
        this.readOnly = readOnly;
        //todo: have the paper version of these render when notificationOnly is true
        logger.log('InstantResults.constructor() => ', this);
    }

    addInstantResultString = (data) => {
        let instantResultsData = new Array();
        let orderableIds = new Array();

        for (let h = 0; h < data.instantResults.length; h++) {
            if (!orderableIds.includes(data.instantResults[h].orderableId)) {
                instantResultsData[data.instantResults[h].orderableId] = new Array();
                orderableIds.push(data.instantResults[h].orderableId);
            }
        }

        for (let i = 0; i < data.instantResults.length; i++) {
            instantResultsData[data.instantResults[i].orderableId].push(data.instantResults[i]);
        }

        for (let j = 0; j < orderableIds.length; j++) {
            const orderableId = orderableIds[j];
            const irNVPairs = [];

            for (let k = 0; k < instantResultsData[orderableId].length; k++) {
                let dataValue = data.instantResults[k].value;
                if (dataValue === null) {
                    dataValue = '';
                }

                irNVPairs.push(data.instantResults[k].name + ': ' + dataValue);
            }

            this.renderInstantResultsString(orderableId, irNVPairs.join(', '));
        }
    }

    renderInstantResultsString = (orderableId, irData) => {
        //logger.info('renderInstantResultsString', orderableId, irData);
        const el = document.querySelectorAll('span#instance-results-' + orderableId).forEach(el => {
            el.innerHTML = irData;
        });
    }

    /**
     * instantResultPromise {data}
     */
    addInstantResultPanels = (data) => {
        if (data === undefined) {
            return;
        }

        let instantResultsData = new Array();
        let orderableIds = new Array();

        for (let h = 0; h < data.instantResults.length; h++) {
            if (!orderableIds.includes(data.instantResults[h].orderableId)) {
                instantResultsData[data.instantResults[h].orderableId] = new Array();
                orderableIds.push(data.instantResults[h].orderableId);
            }
        }

        for (let i = 0; i < data.instantResults.length; i++) {
            instantResultsData[data.instantResults[i].orderableId].push(data.instantResults[i]);
        }

        for (let j = 0; j < orderableIds.length; j++) {
            const orderableId = orderableIds[j];

            const specimenEl = document.querySelector(`div[data-orderable="${orderableId}"]`);
            const specimenIFCEl = document.querySelector(`div.specimen-ifc[data-orderable="${orderableId}"]`);
          if (specimenEl) {
            specimenEl.classList.add('joined-specimen');

            if (!document.getElementById('instantResult-' + orderableId)) {
              const newDivEl = document.createElement('div');
              newDivEl.setAttribute('class',
                  'specimen-row specimen specimen-result row pt-0 pb-2 px-0');
              newDivEl.setAttribute('id', 'instantResult-' + orderableId);

              let resultHTML = `<div class="mx-6 mt-2 mb-0 col d-flex border p-2 bg-neutral-fill-weak rounded-2 form-group">`;
              for (let k = 0; k < instantResultsData[orderableId].length;
                  k++) {
                resultHTML += `${this.renderInputHTML(
                    instantResultsData[orderableId][k])}`;
              }
              resultHTML += `</div>`;

              newDivEl.innerHTML = resultHTML;
              if (specimenIFCEl) {
                specimenIFCEl.insertAdjacentElement('afterend',
                    newDivEl);

                specimenIFCEl.classList.add('specimen-ir-ifc-joint');
                newDivEl.classList.add('specimen-ir-ifc-joint');

              } else {
                specimenEl.insertAdjacentElement('afterend', newDivEl);
              }
            }

            const selectEl = specimenEl.querySelector(
                'select.specimen-status');
            if (selectEl) {
              selectEl.addEventListener('change', (e) => {
                this.changeSpecimenSelect(e.target);
              });
            }
          }
        }
    }

    /**
     * instantResultPromise {data}
     */
    addInstantResultViewPanels = (data) => {
        if (data === undefined) {
            return;
        }

        let instantResultsData = new Array();
        let orderableIds = new Array();

        for (let h = 0; h < data.instantResults.length; h++) {
            if (!orderableIds.includes(data.instantResults[h].orderableId)) {
                instantResultsData[data.instantResults[h].orderableId] = new Array();
                orderableIds.push(data.instantResults[h].orderableId);
            }
        }

        for (let i = 0; i < data.instantResults.length; i++) {
            instantResultsData[data.instantResults[i].orderableId].push(data.instantResults[i]);
        }

        for (let j = 0; j < orderableIds.length; j++) {
            const orderableId = orderableIds[j];

            const specimenEl = document.querySelector(`div[data-orderable="${orderableId}"]`);
            const specimenIFCEl = document.querySelector(`div.specimen-ifc[data-orderable="${orderableId}"]`);
          if (specimenEl) {
            specimenEl.classList.add('joined-specimen');

            if (!document.getElementById('instantResult-' + orderableId)) {
                const newDivEl = document.createElement('div');
                newDivEl.setAttribute('class', 'specimen-row specimen specimen-result row pt-0 pb-2 px-0');
                newDivEl.setAttribute('id', 'instantResult-' + orderableId);

                let instantResultNames = [];
                for (let k = 0; k < instantResultsData[orderableId].length; k++) {
                    instantResultNames.push(instantResultsData[orderableId][k].name);
                }

                let resultHTML = `<div class="mx-6 mt-2 mb-0 col d-flex border p-2 pb-0 bg-neutral-fill-weak rounded-2 form-group">
                <div class="row p-2 pb-3 w-100">
                    <div class="col col-auto ms-1">
                        <span class="fw-600 ps-2">Additional information required: </span> ${instantResultNames.filter(n => n).join(', ')}
                    </div>
                </div>`;

                newDivEl.innerHTML = resultHTML;
                if (specimenIFCEl) {
                    specimenIFCEl.insertAdjacentElement('afterend', newDivEl);

                    specimenIFCEl.classList.add('specimen-ir-ifc-joint');
                    newDivEl.classList.add('specimen-ir-ifc-joint');

                } else {
                    specimenEl.insertAdjacentElement('afterend', newDivEl);
                }
            }

            const selectEl = specimenEl.querySelector(
                'select.specimen-status');
            if (selectEl) {
              selectEl.addEventListener('change', (e) => {
                this.changeSpecimenSelect(e.target);
              });
            }
          }
        }
    }

    renderInputHTML = (resultData) => {
        let resultHTML = `<div class="pb-2 px-2 d-inline-block mt-3 mx-0 my-2 row col-3">`;
        resultHTML += `<label for="result-${resultData.id}" class="fw-500 ms-1 mb-2">${resultData.name}</label>`;

        let dataValue = resultData.value;
        if (dataValue === null) {
            dataValue = '';
        }

        switch (resultData.type) {
            case InstantResultsType.TEXT:
                resultHTML += `<input ${this.inputData(resultData)} autoComplete="off" maxlength="255" type="text" value="${dataValue}" id="result-${resultData.id}" name="instantResult_${resultData.id.replaceAll('-','_')}" class="ms-2 pe-6 form-control form-control-sm" />`;
                resultHTML += `${this.renderInputUnit(resultData.measurement)}`;
                break;

            case InstantResultsType.NUMERIC:
                resultHTML += `<input ${this.inputData(resultData)} autoComplete="off" maxlength="16"  type="text" value="${dataValue}" id="result-${resultData.id}" name="instantResult_${resultData.id.replaceAll('-','_')}" class="ms-2 pe-6 form-control form-control-sm" />`;
                resultHTML += `${this.renderInputUnit(resultData.measurement)}`;
                break;

            case InstantResultsType.DATE:
                resultHTML += `<input ${this.inputData(resultData)} autoComplete="off"  type="date" value="${dataValue}" id="result-${resultData.id}" name="instantResult_${resultData.id.replaceAll('-','_')}" class="ms-2 pe-2 form-control form-control-sm" />`;
                //resultHTML += `${this.renderInputUnitIcon('results-calendar')}`;
                break;

            case InstantResultsType.TIME:
                resultHTML += `<input ${this.inputData(resultData)} autoComplete="off" type="time" value="${dataValue}" id="result-${resultData.id}" name="instantResult_${resultData.id.replaceAll('-','_')}" class="ms-2 pe-2 form-control form-control-sm" />`;
                //resultHTML += `${this.renderInputUnitIcon('results-clock')}`;
                break;

            case InstantResultsType.DECODE:
                if (resultData.displayType === InstantResultsDisplayType.DROPDOWN) {
                    resultHTML += `<select ${this.inputData(resultData)} data-value="" id="result-${resultData.id}" name="instantResult_${resultData.id.replaceAll('-','_')}" class="instant-result instant-result-select form-select form-control-sm ms-2 py-1">`;
                    resultHTML += `<option value="" class="form-select">Select</option>`;
                    for (let i = 0; i < resultData.companyInstantResult.instantResultPropertiesList.length; i++) {
                        resultHTML += `<option value="${resultData.companyInstantResult.instantResultPropertiesList[i].value}" class="form-select"${dataValue === resultData.companyInstantResult.instantResultPropertiesList[i].value ? ' selected':''}>${resultData.companyInstantResult.instantResultPropertiesList[i].label}</option>`;
                    }
                    resultHTML += `</select>`;
                    break;
                }
                if (resultData.displayType === InstantResultsDisplayType.RADIO) {
                    resultHTML += `<input ${this.inputData(resultData)} type="hidden" id="result-${resultData.id}" />`;
                    resultHTML += `<input ${this.inputData(resultData)} type="hidden" id="instantResult_${resultData.id}" />`;
                    resultHTML += `<div class="form-group">`;
                    for (let i = 0; i < resultData.companyInstantResult.instantResultPropertiesList.length; i++) {
                        const camelisedName = this.camelizePropertyName(resultData, i);

                        resultHTML += `                                                    
                            <label for="result-${resultData.id}_${camelisedName}" class="body2 ms-2 ps-1 mb-2">
                                <input ${this.inputData(resultData)} type="radio" name="instantResult_${resultData.id.replaceAll('-','_')}" id="result-${resultData.id}_${camelisedName}" value="${resultData.companyInstantResult.instantResultPropertiesList[i].value}" class="instantResult_${camelisedName} me-2"${dataValue === resultData.companyInstantResult.instantResultPropertiesList[i].value ? ' selected checked':''} />
                                    ${resultData.companyInstantResult.instantResultPropertiesList[i].label}
                            </label>
                            `;

                        //resultHTML += `</div><div class="pb-2 px-2 d-inline-block mt-3 mx-0 my-2 row col-2"><label for="" class="fw-500 ms-1 mb-2">&nbsp;</label>`;
                    }
                    resultHTML += `</div>`;
                    break;
                }

        }

        resultHTML += `
        </div>`;

        return resultHTML;
    }

    renderInputUnit = (units) => {
        return units !== null ? `<div class="x-clearer results-unit">
            ${units}
        </div>` : ``;
    }

    renderInputUnitIcon = (icon) => {
        return `<div class="x-clearer results-unit">
            <img src="/images/icon/${icon}.svg" alt="" />
        </div>`;
    }

    changeSpecimenSelect = (e) => {
        document.getElementById('collectionSection').dispatchEvent(new CustomEvent('specimens-load', {}));
        /*return;
        const specimenId = e.dataset.specimen;
        if (e.value === '') {
            this.enableFormValidation(specimenId);
        } else {
            this.disableFormValidation(specimenId);
        }*/
    }

    inputData = (resultData) => {
        let resultAttributes = `data-resultid="${resultData.id}" data-type="${resultData.type}" data-message="${resultData.errorMessage}"`;

        console.log('inputData', resultData);

        switch (resultData.type) {
            // case InstantResultsType.TEXT:
            //     resultAttributes += `data-minLength="${resultData.string.minLength}" data-maxLength="${resultData.string.maxLength}" data-regex="${resultData.string.regex}"`;
            //     break;

            case InstantResultsType.NUMERIC:
                resultAttributes += `data-min="${resultData.minValue}" data-max="${resultData.maxValue}" data-decimals="${resultData.decimals}"`;
                break;

            case InstantResultsType.DECODE:
                if (resultData.displayType === "DROPDOWN") {
                    resultAttributes += `data-decode-type="select"`;
                }
                if (resultData.displayType === "RADIO") {
                    resultAttributes += `data-decode-type="radio"`;
                }
                break;
            //
            // case 'radio':
            //     resultAttributes += `data-options="`;
            //     for (let i = 0; i < resultData.radio.options.length; i++) {
            //         const camelisedName = collectorPortalDOM.camelize(resultData.radio.options[i].name);
            //         resultAttributes += 'result-' + resultData.id + '_' + camelisedName + ',';
            //     }
            //     resultAttributes += `"`;
            //     break;
        }
        return resultAttributes;
    }

    setFormValidation = (data) => {
        for (let i = 0; i < data.instantResults.length; i++) {
            const resultObj = {
                validators: {
                    callback: {
                        callback: this.validation
                    },
                }
            }
            console.log('setFormValidation()', 'instantResult_' + data.instantResults[i].id.replaceAll('-','_'));
            validationService.validators.paymentCalculatorForm.addField(`instantResult_${data.instantResults[i].id.replaceAll('-','_')}`, resultObj);
        }

        /*collectorPortalValidation.paymentCalculator.on('core.field.invalid', function(field) {
            const typeEl = document.getElementById(field);
            if (typeEl?.getAttribute('data-type') === 'radio') {
                typeEl.closest('.form-group').classList.add('border-error-border');
            }
        });
        collectorPortalValidation.paymentCalculator.on('core.field.valid', function(field) {
            const typeEl = document.getElementById(field);
            if (typeEl?.getAttribute('data-type') === 'radio') {
                typeEl.closest('.form-group').classList.remove('border-error-border');
            }
        });*/
    }

    setEventTriggers = (instantResult) => {
        for (let i = 0; i < instantResult.instantResults.length; i++) {
            const el = document.getElementById('result-' + instantResult.instantResults[i].id);
          if (!el) {
            continue;
          }
            switch (el.getAttribute('data-type')) {
                case 'radio':
                    const options = el.getAttribute('data-options').split(',').filter(n => n);
                    options.forEach(el2 => {
                        document.getElementById(el2).addEventListener('click', (e) => this.onUpdate(e, 'radio'));
                    });
                    break;

                case 'select':
                    el.addEventListener('change', (e) => this.onUpdate(e, 'select'));
                    break;

                default:
                    el.addEventListener('focusout', (e) => this.onUpdate(e));
                    break;
            }
        }
    }

    onUpdate = (event, type) => {
        if (type !== 'radio') {
            if ((type === 'select' && event.target.value === '') || event.target.classList.contains('is-invalid')) {
                return false;
            }
        } else {
            validationService.validators.paymentCalculatorForm.revalidateField(event.target.name);
        }

        this.api.saveInstantResult(event.target.dataset.resultid, event.target.value);
    }

    disableFormValidation = (specimenId = '') => {
        const specimenEl = document.getElementById('specimen-' + specimenId);
        specimenEl.classList.remove('joined-specimen');

        const instantResultsEl = document.getElementById('instantResult-' + specimenId);
        collectorPortalDOM.displayIf(instantResultsEl, false);
        instantResultsEl.querySelectorAll('input, select, textarea').forEach(el => {
            validationService.validators.paymentCalculatorForm.disableValidator(el.name);
        });


    }

    enableFormValidation = (specimenId = '') => {
        const specimenEl = document.getElementById('specimen-' + specimenId);
        specimenEl.classList.add('joined-specimen');
        const instantResultsEl = document.getElementById('instantResult-' + specimenId);
        collectorPortalDOM.displayIf(instantResultsEl, true);
        instantResultsEl.querySelectorAll('input, select, textarea').forEach(el => {
            validationService.validators.paymentCalculatorForm.enableValidator(el.name);
        });
    }

    validationFailed = (result) => {
        return {
            valid: false,
            message: result.message
        }
    }

    validation = (input) => {
        const data = input.value.trim();

        switch (input.element.dataset.type) {
            case 'TEXT':
                return this.stringValidation(data, input.element.dataset);
                break;

            case 'NUMERIC':
                return this.numericValidation(data, input.element.dataset);
                break;

            case 'DATE':
                return this.dateValidation(data, input.element.dataset);
                break;

            case 'TIME':
                return this.timeValidation(data, input.element.dataset);
                break;

            case 'DECODE':
                logger.info('decodeValidation', input, input.element.dataset);
                const decodeType = input.element.dataset.decodeType;
                if (decodeType === 'radio') {
                    return this.radioValidation(data, input.element.dataset);
                }
                if (decodeType === 'select') {
                    return this.selectValidation(data, input.element.dataset);
                }
                break;

            default:
                return true;
                break;
        }
    }

    stringValidation = (input, restrictions) => {
        logger.info('stringValidation', input, restrictions);
        if (input.length < 1) {
            return this.validationFailed(restrictions);
        }

        return true;
    }

    numericValidation = (input, restrictions) => {
        logger.info('numericValidation', input, restrictions);
        const _min = restrictions.min;
        const _max = restrictions.max;

        if (input.length === 0 ||
            isNaN(input) ||
            parseInt(input) < _min ||
            parseInt(input) > _max) {

            return this.validationFailed(restrictions);
        }

        return true;
    }

    dateValidation = (input, restrictions) => {
        logger.info('dateValidation', input, restrictions);
        if (input.length === 0) {
            return this.validationFailed(restrictions);
        }
        // todo: html input[type=date] field has no value until a valid date is entered / selected
        // todo: use dayjs to check the input is a valid date

        return true;
    }

    timeValidation = (input, restrictions) => {
        logger.info('timeValidation', input, restrictions);
        if (input.length === 0) {
            return this.validationFailed(restrictions);
        }
        // todo: html input[type=time] field has no value until a valid time is entered / selected

        return true;
    }

    selectValidation = (input, restrictions) => {
        logger.info('selectValidation', input, restrictions);

        if (input.length === 0) {
            return this.validationFailed(restrictions);
        }

        return true;
    }

    radioValidation = (input, restrictions) => {
        logger.info('radioValidation', input, restrictions);
        if (input.length === 0) {
            return this.validationFailed(restrictions);
        }

        return true;
    }

    load = () => {

    }

    camelizePropertyName(resultData, i) {
        const camelisedName = collectorPortalDOM.camelize(resultData.companyInstantResult.instantResultPropertiesList[i].label);
        return camelisedName;
    }

    hideInstantResults = (orderableId) => {
        collectorPortalDOM.displayIf(document.getElementById('paymentCalculatorForm').querySelector('#instantResult-' + orderableId), false);
    }

    hideAllInstantResults = () => {
        document.getElementById('paymentCalculatorForm').querySelectorAll('.specimen-instant-result').forEach(el => {
            collectorPortalDOM.displayIf(el, false);
        });
    }
}

export const InstantResultsType = {
    DECODE: 'DECODE',
    DATE: 'DATE',
    TEXT: 'TEXT',
    NUMERIC: 'NUMERIC',
    TIME: 'TIME'
};

export const InstantResultsDisplayType = {
    DROPDOWN: 'DROPDOWN',
    RADIO: 'RADIO'
};

export class InstantResultsData {
    constructor(data) {
        this.orderId = data.orderId;
        this.healiusId = data.healiusId;
        this.billingType = data.billingType;
        this.billingTypeExt = data.billingTypeExt;
        this.verified = data.verified;
        this.hasBeenPaid = data.hasBeenPaid;
        this.editable = data.editable;
        this.tests = data.tests;
        this.instantResults = data.instantResults?.map(result => ({
            id: result.id,
            instantResultId: result.instantResultId,
            value: result.value,
            type: result.instantResultsType ? InstantResultsType[result.instantResultsType] : null,
            measurement: result.measurement,
            minValue: result.minValue,
            maxValue: result.maxValue,
            decimals: result.decimals,
            displayType: result.instantResultsDisplayType ? InstantResultsDisplayType[result.instantResultsDisplayType] : null,
            errorMessage: result.errorMessage,
            orderableId: result.orderableId,
            companyInstantResult: result.companyInstantResult ? {
                id: result.companyInstantResult.id,
                instantResultPropertiesList: result.companyInstantResult.instantResultPropertiesList?.map(prop => ({
                    id: prop.id,
                    label: prop.label,
                    value: prop.value
                }))
            } : null
        }));
    }

}

export class InstantResultsAPI extends Fetch {
    #apiRef;
    #apiV1Ref;

    static jsonResponseHandler = response => response.json();
    static textResponseHandler = response => response.text();

    constructor(csrf, baseUrl = '/api/v1/order', orderId, visitId) {
        super(csrf, baseUrl);
        this.#apiRef = new Fetch(csrf, baseUrl + '/' + orderId + '/' + visitId);
        this.#apiV1Ref = new Fetch(csrf, '/api/v1/visit/' + visitId);

        logger.log('InstantResultsAPI.constructor() => ', this, baseUrl);
    }

    /**
     *
     * @returns {Promise<InstantResultsData|null>}
     */
    getInstantResultsData = () => {
        try {
            return this.#apiRef.get(`instantResults`, InstantResultsAPI.jsonResponseHandler);
        } catch (error) {
            logger.error('Error fetching instant results:', error);
            return null;
        }
    }

    saveInstantResult = (resultId, data) => {
        if (data.trim().length === 0) {
            return;
        }
        logger.info('Saving Instant Result', resultId, data.toString());
        const jsonBody = data.toString();
        this.#apiV1Ref.patch(`instantResult/${resultId}`, jsonBody, InstantResultsAPI.textResponseHandler);
    }
}