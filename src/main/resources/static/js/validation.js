class ValidationService {
    constructor() {
        this.dvaRegex = /^[NVQSWT]([A-Z ][0-9]{1,6}|[A-Z]{2}[0-9]{1,5}|[A-Z]{3}[0-9]{1,4})[A-Z]?$/;
        this.validators = {};
        this.phoneInputInstances = new Map(); // Track phone input instances
        this.initializeValidators();
    }

    initializeValidators() {
        FormValidation.validators.optionalDate = () => ({
            validate: this.optionalDateValidation.bind(this, true)
        });
    }

    // Email validation
    emailValidationCallback(input) {
        const value = input.value.trim();
        if (value === '') return true;

        return (
            FormValidation.validators.emailAddress().validate({ value }).valid &&
            FormValidation.validators.regexp().validate({
                value,
                options: { regexp: '^[^@\\s]+@([^@\\s]+\\.)+[^@\\s]+$' }
            }).valid
        );
    }

    collectorValidation(field) {
        return {
            message: 'Enter collector username',
            validators: {
                remote: {
                    message: 'The username you\'ve entered does not match the credentials entered to log in to this computer. Please click "Switch account" for details on how to log out of this computer and log in with your credentials',
                    url: (field) => `/api/v1/validator/username/${field.value}`,
                    headers: {
                        "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
                    }
                },
                notEmpty: {
                    message: 'Enter collector username',
                }
            }
        }
    }

    collectorIdValidation(field) {
        return {
            message: 'Enter collector username',
            validators: {
                remote: {
                    message: 'The username you\'ve entered does not match the credentials entered to log in to this computer. Please click "Switch account" for details on how to log out of this computer and log in with your credentials',
                    url: (field) => `/api/v1/validator/username/${field.value}`,
                    headers: {
                        "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
                    }
                },
                notEmpty: {
                    message: 'Enter collector username',
                }
            }
        }
    }

    // Date validation
    optionalDateValidation(allowEmpty, input) {
        const value = input.value.replace(/_+/g, '');
        return {
            valid: (allowEmpty && value === '//') || dayjs(value, 'DD/MM/YYYY', true).isValid()
        };
    }

    isAIMapped = (input) => {
        const result = input.value === 'true';
        collectorPortalDOM.displayIf(document.getElementById('ai-matching-error'), !result);

        return result;
    }

    dateValidationRequired(input) {
        const allowEmpty = document.getElementById('expiry-required')?.classList.contains('d-none');
        const value = input.value.replace(/_+/g, '');
        const enteredDate = dayjs(value, 'DD/MM/YYYY', true);
        
        return {
            valid: (allowEmpty && value === '//') || 
                   (enteredDate.isValid() && 
                    (allowEmpty || 
                     enteredDate.isSame(dayjs(), "day") ||
                     enteredDate.isBefore(dayjs(), "day")))
        };
    }

    dateValidation(input) {
        const allowEmpty = true;
        const value = input.value.replace(/_+/g, '');
        const enteredDate = dayjs(value, 'DD/MM/YYYY', true);

        return {
            valid: (allowEmpty && value === '//') ||
                (enteredDate.isValid() &&
                    (allowEmpty ||
                        enteredDate.isSame(dayjs(), "day") ||
                        enteredDate.isAfter(dayjs(), "day")))
        };
    }

    // Medicare validation
    medicareValidatorCallback(input) {
        const medicareNumber = input.value?.trim();
        if (medicareNumber?.length === 10) {
            const matches = medicareNumber.match(/^(\d{8})(\d)/);
            if (!matches) return false;

            const base = matches[1];
            const checkDigit = matches[2];
            const weights = [1, 3, 7, 9, 1, 3, 7, 9];

            let sum = 0;
            for (let i = 0; i < weights.length; i++) {
                sum += parseInt(base[i], 10) * weights[i];
            }

            return sum % 10 === parseInt(checkDigit, 10);
        }

        return medicareNumber?.length === 0;
    }

    // Phone validation
    patientMobilePhoneCallback(input) {
        return true;// Get phone input elements
        const mobileElement = document.getElementById('mobilePhone');
        const homeElement = document.getElementById('homePhone');

        // Get row elements for display toggling
        const mobileRowElements = document.querySelectorAll('.mobile-row');
        const homeRowElements = document.querySelectorAll('.phone-row');

        // Check if values exist (more than 1 character)
        const hasMobileValue = mobileElement?.value.trim().length > 1;
        const hasHomeValue = homeElement?.value.trim().length > 1;

        // Validate using intlTelInput if available
        let mobileValid = false;
        let homeValid = false;

        if (hasMobileValue && window.intlTelInputGlobals) {
            const mobileIti = window.intlTelInputGlobals.getInstance(mobileElement);
            mobileValid = mobileIti ? mobileIti.isValidNumber() : hasMobileValue;
        }

        if (hasHomeValue && window.intlTelInputGlobals) {
            const homeIti = window.intlTelInputGlobals.getInstance(homeElement);
            homeValid = homeIti ? homeIti.isValidNumber() : hasHomeValue;
        }

        // Toggle visibility of complementary field based on mobile value
        if (hasMobileValue) {
            homeRowElements.forEach(el => el.classList.remove('d-none'));
        } else {
            homeRowElements.forEach(el => el.classList.add('d-none'));
        }

        // Return validation result - mobile must be valid, or if empty, home must be valid
        return mobileValid || (homeValid && !hasMobileValue);
    }

    patientHomePhoneCallback(input) {
        return true;// Get phone input elements
        const homeElement = document.getElementById('homePhone');

        // Check if value exists (more than 1 character)
        const hasHomeValue = homeElement?.value.trim().length > 1;

        // Empty value is valid
        if (!hasHomeValue) {
            return true;
        }

        // Validate using intlTelInput if available
        if (window.intlTelInputGlobals) {
            const homeIti = window.intlTelInputGlobals.getInstance(homeElement);
            return homeIti ? homeIti.isValidNumber() : true;
        }

        // If intlTelInput is not available, consider it valid
        return true;
    }

    // Address validation
    dvaValidatorCallback(input) {
        console.log('DVA callback triggered', input);

        if(input.value.length === 0){
            return true;
        }

        const value = input.value;
        const isValid = this.dvaRegex.test(value);
        console.log('DVA validation result:', isValid);

        if(!isValid){
           this.setNonFormFieldInvalid(document.getElementById('editPatientDetails')?.querySelector('.patient-dva-no')),
               input.id
        }

        return isValid
    }

    // Address validation
    patientAddressCallback(input) {
        console.log('Address callback triggered');
        const placeId = document.getElementById('mapAddressAUPlace')?.value;
        console.log('Place ID:', placeId);
        const returnValue = placeId !== '';
        console.log('Address validation result:', returnValue);

        this.setNonFormFieldStatus(
            document.getElementById('editPatientDetails')?.querySelector('.patient-address'),
            returnValue,
            'mapAddressAU'
        );

        return returnValue;
    }

    // Field status management
    setNonFormFieldStatus(el, status, id) {
        console.log('setNonFormFieldStatus()', el)

        if (!el) {
            return;
        }
        

    }

    setNonFormFieldInvalid(el, id) {
        console.log('setNonFormFieldInvalid()', el)
        el.innerHTML = '<div class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback fw-400" data-parent="#' + id + '">Missing info</div>';
    }

    setNonFormFieldValid(el, id) {
        if (id === 'mapAddressAU') {
            const addressInput = document.getElementById('mapAddressAUInput');
            const apartmentInput = document.getElementById('apartmentInput');
            const suburbInput = document.getElementById('patientSuburbInput');
            const stateInput = document.getElementById('patientStateInput');
            const postcodeInput = document.getElementById('patientPostcodeInput');

            el.innerHTML = [
                addressInput?.value,
                apartmentInput?.value,
                suburbInput?.value,
                stateInput?.value,
                postcodeInput?.value
            ].filter(Boolean).join(' ');
        } else {
            el.innerHTML = document.getElementById(id)?.value || '';
        }
    }

    // Form validation setup
    setupFormValidation(formId, options) {
        const form = document.getElementById(formId);
        logger.info(`Form with id ${formId}`, options);
        if (!form) {
            logger.warn(`Form with id ${formId} not found`);
            return null;
        }

        const validator = FormValidation.formValidation(form, {
            ...options,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
                // TODO IF VALIDATION BREAKS HERE IS WHY
                excluded: new FormValidation.plugins.Excluded({
                    excluded: function(field, element) {
                        return !element.offsetParent;
                    }
                }),
                ...options?.plugins
            }
        });

        this.validators[formId] = validator;
        return validator;
    }

    // Validation helpers
    async validateForm(formId) {
        console.log('Validating form:', formId);
        const validator = this.validators[formId];
        if (!validator) {
            console.warn(`No validator found for form ${formId}`);
            return false;
        }

        const status = await validator.validate();
        console.log('Form validation status:', status);
        return status === 'Valid';
    }

    async validateField(formId, field) {
        const validator = this.validators[formId];
        if (!validator) {
            logger.warn(`No validator found for form ${formId}`);
            return false;
        }

        const status = await validator.validateField(field);
        return status === 'Valid';
    }

    // Input masking methods
    setRequestDateMask(inputId) {
        return this.setDateMask(inputId);
    }

    setDecimalMask(inputEl) {
        IMask(inputEl, {
            mask: Number,
            scale: 2,
            thousandsSeparator: '',
            padFractionalZeros: true,
            radix: '.',
            min: 0,
            max: 99999.99,
            autofix: true,
        });
    }

    setPositiveIntegerMask(inputEl, digits = 5) {
        let input = document.getElementById(inputEl);
        IMask(input, {
            mask: Number,
            scale: 0,
            thousandsSeparator: '',
            radix: '.',
            min: 0,
            max: parseInt("9".repeat(digits)),
            autofix: true,
        });
    }

    setDateMask(id, minDate = new Date(1900, 0, 1), maxDate = new Date()) {
        let input = document.getElementById(id);
        if (!input) return;

        const momentFormat = 'DD/MM/YYYY';
        IMask(input, {
            mask: Date,
            pattern: momentFormat,
            min: minDate,
            max: maxDate,
            lazy: false,
            format(date) {
                return moment(date).format(momentFormat);
            },
            parse(str) {
                return moment(str, momentFormat);
            },
            blocks: {
                YYYY: {
                    mask: IMask.MaskedRange,
                    from: 1900,
                    to: 3000
                },
                MM: {
                    mask: IMask.MaskedRange,
                    from: 1,
                    to: 12
                },
                DD: {
                    mask: IMask.MaskedRange,
                    from: 1,
                    to: 31
                }
            }
        });
        input.select();
    }

    setTimeMask(id) {
        let input = document.getElementById(id);
        if (!input) return;
        const momentFormat = 'HH:mm';
        IMask(input, {
            mask: Date,
            pattern: momentFormat,
            lazy: false,
            min: new Date(1970, 0, 1),
            max: new Date(2030, 0, 1),
            format: date => moment(date).format(momentFormat),
            parse: str => moment(str, momentFormat),
            blocks: {
                YYYY: {
                    mask: IMask.MaskedRange,
                    from: 1900,
                    to: 3000
                },
                MM: {
                    mask: IMask.MaskedRange,
                    from: 1,
                    to: 12
                },
                DD: {
                    mask: IMask.MaskedRange,
                    from: 1,
                    to: 31
                },
                HH: {
                    mask: IMask.MaskedRange,
                    from: 0,
                    to: 23
                },
                mm: {
                    mask: IMask.MaskedRange,
                    from: 0,
                    to: 59
                }
            }
        });
    }

    setPhoneMask(id) {
        let input = document.getElementById(id);
        if (!input) return;
        
        IMask(input, {
            mask: [
                {
                    mask: '+61 000 000 000',
                    startsWith: '+61',
                    lazy: false
                },
                {
                    mask: '0000 000 000',
                    startsWith: '0',
                    lazy: false
                },
                {
                    mask: '0000 000 000',
                    lazy: false
                }
            ],
            dispatch: function(appended, dynamicMasked) {
                let number = (dynamicMasked.value + appended).replace(/\D/g, '');
                
                if (number.startsWith('61')) {
                    return dynamicMasked.compiledMasks[0];
                }
                if (number.startsWith('0')) {
                    return dynamicMasked.compiledMasks[1];
                }
                return dynamicMasked.compiledMasks[2];
            }
        });
    }

    // Overseas validation methods
    overseasMapAddressAuto(input) {
        const inputValue = input.value?.trim();
        const placeEl = document.getElementById('mapAddressAUBillingPlace');
        if (placeEl.value.length < 1) {
            return {
                valid: false,
                message: 'Enter address'
            };
        }
        return true;
    }

    medicareValidatorAPICallback(input) {
        const status = input.value?.trim();
        return status === "success" || status === "success-with-updates" || status === "error";
    }

    overseasValidatorAPICallback(input) {
        const status = input.value?.trim();
        return status === "success" || status === "error";
    }

    // Receipt and pricing validation
    receiptAmount(data) {
        const input = data.value * 100;
        return !(isNaN(input) || (input < 1));
    }

    customPricing(data) {
        const input = data.value;
        const min = data.element.getAttribute('data-min');

        if (input.length === 0 || isNaN(input) || (input < 0)) {
            return {
                valid: false,
                message: 'Enter test fee'
            };
        }

        const minCents = min * 100;
        const inputCents = input * 100;

        if (parseInt(inputCents) < parseInt(minCents)) {
            return {
                valid: false,
                message: 'Min $' + min
            };
        }

        return true;
    }

    // Label validation methods
    validatePrintLabelAmounts(data) {
        const input = data.value;
        const otherElement = document.getElementById('labelSpecimenPrintAmount');

        if (isNaN(input) || (input < 1 && otherElement.value < 1)) {
            return {
                valid: false,
                message: 'Please print at least 1 label'
            };
        }

        if (input > 30) {
            return {
                valid: false,
                message: 'Please enter a number under 30'
            };
        }

        return true;
    }

    validatePrintSpecimenAmounts(data) {
        const input = data.value;
        const otherElement = document.getElementById('labelPrintAmount');

        if (isNaN(input) || (input < 1 && otherElement.value < 1)) {
            return {
                valid: false,
                message: 'Please print at least 1 label'
            };
        }

        if (input > 30) {
            return {
                valid: false,
                message: 'Please enter a number under 30'
            };
        }

        return { valid: true };
    }

    // Date validation methods
    labelTimeValidation(input) {
        if (input.value === '' || !(/^([01][0-9]|2[0-3]):([0-5][0-9])$/.test(input.value))) {
            return { valid: false, message: "Please enter a valid collection time" };
        }

        let inputDate = dayjs(document.getElementById('labelPrintDate').value, "YYYY-MM-DD", true);
        if (!inputDate.isValid()) {
            inputDate = dayjs();
        }
        const inputTime = dayjs(input.value, 'HH:mm', true);
        const inputDateTime = inputDate.hour(inputTime.hour()).minute(inputTime.minute());

        if (inputDateTime.isAfter(dayjs(), "minute")) {
            return { valid: false, message: "Please enter a non-future time" };
        }

        return true;
    }

    labelDateValidation(input) {
        const inputDate = dayjs(input.value, "YYYY-MM-DD", true);

        if (!inputDate.isValid()) {
            return { valid: false, message: "Please enter a valid collection date" };
        } else if (inputDate.isAfter(dayjs(), "day")) {
            return { valid: false, message: "Please enter a non-future date" };
        }

        return true;
    }

    // Modal and promise methods
    modalPromise(modalId, predicate) {
        return () => new Promise(resolve => {
            if (predicate()) {
                return resolve(true);
            }
            const modalEl = document.getElementById(modalId);
            const actionEl = modalEl.querySelector('.modal-action');
            let valid = false;
            const clicked = () => {
                valid = true;
                healiusPortal.modals[modalId].hide();
            };

            const closed = () => {
                actionEl.removeEventListener('click', clicked);
                modalEl.removeEventListener('hidden.bs.modal', closed);
                resolve(valid);
            };

            actionEl.addEventListener('click', clicked);
            modalEl.addEventListener('hidden.bs.modal', closed);
            healiusPortal.modals[modalId].show();
        });
    }

    // Utility methods
    orCallback(...callbacks) {
        return (input) => {
            return callbacks.reduce((result, callback) => {
                if (result.valid) return true;
                return callback(input);
            }, { valid: false });
        };
    }

    ifValid(validator, action) {
        return validator.validate()
            .then(status => {
                if (status === 'Valid') {
                    action();
                    return true;
                }
                return false;
            });
    }

    fieldValidation(validation, field) {
        return {
            validate: () => validation.validateField(field)
        };
    }

    isValid(validator) {
        return validator.validate()
            .then(status => status === 'Valid');
    }

    allValid(validators) {
        return Promise.all(validators.map(this.isValid.bind(this)))
            .then(statuses => statuses.every(Boolean));
    }

    fieldValid(validator, field) {
        return validator.validateField(field)
            .then(status => status === 'Valid');
    }

    // Reset methods
    resetFields(fields = []) {
        fields.forEach(field => {
            this.validators.patient?.resetField(field, false);
        });
    }

    setupBillingValidation() {
        this.setupFormValidation('billingForm', {
            fields: {
                billingType: {
                    validators: {
                        notEmpty: {
                            message: 'Please select a billing option'
                        }
                    }
                },
                medicareValidated: {
                    validators: {
                        callback: {
                            message: 'Validation Status',
                            callback: validationService.medicareValidatorAPICallback
                        }
                    }
                },
                overseasValidated: {
                    validators: {
                        callback: {
                            message: 'Validation Status',
                            callback: validationService.medicareValidatorAPICallback
                        }
                    }
                },
                medicareNumber: {
                    validators: {
                        notEmpty: {
                            message: 'Enter the Medicare card number'
                        },
                        callback: {
                            message: 'Enter valid Medicare card number',
                            callback: validationService.medicareValidatorCallback
                        }
                    }
                },
                medicareIndex: {
                    validators: {
                        notEmpty: {
                            message: 'Select IRN'
                        }
                    }
                },
                medicareCardSighted: {
                    validators: {
                        notEmpty: {
                            message: 'Please make sure you sight the Medicare card'
                        }
                    }
                },
                medicareMBA: {
                    validators: {
                        notEmpty: {
                            message: 'Please agree to assign your rights to Healius to bulk bill Medicare for your pathology tests'
                        }
                    }
                },
                dvaCardType: {
                    validators: {
                        notEmpty: {
                            message: 'Select the Veteran card colour'
                        }
                    }
                },
                dvaNo: {
                    validators: {
                        notEmpty: {
                            message: 'Enter the Veteran card number'
                        },
                        regexp: {
                            regexp: validationService.dvaRegex,
                            message: 'Enter valid Veteran card number',
                        },
                    }
                },
                dvaCardSighted: {
                    validators: {
                        notEmpty: {
                            message: 'Please make sure you sight the Veteran card'
                        }
                    }
                },
                healthFund: {
                    validators: {
                        notEmpty: {
                            message: 'Please select insurer'
                        }
                    }
                },
                healthFundMembershipNumber: {
                    validators: {
                        notEmpty: {
                            message: 'Enter health fund membership number',
                            trim: true
                        }
                    }
                },
                healthFundMembershipReferenceNumber: {
                    validators: {
                        notEmpty: {
                            message: 'Enter reference no',
                            trim: true
                        }
                    }
                },
                healthFundMembershipExpiryForm: {
                    validators: {
                        callback: {
                            message: 'Enter a valid expiry date',
                            callback: validationService.dateValidationRequired
                        }
                    }
                },
                mapAddressAUBilling: {
                    validators: {
                        callback: {
                            message: 'Enter address',
                            callback: validationService.overseasMapAddressAuto
                        }
                    }
                },
                streetAddressAU: {
                    validators: {
                        notEmpty: {
                            message: 'Enter address',
                            trim: true
                        }
                    }
                },
                city: {
                    validators: {
                        notEmpty: {
                            message: 'Enter suburb',
                            trim: true
                        }
                    }
                },
                state: {
                    validators: {
                        notEmpty: {
                            message: 'Select state'
                        }
                    }
                },
                postCode: {
                    validators: {
                        notEmpty: {
                            message: 'Enter postcode',
                            trim: true
                        }
                    }
                },
                mobilePhone: {
                    validators: {
                        notEmpty: {
                            message: 'Enter a valid phone number',
                            trim: true
                        }
                    }
                },
                homeEmail: {
                    validators: {
                        notEmpty: {
                            message: 'Enter a valid email',
                            trim: true
                        },
                        callback: {
                            message: 'The value is not a valid email address',
                            callback: validationService.emailValidationCallback
                        }
                    }
                },
                privateHealthSighted: {
                    validators: {
                        notEmpty: {
                            message: 'Please make sure you sight the health fund card'
                        }
                    }
                },
                privatePatientDetailsMatch: {
                    validators: {
                        notEmpty: {
                            message: 'Please make sure you check the details'
                        }
                    }
                },

            },
            trigger: new FormValidation.plugins.Trigger({
                event: 'input change',
            }),
        });

        validationService.validators.billingForm.on('core.field.validating', function(event) {
            //console.log('checking #billingValidation ', event);

            if (event === "medicareNumber" || event === "medicareIndex") {
                document.getElementById('MEDICARE-mismatch').classList.add('d-none');
                const el = document.querySelector('#billingSection button.action-next-stage');
                el.classList.add('d-none');
            }

            if (event === "healthFund" || event === "healthFundMembershipNumber" || event === "healthFundMembershipReferenceNumber" || event === "privateHealthSighted") {
                document.getElementById('OVERSEAS-mismatch').classList.add('d-none');
                document.getElementById('overseas-autovalidate').querySelector('button').classList.remove('d-none');
                const el = document.querySelector('#billingSection button.action-next-stage');
                el.classList.add('d-none');
            }
        });

        validationService.validators.billingForm.on('core.field.valid', function(event) {
            //console.log('billignForm all fields valid');
            document.getElementById('billingSection').dispatchEvent(new Event("clear-warnings"));
        });

        const formValidation = this.validators.billingForm;
        if (formValidation) {
            formValidation.on('core.field.invalid', function(event) {
                //console.log('Invalid field:', event);
            });

            formValidation.on('core.field.valid', function(event) {
                //console.log('Valid field:', event);
            });
        }
    }

    // Additional setup methods
    setupLabelsFormValidation(labNumbers = []) {
        console.log('labNumbers', labNumbers);
        if (labNumbers.length === 0) {
            return;
        }
        let optionsObject = [];
        labNumbers.forEach(lab => {
            const labNumber = 'labnumber_' + lab + '_status';
            optionsObject[labNumber] = { selector: undefined, validators: { notEmpty: { message: 'Please confirm status' } } };
        });

        optionsObject['labNumberType'] = { selector: undefined, validators: { notEmpty: { message: 'Please select a type' } } };

        const fieldData = Object.assign({}, optionsObject);
        this.setupFormValidation('labelsForm', {
            fields: fieldData,
        });
    }


    // Additional setup methods
    setupPatientVerifiedValidation() {
        this.setupFormValidation('patientVerifiedForm', {
            fields: {
                patientDetailsVerified: {
                    validators: {
                        notEmpty: {
                            message: 'Please confirm patient details'
                        }
                    }
                }
            }
        });
    }

    setupRequestedDateValidation() {
        this.setupFormValidation('requestedDateForm', {
            fields: {
                requestedDateInput: {
                    validators: {
                        callback: {
                            message: 'Request date is required',
                            callback: this.dateValidationRequired.bind(this)
                        }
                    }
                }
            }
        });
    }

    setupLabelsValidation() {
        this.setupFormValidation('labelsPatientManualConfirmation', {
            fields: {
                writeReceiptOnPaper: {
                    validators: {
                        notEmpty: {
                            message: 'Please confirm you have written the payment receipt number on the paper form'
                        }
                    }
                },
                finalizeJob: {
                    validators: {
                        notEmpty: {
                            message: 'Please confirm you have scanned/handled the paperwork as per usual'
                        }
                    }
                }
            },
            plugins: {
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    rowSelector: '.form-check'
                }),
                fieldStatus: new FormValidation.plugins.FieldStatus({
                    onStatusChanged: function(allFieldsValid) {
                        if (allFieldsValid) {
                            document.getElementById('billingSection').dispatchEvent(new Event("clear-warnings"));
                        }
                    }
                }),
            }
        });
    }

    setupLabNumberTypeValidation() {
        this.setupFormValidation('labNumberTypeSelection', {
            fields: {
                labNumberType: {
                    validators: {
                        notEmpty: {
                            message: 'Please choose what type of lab number you require'
                        }
                    }
                }
            },
            plugins: {
                message: new FormValidation.plugins.Message({
                    container: '.lab-number-type-form',
                    clazz: 'd-block invalid-feedback'
                }),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    defaultMessageContainer: false,
                    rowSelector: '.lab-number-type-form'
                })
            }
        });
    }

    paymentInit = () => {
        this.setupFormValidation('paymentCalculatorForm', {
            fields: {},
            plugins: {
                declarative: new FormValidation.plugins.Declarative(),
                trigger: new FormValidation.plugins.Trigger({
                    event: 'blur',
                }),
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
            },
        });
    }


    setupPrintReferralValidation() {
        this.setupFormValidation('printReferralForm', {
            fields: {
                reasonToPrint: {
                    validators: {
                        notEmpty: {
                            message: 'Please enter reason'
                        }
                    }
                }
            }
        });
    }

    setupUpdateContainersValidation() {
        this.setupFormValidation('updateContainersForm', {
            fields: {},
            plugins: {
                declarative: new FormValidation.plugins.Declarative(),
                trigger: new FormValidation.plugins.Trigger({
                    event: 'blur',
                }),
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
            },
        });
    }

    setupSubmissionValidation() {
        this.setupFormValidation('submissionForm', {
            fields: {
                finalizeJob: {
                    validators: {
                        notEmpty: {
                            message: 'Please agree to scan and handle any paperwork as per standard operating procedure'
                        }
                    }
                },
                writeReceiptOnPaper: {
                    validators: {
                        notEmpty: {
                            message: 'Please ensure the payment receipt number has been written on the paper request form'
                        }
                    }
                },
                informFinancialConsent: {
                    validators: {
                        notEmpty: {
                            message: 'Please advise patient of potential fee for criteria-based test(s)'
                       }
                   }
                }
            },
            plugins: {
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
                    autoFocus: new FormValidation.plugins.AutoFocus()
            }
        });
    }

    setupFvAuthorizationModal() {
        this.setupFormValidation('formAuthorisation', {
            fields: {
                username: this.collectorValidation,
                collectorAcknowledgementOfDraw: {
                    validators: {
                        notEmpty: {
                            message: 'Please read and accept the disclaimer before submitting again'
                        }
                    }
                },
            }, plugins: {
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
                autoFocus: new FormValidation.plugins.AutoFocus(),
            },
        });
    }

    setupAuthorizationValidation() {
        this.setupFormValidation('formAuthorisation', {
            fields: {
                username: {
                    validators: {
                        remote: {
                            message: 'The username you\'ve entered does not match the credentials entered to log in to this computer. Please click "Switch account" for details on how to log out of this computer and log in with your credentials',
                            url: (field) => `/api/v1/validator/username/${field.value}`,
                            headers: {
                                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
                            }
                        },
                        notEmpty: {
                            message: 'Enter collector username'
                        }
                    }
                },
                collectorAcknowledgementOfDraw: {
                    validators: {
                        notEmpty: {
                            message: 'Please read and accept the disclaimer before submitting again'
                        }
                    }
                }
            },
            plugins: {
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        });
    }

    // Override init to include all setup methods
    init() {
        console.log('Initializing validation service');
        this.initializeValidators();

        // Only set up validation for forms that exist on the current page
        if (document.getElementById('patientForm')) {
            // Check if this is a commercial form
            const workflowInput = document.querySelector('input[name="workflow"]');
            const isProfileModal = document.getElementById('addPatientProfileModal') !== null;
            console.log('Workflow input found:', workflowInput?.value);
            
            if (isProfileModal) {
                console.log('Setting up patient profile validation');
                if (this.validators.patientForm) {
                    this.validators.patientForm.destroy();
                    delete this.validators.patientForm;
                }
                this.setupPatientProfileValidation();
            } else if (workflowInput && workflowInput.value === 'COMMERCIAL') {
                console.log('Setting up commercial patient validation');
                if (this.validators.patientForm) {
                    this.validators.patientForm.destroy();
                    delete this.validators.patientForm;
                }
                this.setupCommercialPatientValidation();
            } else {
                console.log('Setting up regular patient validation');
                this.setupPatientValidation();
            }
        }

        // Always set up patient verification validation if the form exists
        if (document.getElementById('patientVerifiedForm')) {
            console.log('Setting up patient verification validation');
            this.setupPatientVerifiedValidation();
        }

        if (document.getElementById('submissionForm')) {
            logger.log('Setting up submission validation');
            this.setupSubmissionValidation();
        }


        if (document.getElementById('billingForm')) {
            this.setupBillingValidation();
        }

        if (document.getElementById('updateContainersForm')) {
            this.setupUpdateContainersValidation();
        }


        if (document.getElementById('requestedDateForm')) {
            this.setupRequestedDateValidation();
        }

        if (document.getElementById('labelsPatientManualConfirmation')) {
            this.setupLabelsValidation();
        }
        if (document.getElementById('scanLabelForm')) {
            this.setupLabNumberValidation();
        }
        if (document.getElementById('labNumberTypeSelection')) {
            this.setupLabNumberTypeValidation();
        }
        if (document.getElementById('printReferralForm')) {
            this.setupPrintReferralValidation();
        }
        if (document.getElementById('formAuthorisation')) {
            this.setupFvAuthorizationModal()
        }
        if (document.getElementById('formAuthorisation')) {
            this.setupAuthorizationValidation();
        }
        if (document.getElementById('paymentCalculatorForm')) {
            this.setupPaymentValidation();
        }
    }

    setupPatientValidation() {
        console.warn('Setting up patient validation');
        this.setupFormValidation('patientForm', {
            fields: {
                requestedDate: {
                    validators: {
                        callback: {
                            message: 'Request date is required',
                            callback: this.dateValidationRequired
                        }
                    }
                },
                familyName: {
                    validators: {
                        notEmpty: {
                            message: "Last name is required"
                        }
                    }
                },
                sex: {
                    validators: {
                        notEmpty: {
                            message: "Birth sex is required"
                        }
                    }
                },
                dateOfBirth: {
                    validators: {
                        callback: {
                            callback: this.optionalDateValidation.bind(this, true),
                            message: 'Enter a valid Date of birth'
                        }
                    }
                },
                medicareNo: {
                    validators: {
                        callback: {
                            message: "Incorrect Medicare No",
                            callback: this.medicareValidatorCallback.bind(this)
                        }
                    }
                },
                mobilePhone: {
                    validators: {
                        callback: {
                            message: "Please enter a phone number",
                            callback: validationService.patientMobilePhoneCallback
                        }
                    }
                },
                homePhone: {
                    validators: {
                        callback: {
                            message: "Please enter a phone number",
                            callback: validationService.patientHomePhoneCallback
                        }
                    }
                },
                mapAddressAU: {
                    validators: {
                        callback: {
                            message: "Please enter the address",
                            callback: this.patientAddressCallback.bind(this)
                        }
                    }
                },
                streetAddressAU: {
                    validators: {
                        notEmpty: {
                            message: "Enter address",
                        }
                    }
                },
                city: {
                    validators: {
                        notEmpty: {
                            message: "Enter city",
                        }
                    }
                },
                state: {
                    validators: {
                        notEmpty: {
                            message: "Select state",
                        }
                    }
                },
                postCode: {
                    validators: {
                        notEmpty: {
                            message: "Enter postcode",
                        }
                    }
                },
                dvaNo: {
                    validators: {
                        // regexp: {
                        //     regexp: this.dvaRegex,
                        //     message: "Incorrect DVA No"
                        // },
                        callback: {
                            message: "Incorrect DVA No",
                            callback : this.dvaValidatorCallback.bind(this)
                        }
                    }
                },
                homeEmail: {
                    validators: {
                        callback: {
                            message: 'The value is not a valid email address',
                            callback: this.emailValidationCallback.bind(this)
                        }
                    }
                }
            }
        });

        const patientFormValidator = validationService.validators.patientForm;
        if (patientFormValidator) {
            patientFormValidator.on('core.field.valid', function(event) {
                console.warn('Valid field:', event);
            });

            patientFormValidator.on('core.field.invalid', function(event) {
                console.warn('Invalid field:', event);
            });
        }


    }

    setupLabNumberValidation() {
        this.setupFormValidation('scanLabelForm', {
            fields: {
                preprintedLabNumber: {
                    validators: {
                        callback: {
                            callback: this.validateLabNumber.bind(this),
                            message: 'Invalid lab number'
                        },
                        remote: {
                            message: 'Lab number already in use',
                            url: (field) => `/api/v1/validator/labNumber/${field.value}`,
                            headers: {
                                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
                            }
                        }
                    }
                }
            }
        });
    }

    setupPaymentValidation() {
        this.setupFormValidation('paymentCalculatorForm', {
            fields: {
                // Add payment-specific validation rules here
            },
            plugins: {
                declarative: new FormValidation.plugins.Declarative(),
                trigger: new FormValidation.plugins.Trigger({
                    event: 'blur'
                })
            }
        });
    }

    validateLabNumber(input) {
        const labNumber = input.value.trim();
        if (labNumber.length === 0) {
            return {
                valid: false,
                message: 'Please enter lab number'
            };
        }
        return !!labNumber.match(/^[1-9][0-9]{7}$/);
    }

    setupCommercialPatientValidation() {
        console.log('Setting up commercial patient validation');
        
        // Remove any existing address validation
        delete this.patientAddressCallback;
        
        // Set up input masks
        this.setRequestDateMask('requestedDate');
        this.setDateMask('dateOfBirth');
        
        // First set up the form with only the required fields
        this.setupFormValidation('patientForm', {
            fields: {
                requestedDate: {
                    validators: {
                        callback: {
                            message: 'Request date is required',
                            callback: this.dateValidationRequired
                        }
                    }
                },
                familyName: {
                    validators: {
                        notEmpty: {
                            message: "Last name is required"
                        }
                    }
                },
                // givenName: {
                //     validators: {
                //         notEmpty: {
                //             message: "First name is required"
                //         }
                //     }
                // },
                sex: {
                    validators: {
                        notEmpty: {
                            message: "Birth sex is required"
                        }
                    }
                },
                dateOfBirth: {
                    validators: {
                        callback: {
                            callback: this.dateValidationRequired,
                            message: 'Enter a valid Date of birth'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5(),
                excluded: new FormValidation.plugins.Excluded({
                    excluded: function(field, element) {
                        const excludedFields = [
                            'mapAddressAU',
                            'streetAddressAU',
                            'city',
                            'state',
                            'postCode',
                            'mobilePhone',
                            'homePhone'
                        ];
                        return excludedFields.includes(field);
                    }
                })
            }
        });

        // Add event listeners to track validation attempts
        const formValidation = this.validators.patientForm;
        if (formValidation) {
            console.log('Setting up validation tracking for commercial workflow');
            
            formValidation.on('core.field.validating', function(event) {
                console.log('Field being validated:', event.field, 'Value:', event.value);
            });
            
            formValidation.on('core.field.validated', function(event) {
                console.log('Field validation result:', event.field, 'Valid:', event.valid);
            });
        } else {
            console.warn('Form validation object not found for commercial workflow');
        }
    }

    setupPatientProfileValidation() {
        console.log('Setting up patient profile validation');
        
        // Set up input masks
        this.setDateMask('requestedDate');
        this.setDateMask('dateOfBirth');
        this.setPositiveIntegerMask('medicareNo', 10);
        
        // Initialize phone input with country picker - only if not already initialized
        const phoneInput = document.getElementById('mobilePhone');
        if (phoneInput && !this.phoneInputInstances.has(phoneInput.id)) {
            // Store a reference to any existing instance before creating a new one
            if (window.intlTelInputGlobals) {
                const existingInstance = window.intlTelInputGlobals.getInstance(phoneInput);
                if (existingInstance) {
                    existingInstance.destroy();
                }
            }
            
            // Create new instance with a unique ID
            const iti = window.intlTelInput(phoneInput, {
                initialCountry: 'au',
                separateDialCode: true,
                utilsScript: 'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js',
                preferredCountries: ['au'],
                formatOnDisplay: false, // Change to false to prevent auto-formatting while typing
                autoPlaceholder: 'off' // Disable auto placeholder to prevent focus issues
            });
            
            // Store the instance
            this.phoneInputInstances.set(phoneInput.id, iti);
            
            // Use a separate blur event instead of attaching it to the validation
            phoneInput.addEventListener('blur', () => {
                if (phoneInput.value.trim()) {
                    if (iti.isValidNumber()) {
                        phoneInput.classList.remove('is-invalid');
                        phoneInput.classList.add('is-valid');
                    } else {
                        phoneInput.classList.remove('is-valid');
                        phoneInput.classList.add('is-invalid');
                    }
                }
            });
        }
        
        this.setupFormValidation('patientForm', {
            fields: {
                requestedDate: {
                    validators: {
                        callback: {
                            message: 'Request date is required',
                            callback: this.dateValidationRequired
                        }
                    }
                },
                familyName: {
                    validators: {
                        notEmpty: {
                            message: "Last name is required"
                        }
                    }
                },
                sex: {
                    validators: {
                        notEmpty: {
                            message: "Birth sex is required"
                        }
                    }
                },
                dateOfBirth: {
                    validators: {
                        regexp: {
                            regexp: /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/,
                            message: 'Please enter a valid date of birth (DD/MM/YYYY)'
                        },
                        callback: {
                            callback: this.optionalDateValidation.bind(this, true),
                            message: 'Enter a valid Date of birth'
                        }
                    }
                },
                country: {
                    validators: {
                        notEmpty: {
                            message: "Country is required"
                        }
                    }
                },
                medicareNo: {
                    validators: {
                        regexp: {
                            regexp: /^\d{10}$/,
                            message: 'Please enter a valid Medicare number (10 digits)'
                        },
                        callback: {
                            message: "Incorrect Medicare No",
                            callback: this.medicareValidatorCallback.bind(this)
                        }
                    }
                },
                mobilePhone: {
                    validators: {
                        callback: {
                            message: 'Please enter a valid phone number',
                            callback: (input) => {
                                if (!input.value.trim()) return true;
                                
                                const iti = this.phoneInputInstances.get('mobilePhone');
                                return iti ? iti.isValidNumber() : true;
                            }
                        }
                    }
                },
                mapAddressAU: {
                    validators: {
                        callback: {
                            message: "Please enter the address",
                            callback: this.patientAddressCallback.bind(this)
                        }
                    }
                },
            }
        });
    }
}

// Create and export a singleton instance
const validationService = new ValidationService();