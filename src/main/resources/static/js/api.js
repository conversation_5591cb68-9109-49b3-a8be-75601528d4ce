class RedirectError extends Error {
    constructor(message, url) {
        super(message);
        this.url = url;
    }
}

const healiusAPI = {
    /**
     * @param {Function} object - The callback function to wrap in a Promise
     * @returns {Promise<unknown>}
     */
    getPromise: (object) => new Promise((resolve, reject) => {
        object((error, data) => {
            if (error) {
                reject(error);
            } else {
                resolve(data);
            }
        });
    }),

    getPromiseWithTimeout: (object, ms = 4000) => new Promise((resolve, reject) => {
        const timer = setTimeout(() => resolve(null), ms);

        object((error, data) => {
            clearTimeout(timer);
            if (error) {
                reject(error);
            } else {
                resolve(data);
            }
        });
    }),

    /**
     * Gets a cleaned CSRF token from healiusPortal
     * @returns {string} The cleaned CSRF token
     */
    getCsrfToken: () => healiusPortal?.csrf?.replace(/^"|"$/g, '') || ''
};

class Fetch {
    #csrfToken;
    #baseUrl;

    static {
        this.jsonResponseHandler = response => response.json();
        this.textResponseHandler = response => response.text();
    }

    constructor(csrf, baseUrl = '/api/v1') {
        this.#baseUrl = baseUrl;
        this.#csrfToken = csrf;
    }

    get = (path, responseHandler = Fetch.jsonResponseHandler) =>
        this.#fetch(path, this.options('GET', "plain/text"), responseHandler);

    post = (path, body = {}, responseHandler = Fetch.jsonResponseHandler) =>
        this.#fetch(path, this.options('POST', "application/json", body), responseHandler);

    put = (path, body = {}, responseHandler = Fetch.jsonResponseHandler) =>
        this.#fetch(path, this.options('PUT', "application/json", body), responseHandler);

    patch = (path, body = {}, responseHandler = Fetch.jsonResponseHandler) =>
        this.#fetch(path, this.options('PATCH', "application/json", body), responseHandler);

    delete = (path, responseHandler = Fetch.jsonResponseHandler) =>
        this.#fetch(path, this.options('DELETE', "application/json"), responseHandler);

    timeout = async (path, options, timeout, responseHandler = Fetch.jsonResponseHandler) => {
        const abortController = new AbortController();
        const { signal } = abortController;
        const timer = setTimeout(() => abortController.abort(), timeout);

        try {
            const result = await this.#fetch(path, { ...options, signal }, responseHandler);
            clearTimeout(timer);
            return result;
        } catch (error) {
            clearTimeout(timer);
            throw error;
        }
    }

    options = (method, contentType, body) => ({
        method,
        body: body ? JSON.stringify(body) : undefined,
        headers: {
            "X-CSRF-TOKEN": this.#csrfToken,
            "Content-Type": contentType,
        }
    })

    #fetch = async (path, options, responseHandler) => {
        const response = await fetch(`${this.#baseUrl}/${path}`, options);
        const handledResponse = await this.#errorHandler(response);
        return responseHandler(handledResponse);
    }

    #errorHandler = async (response) => {
        if (response.redirected) {
            throw new RedirectError('No session', response.url);
        }
        if (!response.ok) {
            const json = await response.json();
            throw new Error(json.message);
        }
        return response;
    }
}

class CollectorPortalApi {
    #fetch = new Fetch(healiusAPI.getCsrfToken());
    #collectionRef = new Fetch(healiusAPI.getCsrfToken(), '/collection/ref');

    getAllSpecimens = (orderId, visitId) =>
        this.#collectionRef.get(`${orderId}/${visitId}/orderable`, Fetch.textResponseHandler);

    getAllSpecimensView = (orderId, visitId) =>
        this.#collectionRef.get(`${orderId}/${visitId}/orderable/view`, Fetch.textResponseHandler);

    getTotalCost = (visitId) =>
        this.#fetch.get(`visit/${visitId}/totalCost`, Fetch.textResponseHandler);

    getPatientEmailForReceipt = (orderId) =>
        this.#fetch.get(`order/${orderId}/patientEmailForReceipt`, Fetch.textResponseHandler);

    verifyPatient = (visitId) => {
        const csrfToken = healiusAPI.getCsrfToken();

        return fetch(`/api/v1/visit/${visitId}/patient/verify`, {
            method: 'PATCH',
            headers: {
                "X-CSRF-TOKEN": csrfToken,
                "Content-Type": "application/json",
            }
        });
    }

    getPatientSummary = (orderId) =>
        this.#collectionRef.get(`${orderId}/patient`, Fetch.textResponseHandler);

    savePatient = (orderId, patient) =>
        this.#fetch.put(`order/${orderId}/referredPatient`, patient);

    updateMedicarePatientDetails = (orderId, patient) =>
        this.#fetch.put(`order/${orderId}/medicarePatientDetails`, patient);

    updateBilling = (orderId, billingType,
                     medicareNo, medicareIndex,
                     dvaCardType, dvaNo, dvaSighted, healthFund, healthFundMembershipNo, healthFundExpiry,
                     medicareSighted,
                     privateHealthSighted,
                     privatePatientDetailsMatch,
                     patientEpisodeInitiated,
                     healthFundRefNo,
                     collectorManualVerification,
                     medicareMBA,
                     medicareResponse) =>
        this.#fetch.patch(`order/${orderId}/billing`, {
            billingType,
            medicareNumber: medicareNo,
            medicareIndex,
            medicareCardSighted: medicareSighted,
            dvaCardType,
            dvaNo,
            dvaCardSighted: dvaSighted,
            healthFund,
            healthFundMembershipNumber: healthFundMembershipNo,
            healthFundExpiry,
            privateHealthSighted,
            privatePatientDetailsMatch,
            patientEpisodeInitiated,
            healthFundRefNo,
            collectorManualVerification,
            medicareMBA,
            medicareResponse
        });

    updateBillingPatient = (orderId,
                            streetAddress, streetAddress2,
                            autocompleteStreetAddress, autocompletePlaceId,
                            suburb, state, postCode, homeEmail, mobilePhone, country) =>
        this.#fetch.patch(`order/${orderId}/referredPatient/billing`, {
            streetAddress,
            streetAddress2,
            autocompleteStreetAddress,
            autocompletePlaceId,
            city: suburb,
            state,
            postCode,
            homeEmail,
            mobilePhone,
            country
        });

    commercialTotal = (price) =>
        this.#fetch.post('payment/commercial', price, Fetch.textResponseHandler);

    usePaper = (healiusId, reason) =>
        this.#fetch.patch(`ref/${healiusId}/usePaper`, reason, Fetch.textResponseHandler);

    testSearch = (search) =>
        this.#fetch.post('selectTest/search/orderable', search);

    addSpecimens = (visitId, orderables) => {
        console.log(orderables);
        return this.#fetch.post(`visit/${visitId}/specimen`, orderables, Fetch.textResponseHandler);
    }

    removeSpecimen = (visitId, specimenId) =>
        this.#fetch.delete(`visit/${visitId}/specimen/${specimenId}`, Fetch.textResponseHandler);

    removeMultiCollectSpecimen = (visitId, specimenId) =>
        this.#fetch.delete(`visit/${visitId}/specimen/multi/${specimenId}`, Fetch.textResponseHandler);

    addUnknownSpecimens = (visitId, ids) =>
        this.#fetch.post(`visit/${visitId}/unknownSpecimen`, ids, Fetch.textResponseHandler);

    removeSpecimenGroup = (visitId, specimenGroupId) =>
        this.#fetch.delete(`visit/${visitId}/specimen/group/${specimenGroupId}`, Fetch.textResponseHandler);

    updateStatusSpecimens = (visitId, specimenId, status, isMultiDay) =>
        this.#fetch.post(`visit/${visitId}/specimen/${specimenId}/status${isMultiDay ? '/multi' : ''}`, status, Fetch.textResponseHandler);

    updateIFCStatus = (visitId, specimenId, status) =>
        this.#fetch.post(`visit/${visitId}/specimen/${specimenId}/ifcstatus`, status, Fetch.textResponseHandler);

    setCustomPriceSpecimen = (visitId, specimenId, price, isMultiDay) =>
        this.#fetch.patch(
            `visit/${visitId}/specimen/${specimenId}/price${isMultiDay ? '/multi' : ''}`,
            price,
            Fetch.textResponseHandler
        );

    setSpecimenExternalReceipt = (visitId, specimenId, receiptNumber, receiptAmount) =>
        this.#fetch.patch(
            `visit/${visitId}/specimen/${specimenId}/externalReceipt`,
            { receiptNumber, receiptAmount },
            Fetch.textResponseHandler
        );

    updateRequestedDate = (orderId, requestedDate) =>
        this.#fetch.patch(`order/${orderId}/requestedDate`, requestedDate, Fetch.textResponseHandler);

    emailReceipt = (orderId, visitId, toEmailAddress) =>
        this.#fetch.post(`order/${orderId}/emailReceipt/${visitId}`, toEmailAddress, Fetch.textResponseHandler);

    getCollectorNotes = (visitId) =>
        this.#fetch.get(`visit/${visitId}/collectorNotes`, Fetch.textResponseHandler);

    generateLabNumber = (visitId) =>
        this.#fetch.post(`visit/${visitId}/labNumber`);

    addLabNumber = (visitId, labNumber) =>
        this.#fetch.post(`visit/${visitId}/labNumber/${labNumber}`);

    printBarcode = (date, time, dpi, labNumber, givenName, familyName, dateOfBirth, sex) =>
        this.#fetch.post(`print/barcode/${dpi}`, {
            date, time, labNumber, givenName, familyName, dateOfBirth, sex
        }, Fetch.textResponseHandler);

    printPaperBarcode = (dpi, labNumber, labNumberPlusYear, labNumberCheckDigit) =>
        this.#fetch.post(`print/paperBarcode/${dpi}`, {
            labNumber, labNumberPlusYear, labNumberCheckDigit
        }, Fetch.textResponseHandler);

    printDigitalPass = (patientName, patientId, dpi) =>
        this.#fetch.post(`print/digitalPass/${dpi}`, {
            patientName, patientId
        }, Fetch.textResponseHandler);

    reprintBarcode = (visitId, date, time, dpi, labNumber, givenName, familyName, dateOfBirth, sex) =>
        this.#fetch.post(`print/${visitId}/barcode/${dpi}`, {
            date, time, labNumber, givenName, familyName, dateOfBirth, sex
        }, Fetch.textResponseHandler);

    preManualSubmit = (visitId, writeReceiptOnPaper, stampPaperInformFinancialConsent, patientAgreedService, patientInformedPossibleCharge, doesNotConsentToUploadEHealth, finalizeJob) =>
        this.#fetch.patch(`visit/${visitId}/preSubmit`, {
            writeReceiptOnPaper,
            stampPaperInformFinancialConsent,
            patientAgreedService,
            patientInformedPossibleCharge,
            doesNotConsentToUploadEHealth,
            finalizeJob
        }, Fetch.textResponseHandler);

    preManualSubmitV2 = (visitId, doesNotConsentToUploadEHealth, finalizeJob, writeReceiptOnPaper, informFinancialConsent) =>
        this.#fetch.patch(`visit/${visitId}/preSubmitV2`, {
            doesNotConsentToUploadEHealth,
            finalizeJob,
            writeReceiptOnPaper,
            informFinancialConsent
        }, Fetch.textResponseHandler);

    submitVisit = async (orderId, visitId, sendToPulsar, collector, trainer) => {
        const formData = new FormData();
        formData.append('sendToPulsar', sendToPulsar);
        if (collector) formData.append('collector', collector);
        if (trainer) formData.append('trainer', trainer);

        return this.#fetch.timeout(
            `order/${orderId}/visit/${visitId}`,
            {
                method: 'POST',
                body: formData,
                headers: { "X-CSRF-TOKEN": healiusAPI.getCsrfToken() }
            },
            30000,
            Fetch.jsonResponseHandler
        );
    }

    getAiRequests = (day) =>
        this.#fetch.get(`ai-order-request/${day}`);
}

oldapi = {
    removeReferrer: (orderId) =>
        fetch(`/api/v1/order/${orderId}/referrer`, {
            method: 'DELETE',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

    removeCopyTo: (orderId, referrerId) =>
        fetch(`/api/v1/order/${orderId}/copyTo/${referrerId}`, {
            method: 'DELETE',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

    updateContainers: (visitId, collected) =>
        fetch(`/api/v1/visit/${visitId}/container`, {
            method: 'PUT',
            body: JSON.stringify(collected),
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken(),
                "Content-Type": "application/json",
            }
        }).then(response => response?.ok ? response.json() : null),

    updateCollection: (visitId, collectorNotes, medication, specimenStatuses, patientEpisodeInitiated) =>
        fetch(`/api/v1/visit/${visitId}/collection`, {
            method: 'PATCH',
            body: JSON.stringify({
                collectorNotes: collectorNotes,
                medication: medication,
                specimenStatuses: specimenStatuses,
                patientEpisodeInitiated: patientEpisodeInitiated
            }),
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),

    printBarcode: (date, dpi, labNumber, givenName, familyName, dateOfBirth, sex) =>
        fetch(`/api/v1/print/barcode/${dpi}`, {
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken(),
                "Content-Type": "application/json",
            },
            method: 'POST',
            body: JSON.stringify({
                date, labNumber, givenName, familyName, dateOfBirth, sex
            }),
        }).then(response => response.text()),

    reprintBarcode: (visitId, date, dpi, labNumber, givenName, familyName, dateOfBirth, sex) =>
        fetch(`/api/v1/print/${visitId}/barcode/${dpi}`, {
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken(),
                "Content-Type": "application/json",
            },
            method: 'POST',
            body: JSON.stringify({
                date, labNumber, givenName, familyName, dateOfBirth, sex
            }),
        }).then(response => response.text()),

    generateLabNumber: (visitId) => {
        return fetch(`/api/v1/visit/${visitId}/labNumber`, {
            method: 'POST',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            },
        }).then(response => response?.ok ? response.json() : null);
    },
    generateCollectorQR: (visitId) => {
        return fetch(`/api/v1/visit/${visitId}/labNumber`, {
            method: 'POST',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            },
        }).then(response => response?.ok ? response.json() : null);
    },

    setLabNumber: (visitId, labNumber) => {
        return fetch(`/api/v1/visit/${visitId}/labNumber/${labNumber}`, {
            method: 'POST',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            },
        }).then(response => response?.ok ? response.json() : null);
    },

    generateExtraLabNumber: (orderId) => {
        return fetch(`/api/v1/order/${orderId}/labNumber`, {
            method: 'POST',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }).then(response => response?.ok ? response.json() : null);
    },

    addExtraLabNumber: (orderId, labNumber) => {
        return fetch(`/api/v1/order/${orderId}/labNumber/${labNumber}`, {
            method: 'POST',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }).then(response => response?.ok ? response.json() : null);
    },

    verifyPatient: (visitId) =>
        fetch(`/api/v1/visit/${visitId}/patient/verify`, {
            method: 'PATCH',
            headers: {"X-CSRF-TOKEN": healiusAPI.getCsrfToken()}
        }),

    updateRequestedDate: (orderId, requestedDate) => {
        const formData = new FormData();
        formData.set("requestedDate", requestedDate)
        return fetch(`/api/v1/order/${orderId}/requestedDate`, {
            method: 'PATCH',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            },
            body: formData
        }).then(response => response.text())
    },

    preSubmit: (visitId, doesNotConsentToUploadEHealth, patientAgreedService, patientInformedPossibleCharge) => {
        let formData = new FormData();
        formData.append('doesNotConsentToUploadEHealth', doesNotConsentToUploadEHealth);
        formData.append('patientAgreedService', patientAgreedService);
        formData.append('patientInformedPossibleCharge', patientInformedPossibleCharge);
        return fetch(`/api/v1/visit/${visitId}/preSubmit`, {
            method: 'PATCH',
            body: formData,
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        });
    },

    preManualSubmit: (visitId, writeReceiptOnPaper, stampPaperInformFinancialConsent, patientAgreedService, finalizeJob) => {
        let formData = new FormData();
        formData.append('writeReceiptOnPaper', writeReceiptOnPaper);
        formData.append('stampPaperInformFinancialConsent', stampPaperInformFinancialConsent);
        formData.append('patientAgreedService', patientAgreedService);
        formData.append('finalizeJob', finalizeJob);
        return fetch(`/api/v1/visit/${visitId}/preSubmit`, {
            method: 'PATCH',
            body: formData,
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        });
    },

    commercialTotal: (formData) => fetch(`/api/v1/payment/commercial`, {
        method: 'POST',
        body: formData,
        headers: {
            "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
        }
    }),

    usePaper: (healiusId) =>
        fetch(`/api/v1/ref/${healiusId}/usePaper`, {
            method: 'PATCH',
            body: '',
            headers: {
                "X-CSRF-TOKEN": healiusAPI.getCsrfToken()
            }
        }),
};
