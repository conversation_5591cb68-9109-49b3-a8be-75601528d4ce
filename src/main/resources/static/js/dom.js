class CollectorPortalDOM {
    constructor() {
        // Initialize tooltips
        logger.debug('Initializing tooltips');
        document.querySelectorAll('[data-toggle="tooltip"]').forEach(
            el => new bootstrap.Tooltip(el)
        );

        return this;
    }

    camelize(str) {
        logger.debug('Camelizing string:', str);
        return str.trim().length === 0 ? '' : str
            .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
                index === 0 ? word.toLowerCase() : word.toUpperCase())
            .replace(/\s+/g, '');
    }

    toProperCase(str) {
        if (str.toString().trim().length === 0) {
            return '';
        }
        return str.toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }

    verifyAnyChecked(id, selector) {
        logger.debug('Verifying any checked:', { id, selector });
        return this.verifyAny(id, selector, el => el.checked);
    }

    verifyAllChecked(id, selector) {
        logger.debug('Verifying all checked:', { id, selector });
        return this.verifyAll(id, selector, el => el.checked);
    }

    verifyEntered(id, selector) {
        logger.debug('Verifying entered:', { id, selector });
        return this.verifyAll(id, selector, el => el.value?.trim().length > 0);
    }

    verifyAny(id, selector, predicate) {
        logger.debug('Verifying any:', { id, selector });
        const visible = this.visible(id, selector);
        return visible.length === 0 || visible.some(predicate);
    }

    verifyAll(id, selector, predicate) {
        logger.debug('Verifying all:', { id, selector });
        const visible = this.visible(id, selector);
        return visible.every(predicate);
    }

    visible(id, selector) {
        logger.debug('Getting visible elements:', { id, selector });
        return [...document.getElementById(id).querySelectorAll(selector)]
            .filter(this.isVisible);
    }

    isVisible(el) {
        const isVisible = !!el.offsetParent;
        logger.debug('Checking element visibility:', { element: el, isVisible });
        return isVisible;
    }

    toggle(hideSelector, showSelector) {
        logger.debug('Toggling elements:', { hideSelector, showSelector });
        return this.toggleEl(
            document.querySelectorAll(hideSelector), 
            document.querySelectorAll(showSelector)
        );
    }

    toggleEl(hideElements, showElements) {
        logger.debug('Toggling elements:', { 
            hideCount: hideElements.length, 
            showCount: showElements.length 
        });
        hideElements.forEach(el => el.classList.add('d-none'));
        showElements.forEach(el => el.classList.remove('d-none'));
    }

    displayIf(el, flag) {
        logger.debug('Displaying element conditionally:', { element: el, flag });
        el?.classList.toggle('d-none', !flag);
    }

    isDateEntered(el) {
        const isValid = el.value?.trim().length > 0 &&
            el.value !== '__/__/____' &&
            dayjs(el.value, 'DD/MM/YYYY', true).isValid();
        logger.debug('Checking date entry:', { value: el.value, isValid });
        return isValid;
    }

    addEventListenersTo(parentEl, selector, eventType, listener) {
        logger.debug('Adding event listeners:', { selector, eventType });
        parentEl.querySelectorAll(selector)
            .forEach(el => el.addEventListener(eventType, listener));
    }

    addEventListeners(selector, eventType, listener) {
        logger.debug('Adding event listeners:', { selector, eventType });
        return this.addEventListenersTo(document, selector, eventType, listener);
    }

    setModalAction(modalId, action, label) {
        logger.debug('Setting modal action:', { modalId, action, label });
        document.getElementById(modalId)
            .querySelectorAll('.modal-action')
            .forEach(el => {
                el.addEventListener('click', this.singleClickListener(action));
                if (label) {
                    el.innerHTML = label;
                }
            });
    }

    singleClickListener(listener) {
        return (event) => {
            logger.debug('Handling single click:', { event });
            if (event.detail === 1) {
                listener(event);
            } else {
                event.preventDefault();
            }
        };
    }

    asyncSingleClickListener(listener) {
        return async (event) => {
            logger.debug('Handling async single click:', { event });
            if (event.detail === 1) {
                event.target.disabled = true;
                event.target.disabled = await listener(event);
            } else {
                event.preventDefault();
            }
        };
    }

    switchModals(currentModalId, newModalId) {
        logger.debug('Switching modals:', { currentModalId, newModalId });
        document.getElementById(currentModalId).addEventListener('hidden.bs.modal', () =>
            healiusPortal.modals[newModalId].show(),
            { once: true }
        );
        healiusPortal.modals[currentModalId].hide();
    }

    async showModalElement(modalId) {
        logger.debug('Showing modal:', { modalId });
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                const error = new Error('Modal failed to show within 5 seconds.');
                logger.error('Modal show timeout:', { modalId, error });
                reject(error);
            }, 5000);

            document.getElementById(modalId).addEventListener(
                'shown.bs.modal',
                () => {
                    clearTimeout(timeout);
                    logger.debug('Modal shown successfully:', { modalId });
                    resolve();
                },
                { once: true }
            );

            healiusPortal.modals[modalId].show();
        });
    }

    async hideModalElement(modalId) {
        logger.debug('Hiding modal:', { modalId });
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                const error = new Error('Modal failed to hide within 5 seconds.');
                logger.error('Modal hide timeout:', { modalId, error });
                reject(error);
            }, 5000);

            document.getElementById(modalId).addEventListener(
                'hidden.bs.modal',
                () => {
                    clearTimeout(timeout);
                    logger.debug('Modal hidden successfully:', { modalId });
                    resolve();
                },
                { once: true }
            );
            healiusPortal.modals[modalId].hide();
        });
    }
}

class PropertyRenderer {
    #root;
    #getter;
    #elements;

    constructor(getter, ...selectors) {
        logger.debug('Creating PropertyRenderer:', { getter, selectors });
        this.#root = document;
        this.#getter = getter;
        this.#elements = [];
        selectors.forEach(s => this.addElements(s));
    }

    addElements = (selector) => {
        logger.debug('Adding elements to PropertyRenderer:', { selector });
        this.#root.querySelectorAll(selector)
            .forEach(el => this.#elements = [...this.#elements, el]);
    }

    render = (entity) => {
        logger.debug('Rendering property:', { entity });
        const value = this.#getter(entity);
        this.#elements.forEach(el => {
            if (el.tagName === 'INPUT' || el.tagName === 'SELECT') {
                el.value = value;
            } else {
                el.innerHTML = value;
                if (el.classList.contains('hide-if-empty')) {
                    el.parentElement.classList.toggle('d-none', !value?.trim().length);
                }
            }
        });
    }
}
