export class CollectorPortalDebug {
    constructor() {
        this.constructor.initialize();
    }

    static initialize = () => {
        this.loglevel = 1;

        /*
            5 = All messages

            1 = Only Critical Errors
            0 = No Messages
         */
    }

    static log = (message = '', level = 3) => {
        const stackTrace = (new Error()).stack;
        let stackTraceArray = stackTrace.split(`\n`);
        stackTraceArray.shift();
        stackTraceArray.shift();

        let newStackTraceArray = [];
        let j = 0;
        for (let i = 0; i < stackTraceArray.length; i++) {
            const entry = stackTraceArray[i].replace('at ', '').trim();
            if (entry !== 'NodeList.forEach (<anonymous>)') {
                newStackTraceArray.push(` ${j}: ${entry}`);
                j += 1;
            }
        }

        const caller = newStackTraceArray.join(`\n`);

        if (level < this.loglevel) {
            console.log(`${Date.now()} (level ${level}): ${message} called from:\n${caller}`);
        }
    }

    static error = (message) => {
        this.log(message, 1);
    }
}
