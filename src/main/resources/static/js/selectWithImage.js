let selectObject = null
let selectWithImage = {

    init: () => {

        selectObject = document.getElementById("imagePicker");
        const listElements = selectObject.querySelectorAll('li');
        const selectButton = document.getElementById("select-image");

        listElements.forEach((element) => {
            // set image
            element.innerHTML = `<img src="${element.getAttribute('data-thumbnail')}" alt=""/> <span>${element.innerText}</span>`;

            // change current selection on click on item
            element.onclick = () => {
                // hide selection list
                selectObject.style.display = 'none';

                // set selected
                selectButton.setAttribute('value', element.getAttribute('value'));
                selectButton.innerHTML = element.outerHTML;
                document.getElementById("image").value = element.getAttribute("value");
            };
        });

        selectButton.onclick = () => {
            selectObject.style.display = selectObject.style.display === 'block' ? 'none' : 'block';
        };
    }
}

selectWithImage.containerIcons = {
    BLOOD_ACDYELLOW: 'tubes_acd.svg',
    BLOOD_AEROBICGREEN: 'blood_cultures_blood_culture_bottle_aerobic.svg',
    BLOOD_AEROBICORANGE: 'blood_cultures_aerobic_paediatric.svg',
    BLOOD_CULTURE_TB_MYCO_F: 'blood_culture_tb_myco_f.svg',
    BLOOD_CULTURES_AEROBIC_ANAEROBIC: 'blood_cultures_aerobic_anaerobic.svg',
    BLOOD_CULTURES_BOTTLE_ANAEROBIC: 'blood_cultures_blood_culture_bottle_anaerobic.svg',
    BLOOD_EDTALAVENDER: 'tubes_edta_4ml.svg',
    BLOOD_EDTALAVENDERTRANSLUCENT: 'tubes_edta_2ml.svg',
    BLOOD_EDTAPINK: 'tubes_edta_6ml.svg',
    BLOOD_ESRBUFFEREDCITRATE: 'tubes_esr_buffered_citrate.svg',
    BLOOD_FLUORIDEOXALATE: 'tubes_fluoride_oxalate_4ml.svg',
    BLOOD_LITHIUMHEPARINGREEN6: 'tubes_lithium_heparin_6ml.svg',
    BLOOD_LITHIUMHEPARINGREEN9: 'tubes_lithium_heparin_9ml.svg',
    BLOOD_PLAINRED10: 'tubes_plain_10ml.svg',
    BLOOD_PLAINTRANSLUCENTRED: 'tubes_plain_2ml.svg',
    BLOOD_QUANTIFERONKIT: 'tubes_quanterferon_kit.svg',
    BLOOD_SODIUMCITRATEBLUE: 'tubes_sodium_citrate_2.7ml.svg',
    BLOOD_SODIUMCITRATETRANSLUCENT: 'tubes_sodium_citrate_1.8ml.svg',
    BLOOD_SSTYELLOW3_5: 'tubes_sst_3.5ml.svg',
    BLOOD_SSTYELLOW8_5: 'tubes_sst_8.5ml.svg',
    BLOOD_TRACEELEMENTNAVY: 'tubes_trace_element_6ml_navy.svg',
    BLOOD_STRECK: 'tubes_streck_10ml.svg',
    BLOOD_CSF: 'tubes_csf_blue.svg',
    BLOOD_CSF_RED: 'tubes_csf_red.svg',
    BLOOD_CSF_YELLOW: 'tubes_csf_yellow.svg',
    BLOOD_CLEAR: 'tubes_clear_tube.svg',
    FAECES_BROWN: 'containers_brown.svg',
    FAECES_OCCULTGREEN: 'tubes_faecal_occult_blood.svg',
    FAECES_PFA_CONTAINER: 'containers_faecal_pfa_container_white_lid_.svg',
    TIMED_FAECAL_COLLECTION: 'containers_timed_faecal_collection.svg',
    SWAB: 'swabs_generic_swab.svg',
    SWAB_ORANGE_FLOQ: 'swabs_orange_top_floq_swab.svg',
    SWAB_ORANGE: 'swabs_orange_top.svg',
    SWAB_GREEN: 'swabs_green_top.svg',
    SWAB_BLUE: 'swabs_blue_top.svg',
    SWAB_RED_FLOQ: 'swabs_swab_red_floq.svg',
    ESWAB_BLUE: 'eswabs_blue.svg',
    ESWAB_GREEN: 'eswabs_green.svg',
    ESWAB_ORANGE: 'eswabs_orange.svg',
    ESWAB_WHITE: 'eswabs_white.svg',
    OTHER_BLUE: 'containers_blue.svg',
    OTHER_RED: 'containers_red.svg',
    OTHER_WHITE: 'containers_white.svg',
    OTHER_YELLOW: 'containers_yellow.svg',
    OTHER_GREEN: 'containers_green.svg',
    OTHER_WHITE_THIN: 'containers_thin_prep.svg',
    HISTOLOGY_CONTAINER: 'containers_histology_container_formalin.svg',
    URINE_BLUE: 'containers_timed_urine_container_plain_blue_.svg',
    URINE_GREEN: 'containers_green.svg',
    URINE_YELLOW: 'containers_yellow.svg',
    URINE_PLAIN: 'containers_timed_urine_container_plain.svg',
    URINE_ACID: 'containers_timed_urine_container_acid.svg',
    HR_24: 'containers_timed_urine_container_plain.svg',
    PAEDIATRIC_EDTA: 'paediatric_edta_0.25ml.svg',
    PAEDIATRIC_FLUORIDE_OXALATE: 'paediatric_fluoride_oxalate_0.25ml.svg',
    PAEDIATRIC_LITHIUM_HEPARIN: 'paediatric_lithium_heparin.svg',
    PAEDIATRIC_PLAIN: 'paediatric_plain_0.4ml.svg',
    PAEDIATRIC_SST: 'paediatric_sst_0.2ml.svg',
    PAEDIATRIC_SST_AMBER: 'paediatric_sst_amber.svg',
    TUBES_BIOLOGICAL_INDICATOR: 'tubes_biological_indicator_test_vial_drop_off_only_.svg',
    TUBES_BLOOD_GAS_SYRINGE: 'tubes_blood_gas_syringe.svg',
    TUBES_COBAS_PCR: 'tubes_cobas_pcr_media_dual_kit.svg',
    TUBES_FLUORIDE_OXALATE_2ML: 'tubes_fluoride_oxalate_2ml.svg',
    TUBES_LUKI: 'tubes_luki_tube.svg',
    TUBES_PPT: 'tubes_ppt.svg',
    TUBES_SALIVETTE: 'tubes_salivette.svg',
    VIRAL_TRANSPORT_FLUID_PURPLE: 'viral_transport_fluid_purple.svg',
    VIRAL_TRANSPORT_FLUID_RED: 'viral_transport_fluid_red.svg',
    VIRAL_TRANSPORT_FLUID_YELLOW: 'viral_transport_fluid_yellow.svg',
    OTHER_AMBULATORY_BLOOD_PRESSURE: 'other_ambulatory_blood_pressure_monitor.svg',
    OTHER_BREATHANALYSER: 'other_breathanalyser.svg',
    OTHER_CULTURE: 'other_culture.svg',
    OTHER_HOLTER_MONITOR: 'other_holter_monitor.svg',
    OTHER_HYDROGEN_BREATH_TEST: 'other_hydrogen_breath_test.svg',
    OTHER_NEONATAL_SCREENING_CARD: 'other_neonatal_screening_card.svg',
    OTHER_PYTEST_KIT: 'other_pytest_kit_c_14_urea_breath_test_.svg',
    OTHER_SALIVA_TESTING: 'other_saliva_instant_oral_fluid_drug_testing_ds6.svg',
    OTHER_SLIDES: 'other_slides.svg',
    OTHER_SPORE_STRIPS: 'other_spore_strips.svg',
    OTHER_URGENT_BAG: 'other_urgent_bag.svg'
}

selectWithImage.containerTypeIcons = {
    CONTAINER: 'container.svg',
    FAECES: 'faeces.svg',
    SWABS: 'swabs.svg',
    TUBE_SAMPLE: 'tube_sample.svg',
    URINES: 'urines.svg',
}
