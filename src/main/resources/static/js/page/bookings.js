import BookingApiClient from '../api/booking-api.js';
import '../components/booking-modal-completed.js';
import '../components/booking-modal-not-completed.js';
import '../components/booking-modal-request-code-verified.js';
import '../components/booking-modal-status-change-confirm.js';

// --- Utility Functions ---
function formatDate(date) {
    if (!(date instanceof Date)) date = new Date(date);
    return date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0');
}
function isToday(date) {
    const today = new Date();
    return formatDate(date) === formatDate(today);
}
function isFutureDate(date) {
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return new Date(date) > today;
}
function isPastDate(date) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return new Date(date) < today;
}
function getPatientSearchData(booking) {
    return {
        givenName: booking.patientGivenName || '',
        familyName: booking.patientFamilyName || '',
        dateOfBirth: booking.patientDateOfBirth || '',
        medicareNo: booking.patientMedicareNumber || '',
        mobilePhone: booking.patientPhone || '',
        sex: booking.patientSex || ''
    };
}
function showModal(modal, onShow, onHide) {
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal.querySelector('.modal'));
    if (onShow) onShow(modal, bsModal);
    bsModal.show();
    modal.querySelector('.modal').addEventListener('hidden.bs.modal', () => {
        if (onHide) onHide();
        modal.remove();
    });
}

class BookingsDashboard extends HTMLElement {
    // Modern class fields
    api = new BookingApiClient('');
    bookings = [];
    currentDate = new Date();
    #refreshInterval;
    loading = false;
    #isCommercialModalOpen = false;

    constructor() {
        super();
    }

    connectedCallback() {
        this.render();
        this.loadBookings();
        this.setupEventListeners();
        this.updateDateLabel();
        // Listen for date changes from day-picker
        const dayPicker = this.querySelector('day-picker');
        if (dayPicker) {
            dayPicker.addEventListener('date-change', (e) => {
                const newDate = new Date(e?.detail?.date ?? new Date());
                // Only update if the date is within the allowed range
                const minDate = new Date();
                const maxDate = new Date();
                minDate.setDate(minDate.getDate() - 7);
                maxDate.setDate(maxDate.getDate() + 7);
                
                if (newDate >= minDate && newDate <= maxDate) {
                    this.currentDate = newDate;
                    this.loadBookings();
                }
            });
        }

        // Auto-refresh bookings every 30 seconds, but only for current day
        this.#refreshInterval = setInterval(() => {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const selectedDate = new Date(this.currentDate);
            selectedDate.setHours(0, 0, 0, 0);
            
            // Only refresh if the selected date is today
            if (selectedDate.getTime() === today.getTime()) {
                this.loadBookings(true); // Pass true to indicate this is an auto-refresh call
            }
        }, 30000);
    }

    disconnectedCallback() {
        if (this.#refreshInterval) {
            clearInterval(this.#refreshInterval);
        }
    }

    getSelectedDate() {
        const dayPicker = this.querySelector('day-picker');
        if (dayPicker && dayPicker.value) {
            const selectedDate = new Date(dayPicker.value);
            // Check if the selected date is within the allowed range
            const minDate = new Date();
            const maxDate = new Date();
            minDate.setDate(minDate.getDate() - 7);
            maxDate.setDate(maxDate.getDate() + 7);
            
            if (selectedDate >= minDate && selectedDate <= maxDate) {
                return selectedDate;
            }
        }
        return this.currentDate;
    }

    async loadBookings(isAutoRefresh = false) {
        console.log('[BookingsDashboard] loadBookings called', { isAutoRefresh });
        try {
            // Prevent duplicate calls while loading
            if (this.loading) {
                return;
            }

            this.loading = true;
            const selectedDate = this.getSelectedDate();
            
            // Format the date as YYYY-MM-DD without timezone issues
            const year = selectedDate.getFullYear();
            const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
            const day = String(selectedDate.getDate()).padStart(2, '0');
            const dateString = `${year}-${month}-${day}`;

            const filters = {
                dateOn: dateString,
                locationId: this.getAttribute('data-collection-center-id') ?? '',
                includeCancelled: false
            };

            const response = await this.api.searchBookings(filters, isAutoRefresh);
            const newBookings = response?.payload ?? [];
            
            // Sort bookings by date (earliest first)
            newBookings.sort((a, b) => {
                const dateA = new Date(a.date + 'T' + a.fromTime);
                const dateB = new Date(b.date + 'T' + b.fromTime);
                return dateA - dateB;
            });

            const hasChanged = this.hasBookingsChanged(newBookings);
            if (hasChanged) {
                this.bookings = newBookings;
                this.updateBookingsList();
            }
        } catch (error) {
            this.bookings = [];
        } finally {
            this.loading = false;
            if (!this.loading) {
                this.updateBookingsList();
            }
        }
    }

    hasBookingsChanged(newBookings) {
        if (!Array.isArray(newBookings) || !Array.isArray(this.bookings)) {
            return true;
        }

        if (newBookings.length !== this.bookings.length) {
            return true;
        }

        // Create a map of existing bookings for quick lookup
        const existingBookingsMap = new Map(
            this.bookings.map(booking => [booking.id, booking])
        );

        // Check if any booking has changed
        return newBookings.some(newBooking => {
            const existingBooking = existingBookingsMap.get(newBooking.id);
            if (!existingBooking) {
                return true;
            }

            // Compare relevant fields that would indicate a change
            return (
                newBooking.notCompletedReason !== existingBooking.notCompletedReason ||
                newBooking.status !== existingBooking.status ||
                newBooking.completed !== existingBooking.completed ||
                newBooking.cancelled !== existingBooking.cancelled ||
                newBooking.fromTime !== existingBooking.fromTime ||
                newBooking.date !== existingBooking.date
            );
        });
    }

    render() {
        this.innerHTML = `
            <style>
                .attachment-icon {
                    width: 20px;
                    height: 20px;
                }
                .printer-icon {
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                    filter: brightness(0);
                }
                .print-button {
                    cursor: pointer;
                }
                .print-button.loading {
                    opacity: 0.5;
                }
                .booking-status img {
                    width: 20px;
                    height: 20px;
                    vertical-align: middle;
                }
                .booking-status span {
                    vertical-align: middle;
                }
                .booking-divider {
                    color: var(--bs-primary-border) !important;
                    border: none;
                    border-top: 1px solid var(--bs-primary-border);
                    height: 0;
                    margin-top: 1.5rem;
                    margin-bottom: 1.5rem;
                    opacity: 1;
                }
                .booking-below-open {
                    border-top: 2px solid var(--bs-primary-border) !important;
                }
                .booking-details-bottom-border > td {
                    border-bottom: 2px solid var(--bs-primary-border);
                }
                .status-btn {
                    background-color: var(--bs-primary-fill) !important;
                    color: #fff !important;
                    border: none !important;
                    box-shadow: none !important;
                    font-weight: 500;
                    font-size: 1rem;
                    transition: background 0.2s;
                }
                .status-btn svg {
                    display: inline-block;
                }
                .status-btn:focus, .status-btn:active {
                    background-color: var(--bs-primary-fill) !important;
                    color: #fff !important;
                    outline: none !important;
                    box-shadow: none !important;
                }
            </style>
            <div class="row m-4">
                <div class="d-flex col col-8">
                    <div class="my-auto">
                        <div class="text-primary-text body1 fw-500">
                            <img src="/images/icon/bookings.svg" alt="" class="me-4" />Bookings (<span class="bookings-size">0</span>)
                        </div>
                    </div>
                </div>
                <div class="d-flex col col-4 justify-content-end">
                    <day-picker></day-picker>
                </div>
            </div>
            <div class="patient-bookings row border-top">
                <div style="width:100%">
                    <table class="table custom-table">
                        <thead class="booking-borders">
                        <tr>
                            <th scope="col" class="col-1 text-primary-text caption fw-600 text-uppercase text-nowrap position-relative"><span class="ps-3">Booking ID</span></th>
                            <th scope="col" class="col-1 text-primary-text caption fw-600 text-uppercase text-nowrap">Time</th>
                            <th scope="col" class="col-2 text-primary-text caption fw-600 text-uppercase text-nowrap">Last Name</th>
                            <th scope="col" class="col-2 text-primary-text caption fw-600 text-uppercase text-nowrap">First Name</th>
                            <th scope="col" class="col-3 text-primary-text caption fw-600 text-uppercase text-nowrap">Test</th>
                            <th scope="col" class="col-1 text-primary-text caption fw-600 text-uppercase text-nowrap">Request Code</th>
                            <th scope="col" class="col-2 text-primary-text caption fw-600 text-uppercase text-nowrap">Status</th>
                            <th scope="col" class="col-1 text-primary-text caption fw-600 text-uppercase text-nowrap"></th>
                        </tr>
                        </thead>
                        <tbody id="bookings-list"></tbody>
                    </table>
                </div>
            </div>
        `;
    }

    updateBookingsList() {
        const bookingsList = this.querySelector('#bookings-list');
        const bookingsSize = this.querySelector('.bookings-size');
        
        if (!bookingsList || !bookingsSize) {
            return;
        }

        // Store currently open accordion IDs before update
        const openAccordions = Array.from(this.querySelectorAll('.booking-details:not(.d-none)'))
            .map(el => el.dataset.bookingId);
        
        if (this.loading) {
            bookingsList.innerHTML = `
                <tr>
                    <td colspan="8">
                        <div class="placeholder-glow">
                            <span class="placeholder col-2"></span>
                            <span class="placeholder col-1"></span>
                            <span class="placeholder col-2"></span>
                            <span class="placeholder col-2"></span>
                            <span class="placeholder col-3"></span>
                            <span class="placeholder col-1"></span>
                            <span class="placeholder col-2"></span>
                            <span class="placeholder col-1"></span>
                        </div>
                    </td>
                </tr>
            `;
            bookingsSize.textContent = 0;
            return;
        }

        bookingsSize.textContent = this.bookings?.length ?? 0;
        if (!Array.isArray(this.bookings)) {
            this.bookings = [];
        }
        
        bookingsList.innerHTML = this.bookings.map(booking => this.renderBookingRow(booking)).join('');

        // Reopen previously open accordions
        openAccordions.forEach(bookingId => {
            const detailsRow = this.querySelector(`.booking-details[data-booking-id="${bookingId}"]`);
            const bookingRow = this.querySelector(`.patient-booking[data-id="${bookingId}"]`);
            if (detailsRow && bookingRow) {
                detailsRow.classList.remove('d-none');
                detailsRow.classList.add('booking-details-bottom-border');
                bookingRow.dataset.togglestatus = 'true';
                bookingRow.classList.remove('bg-white');
                bookingRow.classList.add('bg-neutral-fill-weak');
                const toggleOff = bookingRow.querySelector('.toggler.toggle-off');
                const toggleOn = bookingRow.querySelector('.toggler.toggle-on');
                if (toggleOff) toggleOff.classList.add('d-none');
                if (toggleOn) toggleOn.classList.remove('d-none');
            }
        });
    }

    renderBookingRow(booking) {
        const timeString = booking.fromTime;
        const status = booking.cancelled ? 'cancelled' : (booking.completed ? 'completed' : 'booked');
        const todayFormatted = formatDate(new Date());
        const isRecent = booking.date === todayFormatted;
        const bookingDate = new Date(booking.date + 'T00:00:00');
        const isFuture = isFutureDate(bookingDate);
        const isPast = isPastDate(bookingDate);
        // Attachments HTML
        const attachmentsHtml = this.renderAttachments(booking.attachments);
        // Contact info HTML
        const contactHtml = this.renderContactInfo(booking);
        return `
            <tr class="patient-booking patient-booking-${booking.id} bg-white cursor-pointer" data-id="${booking.id}" data-togglestatus="false" data-booking="${status}">
                <td class="body2 position-relative ${isRecent ? 'recent-booking' : ''}"><span class="ps-3">${booking.bookingNumber || booking.id}</span></td>
                <td class="body2 ">${timeString}</td>
                <td class="body2 fw-600">${booking.patientFamilyName || '-'}</td>
                <td class="body2 ">${booking.patientGivenName || '-'}</td>
                <td class="body2 ">${booking.customerTestName || '-'}</td>
                <td class="body2 ">${booking.healiusId}</td>
                <td class="booking-status">
                    ${this.getStatusIcon(booking)}
                    ${this.getStatusText(booking)}
                </td>
                <td>
                    <img class="toggler toggle-off" src="/images/icon/chevron-down-blue.svg" alt="">
                    <img class="toggler toggle-on d-none" src="/images/icon/chevron-up-blue.svg" alt="">
                </td>
            </tr>
            <tr class="booking-details d-none" data-booking-id="${booking.id}">
                <td colspan="8">
                    <div class="details-content p-4">
                        <div class="row">
                            <div class="col-6">
                                <div class="mb-4">
                                    <span class="text-accent-blue-medium body2">Booking</span>
                                    <div class="mt-2 row">
                                        <div class="col-4 text-neutral-text-medium">Test</div>
                                        <div class="col-8 text-neutral-text">${booking.customerTestName || '-'}</div>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <span class="text-accent-blue-medium body2">Patient</span>
                                    <div class="mt-2 row">
                                        <div class="col-4 text-neutral-text-medium">Mobile</div>
                                        <div class="col-8 text-neutral-text">${booking.patientPhone || '-'}</div>
                                    </div>
                                    <div class="mt-2 row">
                                        <div class="col-4 text-neutral-text-medium">Email</div>
                                        <div class="col-8 text-neutral-text">${booking.patientEmail || '-'}</div>
                                    </div>
                                    <div class="mt-2 row">
                                        <div class="col-4 text-neutral-text-medium">Additional Information</div>
                                        <div class="col-8 text-neutral-text">${booking.comment || '-'}</div>
                                    </div>
                                </div>
                            </div>
                            ${attachmentsHtml}
                        </div>
                        ${contactHtml}
                        ${this.renderBookingActions(booking, isRecent, isPast, isFuture)}
                    </div>
                </td>
            </tr>
        `;
    }

    renderAttachments(attachments) {
        if (!attachments || !attachments.length) return '';
        return `<div class="col-6">
            <div class="text-accent-blue-medium body2 mb-2">Attachments</div>
            ${attachments.map(att => `
                <div class="d-flex align-items-center mb-2">
                    <img src="/images/icon/attachment.svg" class="me-2 attachment-icon" alt="attachment icon" />
                    <span class="me-2 flex-grow-1">${att.fileName}</span>
                    <button class="btn btn-link p-0 print-button" data-attachment-id="${att.id}">
                        <img src="/images/icon/printer.svg" class="printer-icon" alt="print" />
                    </button>
                </div>
            `).join('')}
        </div>`;
    }

    renderContactInfo(booking) {
        if (!(booking.contactGivenName || booking.contactFamilyName || booking.contactPhone || booking.contactEmail)) return '';
        return `
            <hr class="booking-divider">
            <div class="row">
                <div class="col-12">
                    <div class="mb-4">
                        <span class="text-accent-blue-medium body2">Additional contact</span>
                        <div class="mt-2 row">
                            <div class="col-4 text-neutral-text-medium">Name</div>
                            <div class="col-8 text-neutral-text">${booking.contactGivenName || ''} ${booking.contactFamilyName || ''}</div>
                        </div>
                        <div class="mt-2 row">
                            <div class="col-4 text-neutral-text-medium">Mobile</div>
                            <div class="col-8 text-neutral-text">${booking.contactPhone || '-'}</div>
                        </div>
                        <div class="mt-2 row">
                            <div class="col-4 text-neutral-text-medium">Email</div>
                            <div class="col-8 text-neutral-text">${booking.contactEmail || '-'}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderBookingActions(booking, isRecent, isPastDate, isFutureDate) {
        const isCompleted = booking.completed === true;
        const isNotCompleted = booking.completed === false && typeof booking.notCompletedReason === 'string' && booking.notCompletedReason.trim().length > 0;
        const tickIcon = `<svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:8px;"><path d="M4.0001 7.7799L1.2201 4.9999L0.273438 5.9399L4.0001 9.66656L12.0001 1.66656L11.0601 0.726562L4.0001 7.7799Z" fill="white"/></svg>`;
        const completedBtnChecked = `<button class="btn btn-primary-fill fw-600 checked" data-action="show-completed-confirm-modal" data-booking-id="${booking.id}">${tickIcon} Completed</button>`;
        const completedBtnNormal = `<button class="btn btn-outline-primary-fill" data-action="show-completed-modal" data-booking-id="${booking.id}">Completed</button>`;
        const notCompletedBtnChecked = `<button class="btn btn-primary-fill fw-600 checked" data-action="show-not-completed-confirm-modal" data-booking-id="${booking.id}">${tickIcon} Not completed</button>`;
        const notCompletedBtnNormal = `<button class="btn btn-outline-primary-fill" data-action="show-not-completed-modal" data-booking-id="${booking.id}">Not completed</button>`;
        const showCompleteButtons = (isRecent || isPastDate) && !isFutureDate;
        const showCommercialPatientBar = isRecent && !isCompleted && !isNotCompleted && !isFutureDate;
        return `
            <div class="d-flex flex-wrap gap-3 mt-4 justify-content-between">
                <div class="d-flex gap-3">
                    ${showCompleteButtons ? `
                        ${isCompleted ? completedBtnChecked : completedBtnNormal}
                        ${isNotCompleted ? notCompletedBtnChecked : notCompletedBtnNormal}
                    ` : ''}
                </div>
                <div class="d-flex gap-3">
                    ${showCommercialPatientBar ? `
                        <button class="btn btn-outline-primary-fill" data-action="commercial-request" data-booking-id="${booking.id}">Commercial request</button>
                        <button class="btn btn-primary-fill" data-action="patient-lookup" data-booking-id="${booking.id}">Patient lookup</button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Handle print button clicks
        this.addEventListener('click', (event) => {
            const printButton = event.target.closest('.print-button');
            if (printButton) {
                event.preventDefault();
                event.stopPropagation();
                const attachmentId = printButton.dataset.attachmentId;
                if (attachmentId) {
                    this.printAttachment(attachmentId);
                }
                return;
            }

            const bookingRow = event.target.closest('.patient-booking');
            if (bookingRow) {
                event.preventDefault();
                event.stopPropagation();
                this.toggleBookingDetails(bookingRow.dataset.id);
                return;
            }

            const target = event.target.closest('button');
            if (!target) {
                return;
            }

            if (target.classList.contains('today-btn')) {
                this.currentDate = new Date();
                this.updateDateLabel();
                this.loadBookings();
            } else if (target.classList.contains('prev-date')) {
                this.currentDate.setDate(this.currentDate.getDate() - 1);
                this.updateDateLabel();
                this.loadBookings();
            } else if (target.classList.contains('next-date')) {
                this.currentDate.setDate(this.currentDate.getDate() + 1);
                this.updateDateLabel();
                this.loadBookings();
            } else if (target.dataset.action) {
                this.handleBookingAction(target.dataset.action, target.dataset.bookingId);
            }
        });
    }

    updateDateLabel() {
        const dateLabel = this.querySelector('.date-label');
        if (dateLabel) {
            const options = { weekday: 'short', day: '2-digit', month: 'short', year: 'numeric' };
            dateLabel.textContent = this.currentDate.toLocaleDateString('en-GB', options);
        }
    }

    toggleBookingDetails(bookingId) {
        // First, close any other open accordions
        const allDetailsRows = this.querySelectorAll('.booking-details:not(.d-none)');
        const allBookingRows = this.querySelectorAll('.patient-booking[data-togglestatus="true"]');
        
        allDetailsRows.forEach(row => {
            if (row.dataset.bookingId !== bookingId) {
                row.classList.add('d-none');
                row.classList.remove('booking-details-bottom-border');
            }
        });
        
        allBookingRows.forEach(row => {
            if (row.dataset.id !== bookingId) {
                row.dataset.togglestatus = 'false';
                row.classList.remove('bg-neutral-fill-weak');
                row.classList.add('bg-white');
                
                row.querySelector('.toggler.toggle-off')?.classList.add('d-none');
                row.querySelector('.toggler.toggle-on')?.classList.remove('d-none');
            }
        });

        const detailsRow = this.querySelector(`.booking-details[data-booking-id="${bookingId}"]`);
        const bookingRow = this.querySelector(`.patient-booking[data-id="${bookingId}"]`);
        const isExpanded = detailsRow && !detailsRow.classList.contains('d-none');
        if (detailsRow) {
            detailsRow.classList.toggle('d-none');
            detailsRow.classList.toggle('booking-details-bottom-border');
        }
        if (bookingRow) {
            bookingRow.dataset.togglestatus = (!isExpanded).toString();
            if (!isExpanded) {
                bookingRow.classList.remove('bg-white');
                bookingRow.classList.add('bg-neutral-fill-weak');
            } else {
                bookingRow.classList.remove('bg-neutral-fill-weak');
                bookingRow.classList.add('bg-white');
            }
            const toggleOff = bookingRow.querySelector('.toggler.toggle-off');
            const toggleOn = bookingRow.querySelector('.toggler.toggle-on');
            if (toggleOff) toggleOff.classList.toggle('d-none');
            if (toggleOn) toggleOn.classList.toggle('d-none');
        }
    }

    getStatusIcon(booking) {
        if (booking.cancelled) {
            return '<img src="/images/icon/decline.svg" alt="" class="me-2">';
        } else if (booking.completed === true) {
            return '<img src="/images/icon/tickGreen.svg" alt="" class="me-2">';
        } else if (booking.completed === false && booking.notCompletedReason && booking.notCompletedReason.length > 0) {
            return '<img src="/images/icon/decline.svg" alt="" class="me-2">';
        }
        return '<img src="/images/icon/calendarBlue.svg" alt="" class="me-2">';
    }

    getStatusText(booking) {
        if (booking.cancelled) {
            return '<span class="text-error-text fw-500 description">Cancelled</span>';
        } else if (booking.completed === true) {
            return '<span class="text-success-text fw-500 description">Collected</span>';
        } else if (booking.completed === false && booking.notCompletedReason && booking.notCompletedReason.length > 0) {
            return '<span class="text-error-text fw-500 description">Not Collected</span>';
        }
        return '<span class="text-info-text-dark fw-500 description">Booked</span>';
    }

    async handleBookingAction(action, bookingId) {
        try {
            switch (action) {
                case 'show-completed-modal':
                    this.showCompletedModal(bookingId);
                    break;
                case 'show-not-completed-modal':
                    this.showNotCompletedModal(bookingId);
                    break;
                case 'show-completed-confirm-modal':
                    this.showCompletedModal(bookingId);
                    break;
                case 'show-not-completed-confirm-modal':
                    this.showNotCompletedModal(bookingId);
                    break;
                case 'patient-lookup':
                    // Only set active booking if the booking is expanded
                    const bookingRow = this.querySelector(`.patient-booking[data-id="${bookingId}"]`);
                    if (bookingRow && bookingRow.dataset.togglestatus === 'true') {
                        await this.handlePatientLookup(bookingId);
                    }
                    break;
                case 'commercial-request':
                    // Only set active booking if the booking is expanded
                    const commercialBookingRow = this.querySelector(`.patient-booking[data-id="${bookingId}"]`);
                    if (commercialBookingRow && commercialBookingRow.dataset.togglestatus === 'true') {
                        this.openCommercialRequestModal(bookingId);
                    }
                    break;
                default:
            }
        } catch (error) {
            this.showError(`Failed to ${action} booking`);
        }
    }

    async handlePatientLookup(bookingId) {
        try {
            const booking = this.bookings.find(b => b.id === bookingId);
            if (!booking) throw new Error('Booking not found');
            const searchData = getPatientSearchData(booking);
            if (!booking.healiusId) {
                localStorage.setItem('patientSearchData', JSON.stringify(searchData));
                const searchParams = new URLSearchParams(searchData);
                window.location.href = `/patient/lookup?${searchParams.toString()}`;
                return;
            }
            const existsResponse = await this.api.checkHealiusId(booking.healiusId);
            if (existsResponse) {
                // await this.api.verifyPatient(booking.visitId);
                this.showRequestCodeVerifiedModal(bookingId, booking.healiusId);
            } else {
                localStorage.setItem('patientSearchData', JSON.stringify(searchData));
                const searchParams = new URLSearchParams(searchData);
                window.location.href = `/patient/lookup?${searchParams.toString()}`;
            }
        } catch (error) {
            const booking = this.bookings.find(b => b.id === bookingId);
            if (booking) {
                const searchData = getPatientSearchData(booking);
                localStorage.setItem('patientSearchData', JSON.stringify(searchData));
                const searchParams = new URLSearchParams(searchData);
                window.location.href = `/patient/lookup?${searchParams.toString()}`;
            } else {
                window.location.href = '/patient/lookup';
            }
        }
    }

    showCompletedModal(bookingId) {
        console.log('[BookingsDashboard] showCompletedModal called', { bookingId });
        const booking = this.bookings.find(b => b.id === bookingId);
        if (!booking) return;
        if (booking.completed) {
            console.log('[BookingsDashboard] showCompletedModal: booking is already completed, showing status change confirm modal');
            this.showStatusChangeConfirmModal(async () => { await this.loadBookings(); }, bookingId);
        } else {
            console.log('[BookingsDashboard] showCompletedModal: booking is not completed, showing completed modal');
            this._showCompletedModalInternal(bookingId);
        }
    }

    _showCompletedModalInternal(bookingId) {
        console.log('[BookingsDashboard] _showCompletedModalInternal called', { bookingId });
        const modal = document.createElement('booking-modal-completed');
        modal.bookingId = bookingId;
        showModal(modal, (modalEl) => {
            modalEl.addEventListener('completed', (e) => {
                console.log('[BookingsDashboard] completed event received from modal', e.detail);
                this.updateBookingStatus(e.detail.bookingId, 'COMPLETED');
            });
        });
    }

    showNotCompletedModal(bookingId) {
        const booking = this.bookings.find(b => b.id === bookingId);
        if (!booking) return;
        const isNotCompleted = booking.completed === false && typeof booking.notCompletedReason === 'string' && booking.notCompletedReason.trim().length > 0;
        if (isNotCompleted) {
            this.showStatusChangeConfirmModal(async () => { await this.loadBookings(); }, bookingId);
        } else {
            this._showNotCompletedModalInternal(bookingId);
        }
    }

    _showNotCompletedModalInternal(bookingId) {
        console.log('[BookingsDashboard] _showNotCompletedModalInternal called', { bookingId });
        const modal = document.createElement('booking-modal-not-completed');
        modal.bookingId = bookingId;
        showModal(modal, (modalEl) => {
            modalEl.addEventListener('not-completed', (e) => {
                console.log('[BookingsDashboard] not-completed event received from modal', e.detail);
                this.updateBookingStatus(e.detail.bookingId, 'NOT_COMPLETED', e.detail.reason);
            });
        });
    }

    showStatusChangeConfirmModal(onConfirmed, bookingId) {
        const modal = document.createElement('booking-modal-status-change-confirm');
        showModal(modal, (modalEl) => {
            modalEl.addEventListener('status-change-confirmed', async () => {
                if (bookingId) await this.updateBookingStatus(bookingId, 'REMOVE_STATUS');
                if (typeof onConfirmed === 'function') onConfirmed();
            }, { once: true });
        });
    }

    showRequestCodeVerifiedModal(bookingId, healiusId) {
        const modal = document.createElement('booking-modal-request-code-verified');
        modal.bookingId = bookingId;
        modal.healiusId = healiusId;
        showModal(modal);
    }

    async updateBookingStatus(bookingId, status, reason = '') {
        console.log('[BookingsDashboard] updateBookingStatus called', { bookingId, status, reason });
        if (!bookingId) {
            console.error('[BookingsDashboard] updateBookingStatus: bookingId is missing or invalid', bookingId);
            return;
        }
        const booking = this.bookings.find(b => b.id === bookingId);
        if (!booking) {
            console.error('[BookingsDashboard] updateBookingStatus: booking not found for id', bookingId);
            return;
        }
        try {
            let updatedBooking;
            if (status === 'REMOVE_STATUS') {
                updatedBooking = {
                    ...booking,
                    completed: false,
                    notCompletedReason: null
                };
            } else {
                updatedBooking = {
                    ...booking,
                    completed: status === 'COMPLETED',
                    notCompletedReason: status === 'NOT_COMPLETED' ? reason : null
                };
            }
            
            await this.api.updateBookingAdmin(booking.id, updatedBooking);
            console.log('[BookingsDashboard] updateBookingStatus: booking updated, now reloading bookings');
            await this.loadBookings();
        } catch (error) {
            this.showError('Failed to update booking status');
        }
    }

    async printAttachment(attachmentId) {
        try {
            // Show loading state
            const printButton = this.querySelector(`[data-attachment-id="${attachmentId}"]`);
            if (printButton) {
                printButton.classList.add('loading');
            }
            window.printJS({
                printable: `/api/v1/bookings/attachments/${attachmentId}.jpg`,
                type: 'image',
                showModal: true,
                modalMessage: 'Loading PDF',
                onPrintDialogClose: () => resolve()
            });
        } catch (error) {
            this.showError(error.message || 'Failed to print attachment');
        } finally {
            // Reset button state
            const printButton = this.querySelector(`[data-attachment-id="${attachmentId}"]`);
            if (printButton) {
                printButton.classList.remove('loading');
            }
        }
    }

    openCommercialRequestModal(bookingId) {
        const booking = this.bookings.find(b => b.id === bookingId);
        if (!booking) return;

        this.#isCommercialModalOpen = true;

        // Create and dispatch a custom event to open the commercial request modal
        const event = new CustomEvent('open-commercial-request', {
            detail: {
                patientDetails: {
                    givenName: booking.patientGivenName || '',
                    familyName: booking.patientFamilyName || '',
                    dateOfBirth: booking.patientDateOfBirth || '',
                    medicareNumber: booking.patientMedicareNumber || '',
                    phone: booking.patientPhone || '',
                    email: booking.patientEmail || '',
                    address: booking.patientAddress || '',
                    suburb: booking.patientSuburb || '',
                    state: booking.patientState || '',
                    postcode: booking.patientPostcode || '',
                    testName: booking.customerTestName || '',
                    requestCode: booking.healiusId || ''
                }
            },
            bubbles: true,
            composed: true
        });
        this.dispatchEvent(event);

        // Listen for modal close
        document.addEventListener('commercial-request-modal-closed', () => {
            this.#isCommercialModalOpen = false;
        }, { once: true });
    }

    showError(message) {
        logger.error(message);
    }

}

customElements.define('bookings-dashboard', BookingsDashboard);