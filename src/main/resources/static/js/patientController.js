export class PatientController {
    #el;

    constructor(id) {
        this.#el = document.getElementById(id);
        logger.info('Patient Controller class initialized');
    }

    addListener = (type, listener) => this.#el.addEventListener(type, listener)

    setupDateMasks = () => {
        logger.info('setupMasks()');
        validationService.setDateMask('dateOfBirth');
    }

    checkAddressValidity = () => {
        logger.info('checkAddressValidity()');

        const countryData = document.getElementById('country').options[document.getElementById('country').selectedIndex].value;

        logger.info('countryData for => ', countryData);

        if (countryData === 'AU') {
            logger.info('checkAddressValidity for => AU');

            // check auto address see if its valid
            validationService.validators.patientForm.validateField('mapAddressAU').then(async function (status) {
                logger.info('mapAddressAU status => ', status);


                // auto address is not valid, so reset this field and check the manual address
                if (status === 'Invalid') {

                    validationService.validators.patientForm.resetField('mapAddressAU', false);

                    const elementsToCheck = ['streetAddressAU', 'city', 'state', 'postCode'];
                    let elementStatus = {};
                    for (const el of elementsToCheck) {
                        elementStatus[el] = await validationService.validators.patientForm.validateField(el).then(function (status) {
                            return status === 'Valid';
                        });
                        validationService.validators.patientForm.resetField(el, false);
                    }

                    logger.info('elementStatus', elementStatus);

                    const allManualFieldsValid = Object.values(elementStatus).every(element => element === true);
                    if (allManualFieldsValid) {

                        document.querySelectorAll('.patient-view-value-address').forEach(el => {
                            const addressElements = [
                                document.getElementById('patientForm').elements['mapAddressAUInput'].value,
                                document.getElementById('patientForm').elements['streetAddress2'].value,
                                document.getElementById('patientForm').elements['city'].value,
                                document.getElementById('patientForm').elements['state'].value,
                                document.getElementById('patientForm').elements['postCode'].value
                            ];

                            logger.info('addressElements', addressElements);

                            const result = [[addressElements[0], addressElements[1], addressElements[2]].filter(n => n).join(', '), addressElements[3], addressElements[4]].join(' ') + ', Australia';;


                            el.innerHTML = result;
                        });
                        collectorPortalPatient.hideAddressAutocomplete("initial-check");
                    } else {

                    }
                } else {
                    document.querySelectorAll('.patient-view-value-address').forEach(el => {
                        el.innerHTML = document.getElementById('patientForm').elements['mapAddressAU'].value + ', Australia';
                    });
                }
            });
        }

        if (countryData === 'Overseas') {
            logger.info('checkAddressValidity for => OS');
            document.querySelectorAll('.patient-view-value-address').forEach(el => {
                el.innerHTML = document.getElementById('patientForm').elements['mapAddressOverseas'].value;
            });
        }

    }

    checkPhoneMobileValidity = () => {
        logger.info('checkPhoneMobileValidity()');
        const homeElement = document.getElementById('homePhone');
        const mobileElement = document.getElementById('mobilePhone');

        const homePhoneValid = this.checkContactNumberValidity('homePhone');
        const mobilePhoneValid = this.checkContactNumberValidity('mobilePhone');

        if (homePhoneValid && mobilePhoneValid) {
            logger.info('scenario where both mobile and phone are valid');

            const hasPhoneNumber = homeElement.value.length > 0;
            const hasMobileNumber = mobileElement.value.length > 0;
            logger.info(homeElement.value.length > 0);

            if (hasPhoneNumber && hasMobileNumber) {
                logger.info('scenario where both mobile and phone are valid and both home and mobile are not empty');
                document.querySelectorAll('.patient-mobile').forEach(el => {
                    el.innerHTML = mobileElement.value;
                    el.parentElement.classList.remove('d-none');
                });

                document.querySelectorAll('.patient-phone').forEach(el => {
                    el.innerHTML = homeElement.value;
                    el.parentElement.classList.remove('d-none');
                });
            }

            if (hasPhoneNumber && !hasMobileNumber) {
                logger.info('scenario where both mobile and phone are valid but home is not empty but mobile is empty');
                document.querySelectorAll('.patient-mobile').forEach(el => {
                    el.innerHTML = '';
                    el.parentElement.classList.add('d-none');
                });

                document.querySelectorAll('.patient-phone').forEach(el => {
                    el.innerHTML = homeElement.value;
                    el.parentElement.classList.remove('d-none');
                });
            }

            if (!hasPhoneNumber && hasMobileNumber) {
                logger.info('scenario where both mobile and phone are valid but home is empty but mobile is not empty');
                document.querySelectorAll('.patient-mobile').forEach(el => {
                    el.innerHTML = '';
                    el.parentElement.classList.add('d-none');
                });

                document.querySelectorAll('.patient-phone').forEach(el => {
                    el.innerHTML = mobileElement.value;
                    el.parentElement.classList.remove('d-none');
                });
            }

            if (!hasPhoneNumber && !hasMobileNumber) {
                logger.info('scenario where both mobile and phone are valid but both home and mobile is empty');
                document.querySelectorAll('.patient-mobile').forEach(el => {
                    el.innerHTML = '';
                    el.parentElement.classList.add('d-none');
                });

                document.querySelectorAll('.patient-phone').forEach(el => {
                    el.innerHTML = '<div class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback fw-400" data-parent="#' + el + '">Missing info</div>';
                    el.parentElement.classList.remove('d-none');
                    console.log('parentEl', el.parentElement);
                });
            }
        }

        if (!homePhoneValid && !mobilePhoneValid) {
            logger.info('scenario where both mobile and phone are invalid');

            document.querySelectorAll('.patient-mobile').forEach(el => {
                el.innerHTML = '';
                el.parentElement.classList.add('d-none');
            });

            document.querySelectorAll('.patient-phone').forEach(el => {
                el.innerHTML = '<div class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback fw-400" data-parent="#' + el + '">Missing info</div>';
                el.parentElement.classList.remove('d-none');
            });
        }

        if (homePhoneValid && !mobilePhoneValid) {
            logger.info('scenario where home is valid, mobile is invalid');

            document.querySelectorAll('.patient-mobile').forEach(el => {
                el.innerHTML = '';
                el.parentElement.classList.add('d-none');
            });

            document.querySelectorAll('.patient-phone').forEach(el => {
                el.innerHTML = homeElement.value;
                el.parentElement.classList.remove('d-none');
            });
        }

        if (!homePhoneValid && mobilePhoneValid) {
            logger.info('scenario where mobile is valid, home is invalid');

            document.querySelectorAll('.patient-mobile').forEach(el => {
                el.innerHTML = '';
                el.parentElement.classList.add('d-none');
            });

            document.querySelectorAll('.patient-phone').forEach(el => {
                el.innerHTML = mobileElement.value;
                el.parentElement.classList.remove('d-none');
            });
        }
    }

    checkContactNumberValidity = async (inputEl) => {
        const result = await validationService.validators.patientForm.validateField(inputEl).then(function (status) {
            return status === 'Valid';
        });
        validationService.validators.patientForm.resetField(inputEl, false);
        return result;
    }
}
