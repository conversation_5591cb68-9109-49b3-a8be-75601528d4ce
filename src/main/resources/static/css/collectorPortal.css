/* ==========================================================================
   Base Styles
   ========================================================================== */
.searchLabNumberYear {
    max-width: 100px;
    min-width: 80px;
}

.envBand .accSelectHeader {
    padding-top: 58px;
}

.bodyContent {
    padding-left: 40px !important;
    padding-right: 40px !important;
}

.bodyContentOrderableTable {
    padding-left: 40px !important;
}

.bodyContentOff {
    margin-left: -40px !important;
    margin-right: -40px !important;
}

.comma:not(:empty):before {
    content: ", ";
}

.comma {
    position: relative;
    left: -7px;
}

/* ==========================================================================
   Layout & Structure
   ========================================================================== */
.referrer-actions {
    text-align: right;
    padding-right: 32px;
}

.orderableTableAction {
    width: 40px;
    text-align: center;
}

.orderableTableHeaderAction {
    padding-right: 40px;
}

#headerMenu {
    border-bottom-right-radius: 42px;
}

.headerMenuScroll {
    box-shadow: 10px 3px 20px 0 rgba(0, 0, 0, 0.2);
    transition-duration: 400ms;
}

.envTopBand + .sideBar {
    height: calc(100vh - 58px);
}

.mainContentACC {
    margin-left: 0 !important;
}

.envBand .mainContent main {
    margin-top: 125px;
}

.mainContentError {
    margin-top: 125px !important;
}

main.disablePrintButtonPatent {
    margin-top: 120px !important;
}

main.disablePrintButtonPatent.disablePrintButtonPatentView {
    margin-top: 130px !important;
}

.acc-envBand {
    width: 226px;
    position: fixed;
    left: 12px;
    bottom: 12px;
}

/* ==========================================================================
   Buttons & Interactive Elements
   ========================================================================== */
.btn-outline-primary-fill {
    border-color: var(--bs-primary-fill);
    color: var(--bs-primary-fill);
}

.btn-outline-primary-fill:hover {
    border-color: var(--bs-primary-fill);
    background-color: var(--bs-primary-fill);
    color: #FFFFFF;
}

.btn-outline-primary-fill:hover img,
.btn-outline-error-text:hover img,
.restore-declined-tests:hover img {
    filter: brightness(0) invert(1);
}

.hover-white-text:hover {
    color: #FFFFFF !important;
}

.restore-declined-tests {
    background-color: #FFFFFF;
    color: var(--bs-primary-fill);
}

.restore-declined-tests:hover {
    background-color: var(--bs-primary-fill);
    color: #FFFFFF;
}

/* ==========================================================================
   Tables & Lists
   ========================================================================== */
tbody {
    border-color: transparent;
}

.searchContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 492px);
}

.scanContainerEmpty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 492px);
}

/* ==========================================================================
   Forms & Inputs
   ========================================================================== */
.search::placeholder {
    color: var(--bs-neutral-text);
}

input:-webkit-autofill,
input:-webkit-autofill:active,
input:-webkit-autofill:focus,
input:-webkit-autofill:hover {
    -webkit-animation: forwards autofill;
    animation: forwards autofill;
}

input:focus,
textarea:focus,
select:focus {
    outline: none !important;
    box-shadow: none !important;
}

.form-control::-moz-placeholder {
    color: #a6adb3;
    opacity: 1;
}

.form-control::placeholder {
    color: #a6adb3;
    opacity: 1;
}

.form-control:-ms-input-placeholder {
    color: #a6adb3;
}

.form-control::-ms-input-placeholder {
    color: #a6adb3;
}

.form-control[readonly] {
    background-color: white;
}

.form-switch {
    height: 24px;
    min-height: 24px;
}

.form-switch input {
    border-width: 1px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3.5' fill='%23FFFFFF'/%3e%3c/svg%3e") !important;
}

.form-switch input:checked {
    background-color: var(--bs-primary-fill) !important;
    border-color: var(--bs-primary-fill) !important;
}

.form-switch input:not(:checked) {
    background-color: var(--bs-primary-fill-medium) !important;
    border-color: var(--bs-primary-fill-medium) !important;
}

.form-switch input.form-check-input {
    width: 48px;
    height: 24px;
}

.form-switch .switch-label-disabled {
    color: #CCCCCC !important;
}

#searchForm .form-switch input:checked {
    background-color: #CF2B2B !important;
    border-color: #CF2B2B !important;
}

#searchForm .form-switch input:not(:checked) {
    background-color: #0863EB !important;
    border-color: #0863EB !important;
}

/* ==========================================================================
   Navigation & Menu
   ========================================================================== */
.healiusLogo {
    padding-left: 11px;
}

.dropdown-menu li {
    padding: 0 8px;
}

.menuItemHeader {
    padding: 8px 10px;
}

.menuItemHeaderItem:hover {
    background-color: var(--bs-primary-fill-weak) !important;
    border-radius: 8px;
}

.dropdown-item {
    background-color: white !important;
    border-radius: 8px !important;
}

.dropdown-item:hover {
    background-color: var(--bs-primary-fill-weak) !important;
}

.menuItemHr {
    background-color: var(--bs-primary-border) !important;
    margin: 4px 0;
}

.menu,
.menu .sideBar {
    width: 250px !important;
    max-width: 250px !important;
    transition: width 0.5s linear, max-width 0.5s linear !important;
}

.menu.minimized,
.menu.minimized .sideBar {
    width: 63px !important;
    max-width: 63px !important;
}

.menu.minimized section {
    display: none;
}

.menu.minimized .sideBarItem {
    width: 50px;
}

.menu.minimized .sideBarItem span {
    display: none;
}

.menu.minimized .bigLogo {
    display: none !important;
}

.menu.minimized .smallLogo {
    display: block !important;
}

/* ==========================================================================
   Sidebar & Menu Items
   ========================================================================== */
.active {
    background: var(--bs-primary-fill-medium);
    border-radius: 8px;
}

.sideBarMenuItem svg,
.sideBarMenuItem img {
    fill: var(--bs-neutral-text-weak);
    margin: 11px 6px 11px 11px;
}

.sideBarMenuItem.active svg,
.sideBarMenuItem.active img {
    fill: var(--bs-primary-fill);
}

.sideBarMenuItem a svg,
.sideBarMenuItem a img {
    margin-left: 11px;
    margin-right: 8px;
}

.versionTag {
    padding-left: 11px;
}

.menuItem {
    background: var(--bs-primary-fill);
    box-shadow: 2px 3px 11px 0 var(--bs-primary-fill);
}

.sideBarMenuItem {
}

/* ==========================================================================
   Buttons & Interactive Elements
   ========================================================================== */
.btn:disabled {
    pointer-events: auto !important;
}

/* ==========================================================================
   Tables & Lists
   ========================================================================== */
.searchContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 492px);
}

.scanContainerEmpty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 492px);
}

tr.accordion-toggle:not(.collapsed) {
    background-color: var(--bs-neutral-fill-weak);
    border-bottom: 1px solid var(--bs-neutral-fill-weak) !important;
}

.sidBarBadge {
    background-color: var(--bs-accent-brand-fill);
    border-radius: 13px;
    padding: 4px 8px 4px 8px;
}

/* ==========================================================================
   Modals & Overlays
   ========================================================================== */
#printJS-Modal div {
    color: #fff;
}

.authorization-modal {
    width: 720px !important;
    max-width: 720px !important;
}

.small-modal {
    max-width: 400px;
}

.medium-modal {
    max-width: 500px;
}

.logoutModal {
    max-width: 375px;
}

.iti {
    display: inline-grid !important;
    width: 100% !important;
}

.disablePrintButtonPatent .disablePrintButton {
    z-index: 10000;
    position: absolute;
    top: 148px;
}

.disablePrintButtonPatent .disableBackButton {
    z-index: 10000;
    position: absolute;
    top: 124px;
}

#attachmentViewer .modal {
    height: 90%;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */
.cursor-pointer {
    cursor: pointer;
}

.clearButtonStyle {
    background: 0 0;
    border: none;
    padding: 0;
    margin: 0;
}

/* ==========================================================================
   Map & Location
   ========================================================================== */
input#mapAddressAUBilling {
    width: calc(100% + 16px);
}

/* ==========================================================================
   Clearer Elements
   ========================================================================== */
.x-clearer {
    float: left;
    position: relative;
    left: 100%;
    margin-left: -36px;
    top: -40px;
    margin-bottom: -40px;
    width: 24px;
    height: 24px;
}

.autocomplete-clearer {
    margin-left: -20px !important;
}

/* ==========================================================================
   Component Specific Styles
   ========================================================================== */
.component-container {
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
}

.component-container.rendered {
    opacity: 1;
}

.search-results {
    position: absolute;
    max-height: 500px;
    background: white;
    z-index: 1000;
    overflow-x: hidden;
    overflow-y: scroll;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.15);
}

.search-results-original {
    max-height: 730px;
    overflow-y: hidden;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
}
.cc-match {
    cursor: pointer;
}

/* ==========================================================================
   Loading States
   ========================================================================== */
.loading-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    z-index: 1;
}

.component-loading {
    position: relative;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    transition: opacity 0.4s ease-in-out;
}

/* ==========================================================================
   Animations
   ========================================================================== */
@keyframes autofill {
    100% {
        background: 0 0;
        color: inherit;
    }
}

@-webkit-keyframes autofill {
    100% {
        background: 0 0;
        color: inherit;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.fade-in {
    animation: fadeIn 0.4s ease-in-out forwards;
}

.fade-out {
    animation: fadeOut 0.4s ease-in-out forwards;
}

/* ==========================================================================
   Additional Styles
   ========================================================================== */
.orderable-match-highlight{
    background-color: var(--bs-info-fill);
}

.checkbox-lg .fv-plugins-message-container.invalid-feedback {
    padding-left: 8px;
}

.panelAddedText {
    border-radius: 8px;
    padding: 8px 16px;
    margin-right: 8px;
    background-color: #c3ddf2;
    margin-bottom: 8px;
    width: fit-content;
}
.panelNotFound {
    border-radius: 8px;
    padding: 8px 16px;
    margin-right: 8px;
    background-color: var(--bs-warning-fill);
    margin-bottom: 8px
}

.panelAddedText div {
    line-height: 24px
}

.panelScrollContent {
    overflow: auto;
    max-height: 48vh
}

.orderableSelectable, .orderableSelectableEmpty {
    padding: 16px;
    border-radius: 8px;
    cursor: pointer;
}

.orderableSelectable:hover {
    background-color: var(--bs-neutral-fill-weak) !important
}

.orderableSelectable {
    background-color: var(--bs-primary-fill-weak) !important
}

.orderableSelectable:focus {
    background-color: #e7f0f8 !important
}

.orderableSelectable:has(.row-synonyms.d-none) {
    background: white !important;
}

.chevron-button {
    background: transparent;
}

.blueDotWithCounter {
    background-color: var(--bs-primary-fill);
    color: white;
    width: 32px;
    height: 32px;
    text-align: center;
    vertical-align: middle;
    line-height: 32px;
    border-radius: 100%;
    min-width: 32px;
}

.register-eReferral {
    color: #007DB5;
}

.requestDate {
    color: #6d6e71 !important;
    opacity: .85
}

.kebab {
    margin: auto 0;
}

.clearAddPanel {
    padding: 17px 0;
    right: 21px;
    top: 0
}

.removeNotFoundPanel:hover svg {
    background-color: var(--bs-warning-fill-strong);
    border-radius: 4px
}

.removeNotFoundPanel:hover path {
    fill: #333F48
}

.panelMappingIcon {
    left: 16px;
    padding: 16px 0
}

.panelMappingInput {
    line-height: 22px;
    padding-left: 48px
}

.panelDollarInput {
    line-height: 22px;
    padding-left: 16px
}

.commonRow {
    flex-direction: row;
    align-items: stretch;
    height: 100%
}

.commonPanel {
    flex-direction: column;
    justify-content: center;
    display: flex;
    align-items: start;
    flex: 1 0 21%;
    background-color: #eff4f8;
    margin-bottom: 16px;
    border-radius: 8px;
    padding-left: 16px;
    cursor: pointer;
}

.commonPanel:hover {
    background-color: #dbeaf4
}

.submitted-visit-collector-summary {
    min-width: 220px
}

.status-label-toFollow {
    color: var(--bs-warning-fill-strong);
    margin: auto 0;
}

select.specimen-status {
    color: var(--bs-neutral-text-weak)
}

select.specimen-status[data-status='TO_FOLLOW'],
select.specimen-status[data-status='DECLINE_TO_COLLECT'],
select.specimen-status[data-status='DECLINE_TO_PAY'],
select.specimen-status[data-status='TEST_NOT_AVAILABLE'] {
    color: var(--bs-error-fill-strong)
}

.toast-container {
    pointer-events: all !important
}

.fixedHeaderBodyFixPlusError {
    padding-top: 134px
}

.disableInputScreenAllow {
    z-index: 1000;
    height: 100vh;
    position: fixed;
    background-color: rgb(128, 128, 128);
    opacity: 0.1;
    margin-top: 122px;
}

.disableInputScreenAllowPrint {
    padding-top: 136px
}

.disableInputScreen {
    background-color: #fff;
    z-index: 999
}

.header-status-text {
    line-height: 56px
}

.inputDollar {
    position: relative;
    background-image: url(/images/icon/ausDollar.svg);
    background-repeat: no-repeat;
    background-position: 12px 50%;
    background-size: 9px;
    padding-left: 29px
}

#collected-samples-modal input {
    width: 100px;
    margin-right: 16px
}

#collected-samples-modal .modal-body {
    padding-top: 0
}

.specimensOther, .tubesCIT, .tubesEDTA, .tubesFluoride, .tubesHEP, .tubesPlain, .tubesSST {
    border-radius: 4px
}
.referrerAndCopyToFrame {
    /*min-height: 156px;*/
    padding-top: 16px;
}

.blue-border-start {
    border-left: 1px solid #9cf
}

.red-border-start {
    border-left: 2px solid #fc8ba7
}

#search-container {
    position: absolute;
    max-height: 500px
}

.cc-match-highlight {
    color: var(--bs-accent-blue-medium);
}

.table-test-header {
    border-bottom: 1px solid var(--bs-primary-border)
}

.table-test-item {
    border-top: 1px solid var(--bs-primary-border)
}

.collected-green, .status-COLLECTED {
    color: #0c782e;
}

.status-DECLINE_TO_PAY, .status-DECLINE_TO_COLLECT {
    color: #CF2B2B;
}

.status-TO_FOLLOW {
    color: #F56305;
}

.accordion-toggle.collapsed svg {
    transform: rotate(0deg);
}

.accordion-toggle svg {
    transform: rotate(-180deg);
}

.waitingRoomAction{
    background-color: white;
    border-radius: 4px;
    height: 44px;
    line-height: 44px;
}

.waitingRoomRow:hover .btn-outline-primary-fill{
    border-color: var(--bs-primary-fill);
    background-color: var(--bs-primary-fill);
    color: #FFFFFF;
}

.pac-container {
    z-index: 2000;
}

.pac-container .pac-item {
    padding: 0.5rem;
    background: #fff;
    color: #000;
    font-weight: bold;
    transition: all ease-in-out 0.3s;
}

.pac-logo:after {
    display: none !important;
}

.page-picker {
    max-width: 120px;
    z-index: 1000;
    height: 100%;
    overflow: hidden;
}

.page-picker:before {
    content: '';
    position: absolute;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5), transparent, transparent, transparent, transparent, rgba(0,0,0,0.5));
    height: 100%;
    width: 100%;
    pointer-events: none;
}

.page-scroller {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
}

.collection-header-container {
    background-color: var(--bs-primary-fill);
    max-height: 206px;
}

.collection-header-patient-details{
    background-color: #FFF;
    border-bottom-right-radius: 50px;
    line-height: 88px;
    padding: 22px 0;
}
.collection-header-patient-details-float{
    background-color: var(--bs-white-rgb);
}

.collection-header-order-status {
    padding-top: 32px;
    padding-bottom: 32px;
    color:white;
}

.hs-badge {
    padding: 8px 16px;
    color: var(--bs-neutral-text);
    line-height: 22px;
    border-radius: 8px;
}

.view-request-accordion {
    padding: 16px 40px;
    background-color: var(--bs-primary-fill-medium);
    cursor: pointer;
}

.visit-details .view-request-accordion,
.visit-details .accordion-button {
    background-color: var(--bs-success-fill);
}

.visit-details.visit-details-ABANDONED .view-request-accordion,
.visit-details.visit-details-ABANDONED .accordion-button {
    background-color: var(--bs-warning-fill);
}

.visit-details .section-icon {
    background-color: var(--bs-success-graphic) !important;
}

.visit-details.visit-details-ABANDONED .section-icon {
    background-color: var(--bs-warning-graphic) !important;
}

.view-request-accordion-content {
    overflow:hidden;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
}

.cp-panel {
    background: white;
}
.cp-panel:not(:last-child) {
    border-bottom: 1px solid var(--bs-primary-border) !important;
}

.cp-panel:last-child {
    padding-bottom: 32px;
}

#labelsSection .cp-panel:last-child {
    padding-bottom: 0 !important;
}

.specimen-row{
    min-height: 73px;
}
.specimen-row:not(:last-child) {
    border-bottom: 1px solid var(--bs-primary-border) !important;
}

.read-only .specimen-row {
    min-height: 43px !important;
}

.sticky-header {
    top: 0;
    left: 0;
    width: -webkit-fill-available;
    z-index: 100;
    border-bottom: 1px solid var(--bs-primary-border);
    border-left: 1px solid var(--bs-primary-border);
    box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.08);
    margin-left: 250px;
}

#collectingAccordion .accordion-button::after {
    margin-left: 0;
}

.section-icon {
    border-radius: 50%;
    padding: 8px;
}

.section-icon-collecting {
    border-radius: 43px;
    padding: 8px;
}

.section-icon-tick {
    border-radius: 50%;
    padding: 4px;
}

.avatar-lock {
    padding: 6px;
    height: 32px;
    width: 32px;
    border: solid 1px white;
}

.coming-soon-tag {
    position: absolute;
    right: 0;
    padding: 6px 24px;
    font-weight: 700;
    line-height: 22px;
}

.payment-modal-separator-start {
    padding-left: 40px;
}
.payment-modal-separator-end {
    padding-right: 40px;
}

.manual-charge-container{
    padding-bottom: 40px;
}

.icon-20 {
    height: 20px;
    width: 20px;
}

.stage-control-container {
    height: 80px;
}

.accordion-header {
    padding: 16px 24px;
}

#containers .accordion-body {
    border-top: 1px solid var(--bs-primary-border);
}

.labNumberDropDown{
    min-width: 125px;
}

.panelAddedTextTruncate{
    max-width: 305px;
}

/*TOOL TIP*/
.tooltipCustom {
    position: relative;
    display: inline-block;
}

.tooltipCustom .tooltiptext {
    visibility: hidden;
    width: 250px;
    background-color: var(--bs-neutral-text);
    color: var(--bs-white);
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: 0;
    margin-left: 40px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.tooltipCustom .tooltiptext::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    margin-left: -10px;
    margin-top: 10px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent #555 transparent transparent;
}

.tooltipCustom:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
/*TOOL TIP*/

/* MODAL MD*/
.modal-md {

}

.dashboard-cards {
    min-height: 132px;
}

/* MODAL MD*/

.special-numbers {
    border-radius: 50%;
    min-width: 32px;
    height: 32px;
    padding-left: 11px;
    padding-top: 5px;
    width: 32px;
}

/* replacing inline styles */
.specimensLabels {
    padding-left: 16px !important;
}

#lab-number-header {
    min-width: 125px;
}

#lab-number-header a#labNumberDropdown {
    min-width: 125px;
    text-align: left;
}

div.view-request-accordion div.position-absolute {
    right:100px;
    z-index : 90
}

#reviewedScansContainer .accordion-collapse .row {
    height: 400px;
}

div.collecting-stage div.view-request-accordion button.edit-accordion-button span.section-icon-collecting {
    border-radius: 43px;
}

#paymentForm #payment-form {
    width: 400px;
}

.right-z-index {
    right:100px;
    z-index: 90;
}

.bg-c92222 {
    background-color: #c92222;
}

.color-cfb0b0 {
    color: #cfb0b0;
}

.panelScrollContent.row {
    overflow-x: hidden;
}

div.search-results hr {
    height: 2px;
}

div#panelScrollContent {
    border-style: solid;
    overflow-x: hidden;
}

form.referrer-search-form input.has-clear {
    padding-right: 40px;
}

input#mapAddressAUBilling {
    width: calc(100% + 16px);
}

.x-clearer {
    float: left;
    position: relative;
    left: 100%;
    margin-left: -36px;
    top: -40px;
    margin-bottom: -40px;
    width: 24px;
    height: 24px;
}

.autocomplete-clearer {
    margin-left: -30px !important;
}

section#referrerSection button.btn:hover img,
section#referrerSection button.btn:active img {
    filter: brightness(0) invert(1);
    -webkit-transition: -webkit-filter 0.15s ease-in-out;
    -moz-transition: -moz-filter 0.15s ease-in-out;
    -moz-transition: -ms-filter 0.15s ease-in-out;
    -ms-transition: -webkit-filter 0.15s ease-in-out;
    -o-transition: -o-filter 0.15s ease-in-out;
    transition: filter 0.15s ease-in-out, -webkit-filter 0.15s ease-in-out;

}

#patientModal .autocomplete-clearer {
    margin-left: -36px !important;
}

.background-error-fill {
    background-color: var(--bs-error-fill);
}

/*
    forces the arrow at the bottom of the tooltip to be centered and not fight against an "incorrect" bounding box
*/
.labels-tooltip {
    width: 201px;
}

.max-w-40px {
    max-width: 40px;
}

.mx--1 {
    margin-left: -0.25rem !important;
    margin-right: -0.25rem !important;
}

.mx--2 {
    margin-left: -0.5rem !important;
    margin-right: -0.5rem !important;
}

.my--1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
}

.my--2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
}

.specimen.joined-specimen {
    border-bottom-width: 0 !important;
}

.max-w-120px {
    max-width: 120px !important;
}

.min-w-85px {
    min-width: 85px !important;
}

.cp-container {
    width: 24px;
    height: 24px;
    padding-top: 0.125rem;
}

.cp-container-purple .cp-container {
    background-color: #9e3494;
    color: #FFFFFF;
}

.cp-container-yellow .cp-container {
    background-color: #F6D548;
    color: #333F48;
}

.cp-container-red .cp-container {
    background-color: #C3445B;
    color: #FFFFFF;

}

.cp-container-grey .cp-container {
    background-color: #5F6972;
    color: #FFFFFF;
}

.main-content {
    overflow-x: hidden !important;
}

tr td.recent-booking:first-of-type:before,
tr th.recent-booking:first-of-type:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 100%;
    background-color: var(--bs-info-graphic);
}

.patient-booking {
    border-top: 1px solid var(--bs-primary-border);
    border-bottom: 1px solid var(--bs-primary-border);
}

.ai-tests-gradient {
    background: linear-gradient(90deg, var(--bs-info-text-light, #0863EB) 0%, var(--bs-accent-brand-fill, #ED255C) 33%, var(--bs-info-text-light, #0863EB) 66%, var(--bs-accent-brand-fill, #ED255C) 100%);
    background-size: 300% 300%;
    animation: gradient 5s linear infinite;
    animation-direction: alternate;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 100% 50%;
    }
}

.results-unit {
    top: -27px;
}

input[type=radio].is-invalid {
    pointer-events: none;
    appearance: none;
    webkit-appearance: none;
    border: 1px solid red;
    border-radius: 50%;
    padding: 0.37rem;
}

.ps-1-5 {
    padding-left: 0.375rem !important;
}

.s-16px {
    width: 16px;
    height: 16px;
}

#chooseDateTime div.fv-plugins-message-container {
    display: none !important;
}

.border-primary-text {
    border-color: rgba(var(--bs-primary-border-rgb), 1) !important;
}

.flex-basis-0 {
    flex-basis: 0;
}

.w-0 {
    width: 0;
}

.resize-0 {
    resize: none;
}

.resize-x {
    resize: horizontal;
}

.resize-y {
    resize: vertical;
}

.resize-1 {
    resize: both;
}

.s-24px {
    width: 24px;
    height: 24px;
}

.rounded-bottom-right-42px {
    border-bottom-right-radius: 42px;
}

.handle-tube {
    height: 32px;
}

.handle-container {
    height: 32px;
    background-color: var(--bs-white);
}

.handle-container:last-child {
    margin-right: 0 !important;
}

.storage-container {
    height: 20px;
}

.test-tick {
    height: 16px;
}

.tooltip-inner {
    padding: 0.5rem 0.75rem !important;
}

/* Styles for CCSearch component moved from acc-search.js */
.component-container {
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
}
.component-container.rendered {
    opacity: 1;
}
.clear-reset {
    position: absolute;
    top: 71px;
    bottom: 0;
    right: 13px;
    cursor: pointer;
}
.cc-match {
    cursor: pointer;
}
.loading-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    z-index: 1;
}
.component-loading {
    position: relative;
}
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    transition: opacity 0.4s ease-in-out;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
.fade-in {
    animation: fadeIn 0.4s ease-in-out forwards;
}
.fade-out {
    animation: fadeOut 0.4s ease-in-out forwards;
}

/* ==========================================================================
   Day Book Styles
   ========================================================================== */
.dayBookDateIcon {
    display: flex;
    align-items: center;
}

.dayBookDayDate {
    display: flex;
    align-items: center;
}

.left-right-arrow {
    display: flex;
    align-items: center;
}

.dayBookDayToggle {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    transition: background-color 0.2s ease;
}

.dayBookDayToggle:hover {
    background-color: var(--bs-primary-fill-weak);
}

.custom-table {
    width: 100%;
    margin-bottom: 0;
}

.custom-table th {
    padding: 12px 8px;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.5px;
}

.custom-table td {
    padding: 16px 8px;
    vertical-align: middle;
}

.emptyTable {
    padding: 32px !important;
    color: var(--bs-neutral-text);
    text-align: center;
    padding-top: 106px !important;
    padding-bottom: 106px !important;
}

.idcard-qr-box.border.background-primary-border {
    padding: 3.125rem 1.25rem;
  }

  .idcard-success-banner {
    padding: 0.75rem 4.6875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1rem;
    margin-bottom: 0;
  }
  .idcard-success-banner svg {
    margin-right: 0.5rem;
  }

.container-colour {
    width: 12px;
    height: 12px;
    margin-right: -12px;
    position: relative;
    left: -24px;
    top: 6px;
}

.handling-icon {
    width: 32px;
    height: 32px;
    background-color: var(--bs-white);
    border: 1px solid var(--bs-primary-border);
    border-radius: 8px;
}

.trm-icon {
    padding: 6px 4px;
    transition: background-color 0.15s ease-in-out;
}

.trm-icon img {
    -webkit-transition: -webkit-filter 0.15s ease-in-out;
    -moz-transition: -moz-filter 0.15s ease-in-out;
    -moz-transition: -ms-filter 0.15s ease-in-out;
    -ms-transition: -webkit-filter 0.15s ease-in-out;
    -o-transition: -o-filter 0.15s ease-in-out;
    transition: filter 0.15s ease-in-out, -webkit-filter 0.15s ease-in-out;
}

.trm-icon:hover,
.trm-icon.trm-icon-active {
    background-color: var(--bs-primary-fill);
}

.trm-icon:hover img,
.trm-icon.trm-icon-active img {
    filter: brightness(0) invert(1);
}

.specimen-row.specimen-nobottom-border {
    border-bottom: 0 !important;
}


.specimen-row.specimen-ifc.specimen-ir-ifc-joint {
    border-bottom-width: 0 !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.specimen-row.specimen-ifc.specimen-ir-ifc-joint div {
    margin-bottom: 0 !important;
}

.specimen-row.specimen-ifc.specimen-ir-ifc-joint div div.rounded-2 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-width: 0 !important;
}

.specimen-row.specimen-result.specimen-ir-ifc-joint {
    border-top-width: 0 !important;
    margin-top: 0 !important;
    padding-top: 0 !important;
    min-height: initial !important;
}

.specimen-row.specimen-result.specimen-ir-ifc-joint div.rounded-2 {
    margin-top: 0 !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
}

.specimen-result input[type="date"]::-webkit-inner-spin-button,
.specimen-result input[type="date"]::-webkit-calendar-picker-indicator,
.specimen-result input[type="time"]::-webkit-inner-spin-button,
.specimen-result input[type="time"]::-webkit-calendar-picker-indicator {
    /*opacity: 0;
    display: none;
    -webkit-appearance: none;*/
}

#collectedSamplesBody .accordion-body {
    flex-wrap: wrap;
}

.fixed-container-width {
    flex: 0 0 calc(100% / 8);
    max-width: calc(100% / 8);
    padding: 0 0.25rem;
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
    box-sizing: border-box;
}

.fixed-container-width label {
    white-space: nowrap;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.border-colour-error-border {
    border: 1px solid var(--bs-error-border);
}

.hidden-no-block {
    opacity: 0;
    height: 1px;
    overflow: hidden;
}