body {
	overflow-x: hidden;
}

.z-1 {
	position: relative;
	z-index: 1;
}

section.panel section {
	background: white;
	box-shadow: 0px 2px 16px 2px rgba(0, 0, 0, 0.08);
	margin-bottom: 16px;
}

header {
	padding: 24px 108px 24px 108px;
	height: 104px;
}

header img.brandLogo {
	width: 147px;
	height: 30px;
	margin: 8px;
}

header h1 {
	margin: 6px 23px 0 14px;
	padding: 6px 23px;
	border-left-width: 1px;
	border-left-style: solid;
	height: 40px;
	width: 66%;
}

div.lang-select {
	margin: 0;
	width: 20%;
	word-wrap: normal;
}

div.login {
	margin: 18px 1rem 0 4rem;
	width: 14%;
}

div.login a {
	display: inline;
}

.m-a {
	margin-left: auto;
	margin-right: auto;
	text-align: center;
}

.wh-24px {
	width: 24px;
	height: 24px;
}

.px-sm-ext {
	padding-right: 4rem !important;
    padding-left: 4rem !important;
}

.px-sm-ext-s {
	padding-right: 3rem !important;
    padding-left: 3rem !important;
}

a img {
	position: relative;
	top: -2px;
}

div.accordion-item {
	border-radius: 8px;
	padding: 1rem 1.5rem 1rem 1.5rem;
}

p.accordion-body {
	padding: 0;
}

div.search img {
	width: 24px;
	height: 24px;
}

div.floating {
	overflow: hidden;
}

div.circles-tl {
	position: absolute;
	top: 0px;
	width: 220px;
	height: 220px;
}

div.circles-tr {
	position: absolute;
	top: 0px;
	right: 0px;
	width: 249px;
	height: 250px;
}

div.circles-br {
	position: absolute;
	top: 160px;
	right: 0px;
	width: 320px;
	height: 320px;
}

div.floating img {
	width: 220px;
}

div.circle {
	border-radius: 50% 50%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 0;
}

iframe {
	position: relative;
	z-index: 1;
}

div.circles-tl div.circle-large {
	width: 100%;
	height: 100%;
	top: -39.545454545454545454545454545455%;
}

div.circles-tl div.circle-medium {
	width: 26.696832579185520361990950226244%;
	height: 26.696832579185520361990950226244%;
	top: 29.090909090909090909090909090909%;
}

div.circles-tl div.circle-small {
	width: 9.0497737556561085972850678733032%;
	height: 9.0497737556561085972850678733032%;
	top: 66.363636363636363636363636363636%;
	left: 21.818181818181818181818181818182%;
}


div.circles-br div.circle-large {
	width: 151.89504373177842565597667638484%;
	height: 151.89504373177842565597667638484%;
}

div.circles-br div.circle-medium {
	width: 34.985422740524781341107871720117%;
	height: 34.985422740524781341107871720117%;
	top: 11.428571428571428571428571428571%;
	left: 3.4985422740524781341107871720117%;
}

div.circles-br div.circle-small {
	width: 3.4985422740524781341107871720117%;
	height: 3.4985422740524781341107871720117%;
	top: 1.7857142857142857142857142857143%;
	left: 31.486880466472303206997084548105%;
}

div.circles-tr div.circle-large {
	width: 100.60975609756097560975609756098%;
	height: 100.60975609756097560975609756098%;
	left: 8.9430894308943089430894308943089%;
	top: -39.61038961038961038961038961039%;
}

div.circles-tr div.circle-medium {
	width: 26.422764227642276422764227642276%;
	height: 26.422764227642276422764227642276%;
	top: 23.706896551724137931034482758621%;
	left: 0%;
}

div.circles-tr div.circle-small {
	width: 5.1948051948051948051948051948052%;
	height: 5.1948051948051948051948051948052%;
	top: 60.606060606060606060606060606061%;
	left: 13.852813852813852813852813852814%;
}

.section-radius-bottom-right {
	border-bottom-right-radius: 224px 224px;
}

.section-radius-top-left {
	border-top-left-radius: 224px 224px;
}

.section-radius-top-right {
	border-top-right-radius: 224px 224px;
}

section.introducing-cp div.container-xl {
	margin-top: 64px;
}

section.key-features div.row div.d-flex {
	width: 50%;
}

section.panel section header {
	border-bottom: 1px solid rgba(4, 54, 115, 0.16);
}

section.introducing-cp {
	height: 480px;
}

section.half-small {
	height: 280px;
	overflow-y: hidden;
}

section.elevating-experience>div{
	padding-top: 320px;
	padding-bottom: 120px;
}

section.key-features {
	padding-top: 100px;
	padding-bottom: 120px;
}

section.key-features img {
	width: 24px;
	height: 24px;
	padding: 8px;
	box-sizing: content-box;
	border-radius: 50%;	
}

section.key-features img {
	width: 24px;
	height: 24px;
	padding: 8px;
	box-sizing: content-box;
	border-radius: 50%;	
}

section.key-features .secondary img {
	padding: 10px;
}

section.key-features h4,
section.key-features ul {
	padding: 0 44px 0 44px;
}

section.key-features ul li {
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

section.key-features ul li img,
section.key-features ul li p {
	display: inline-block;
	padding: 1rem;
}

section.start-using-cp {
	padding: 100px 0 100px 0;
}

section.start-using-cp h3 {
	margin-bottom: 6rem !important;
}

section.start-using-cp div.icon-card {
	padding: 0 12px 0 12px;
	box-sizing: border-box;
	padding-bottom: 1.5rem;
}

section div.icon-card-p {
	width: 100%;
	padding-bottom: 2rem;
}

section div.icon-card div.rounded-circle {
	padding: 2rem;
	width: 33.71584699453551912568306010929%;
	aspect-ratio: 1;
	margin: 12.068965517241379310344827586207% auto;
	display: block;
}

section div.icon-card div.rounded-circle img {
	width:  100%;
	height: 100%;
}

section div.icon-card a img {
	display: inline-block;
}

section.introducing-cp .row {
	padding-top: 160px;
}

section.introducing-cp input {
	padding: 0.5rem 1rem;
	width: 77.855477855477855477855477855478%;
	margin: 1rem auto;
	border-radius: 40px;
	border-style: solid;
	border-width: 1px;
	background-image: url('https://web-assets.dev.healius.com.au/images/icon/search.svg');
	background-repeat: no-repeat;
	background-size: 24px 24px;
	background-position: 24px 24px;
	padding-left: 60px !important;
}

.anchor-list li.active {
	
}

.fcl {
	float: left;
	min-height: 48px;
}

.fcl-15 {
	width: 15.220588235294117647058823529412%;
	min-width: 180px;
}

.fcl-22 {
	width: 22.175379426644182124789207419899%;
}

.fcl-77 {
	width: 77.824620573355817875210792580101%;
}

.fcl-69 {
	width: 69.558823529411764705882352941176%;
	padding-left: 3rem;
	padding-right: 3rem;
}

section.search-knowledge {
	padding: 96px 0 120px 0;
}

section.whats-new {
	padding: 120px 0 120px 0;
}

section.trending-topics {
	padding: 64px 0 64px 0;
}

section.faq {
	padding: 96px 0 120px 0;
}

section.extra-help {
	padding: 96px 0 144px 0;
}

section.trending-topics ul li {
	border-bottom-width: 1px;
	border-bottom-style: solid;
	padding: 16px 20px;
}

.result {
	padding-top: 56px;
}

.result ul {
	padding: 0;
	list-style: circle;
}

.result li {
	display: block;
	padding: 0 24px 0 24px;
	
	float: left;
}

.result li:first-child {
	padding-left: 0;	
}

ol.circle-list li {
	
}

ol {
  list-style: none;
  counter-reset: li-counter;
  margin: 0;
  padding: 0;
}

ol li {
  counter-increment: li-counter;
}

ol li::before {
  content: counter(li-counter);
  color: var(--bs-black-rgb);
  font-weight: bold;
  width: 34px;
  height: 34px;
  border-width: 2px;
  border-style: solid;
  border-color:  var(--bs-darkblue);
  border-radius: 50%;
  display: inline-block;
  text-align: left;
  padding: 0.35rem 0.7rem 0 0.7rem;
  margin: 0.25rem 0.5rem 0.25rem 0rem;
}

a.btn img { margin-right: 4px; }

.notice {
	background-image: url('https://web-assets.dev.healius.com.au/images/icon/blueNoticeIcon.svg');
	background-position: 16px 16px;
	background-size: 16px 16px;
	background-repeat: no-repeat;
	padding-left: 64px;
}

section.half-small .row {
	padding-top: 88px;
}

.w-16px {
	width: 16px;
}

.w-20px {
	width: 20px;
}

footer.row {
	padding-top: 6rem;
}

.sec-center {
  position: relative;
  max-width: 100%;
}

.fd [type="checkbox"]:checked,
.fd [type="checkbox"]:not(:checked) {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.fd .dropdown:checked + label,
.fd .dropdown:not(:checked) + label {
  position: relative;
  line-height: 2;
  transition: all 200ms linear;
  border-radius: 4px;
  letter-spacing: 1px;
  width: 100%;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: text;
  -moz-align-items: text;
  -ms-align-items: text;
  align-items: text;
  -webkit-justify-content: text;
  -moz-justify-content: text;
  -ms-justify-content: text;
  justify-content: text;
  -ms-flex-pack: center;
  border: none;
  cursor: pointer;
}

.fd .dark-light:checked ~ .sec-center .for-dropdown {

}

.fd .dropdown:checked + label:before,
.fd .dropdown:not(:checked) + label:before {
  position: fixed;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  cursor: auto;
  pointer-events: none;
}

.fd .dropdown:checked + label:before {
  pointer-events: auto;
}

.fd .dropdown:not(:checked) + label .uil {
  font-size: 24px;
  margin-left: 10px;
  transition: transform 200ms linear;
}

.fd .dropdown:checked + label .uil {
  transform: rotate(180deg);
  font-size: 24px;
  margin-left: 10px;
  transition: transform 200ms linear;
}

.fd .section-dropdown {
  position: absolute;
  padding: 5px;
  top: 45px;
  left: 0;
  width: 100%;
  border-radius: 4px;
  display: block;
  z-index: 202;
  opacity: 0;
  pointer-events: none;
  transform: translateY(20px);
  transition: all 200ms linear;
  background: #FFF;
}


.fd .section-dropdown.lang-section {
	top: 36px !important;
}

.fd .dropdown:checked ~ .section-dropdown {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0);
}

.fd .section-dropdown:before {
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: 20px;
  content: '';
  display: block;
}

.fd .dark-light:checked ~ .sec-center .section-dropdown:after {
  border-bottom: 8px solid #fff;
}

.fd a {
  position: relative;
  transition: all 200ms linear;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  justify-content: space-between;
    -ms-flex-pack: distribute;
}

.fd .section-dropdown a:hover {
  background-color: #E6F1FA;
}


@media screen and (max-width: 991px) {
	.fd .logo {
		top: 30px;
		left: 20px;
	}

	.fd .dark-light:checked + label,
	.fd .dark-light:not(:checked) + label {
	  top: 20px;
	  right: 20px;
	}
}