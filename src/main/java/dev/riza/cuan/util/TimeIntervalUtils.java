package dev.riza.cuan.util;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for handling time intervals and time alignment for candle aggregation.
 * Supports parsing interval strings like "1s", "5m", "1h" and calculating proper
 * time boundaries for candle alignment.
 */
public class TimeIntervalUtils {
    
    private static final Pattern INTERVAL_PATTERN = Pattern.compile("^(\\d+)([smhd])$");
    
    /**
     * Parses a time interval string into a Duration object.
     * 
     * @param intervalString The interval string (e.g., "1s", "5m", "1h", "1d")
     * @return Duration representing the interval
     * @throws IllegalArgumentException if the interval string is invalid
     */
    public static Duration parseInterval(String intervalString) {
        if (intervalString == null || intervalString.trim().isEmpty()) {
            throw new IllegalArgumentException("Interval string cannot be null or empty");
        }
        
        Matcher matcher = INTERVAL_PATTERN.matcher(intervalString.toLowerCase().trim());
        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid interval format: " + intervalString + 
                ". Expected format: number followed by s/m/h/d (e.g., '1s', '5m', '1h', '1d')");
        }
        
        int value = Integer.parseInt(matcher.group(1));
        String unit = matcher.group(2);
        
        if (value <= 0) {
            throw new IllegalArgumentException("Interval value must be positive: " + value);
        }
        
        return switch (unit) {
            case "s" -> Duration.ofSeconds(value);
            case "m" -> Duration.ofMinutes(value);
            case "h" -> Duration.ofHours(value);
            case "d" -> Duration.ofDays(value);
            default -> throw new IllegalArgumentException("Unsupported time unit: " + unit);
        };
    }
    
    /**
     * Calculates the aligned start time for a candle period based on the given timestamp and interval.
     * This ensures that candle boundaries align with standard time boundaries.
     * 
     * Examples:
     * - For 1-minute candles: 12:34:23.456 -> 12:34:00.000
     * - For 5-minute candles: 12:34:23.456 -> 12:30:00.000
     * - For 1-hour candles: 12:34:23.456 -> 12:00:00.000
     * 
     * @param timestamp The reference timestamp
     * @param interval The candle interval duration
     * @return The aligned start time for the candle period
     */
    public static Instant alignToInterval(Instant timestamp, Duration interval) {
        if (timestamp == null) {
            throw new IllegalArgumentException("Timestamp cannot be null");
        }
        if (interval == null) {
            throw new IllegalArgumentException("Interval cannot be null");
        }
        
        ZonedDateTime zdt = timestamp.atZone(ZoneOffset.UTC);
        
        // Handle different interval types
        long intervalSeconds = interval.getSeconds();
        
        if (intervalSeconds < 60) {
            // Seconds-based intervals
            return alignToSeconds(timestamp, intervalSeconds);
        } else if (intervalSeconds < 3600) {
            // Minutes-based intervals
            return alignToMinutes(zdt, intervalSeconds / 60);
        } else if (intervalSeconds < 86400) {
            // Hours-based intervals
            return alignToHours(zdt, intervalSeconds / 3600);
        } else {
            // Days-based intervals
            return alignToDays(zdt, intervalSeconds / 86400);
        }
    }
    
    /**
     * Calculates the end time for a candle period given the start time and interval.
     * 
     * @param startTime The start time of the candle
     * @param interval The candle interval duration
     * @return The end time of the candle period (exclusive)
     */
    public static Instant calculateEndTime(Instant startTime, Duration interval) {
        return startTime.plus(interval);
    }
    
    /**
     * Checks if a timestamp falls within a candle period.
     * 
     * @param timestamp The timestamp to check
     * @param candleStart The start time of the candle period (inclusive)
     * @param candleEnd The end time of the candle period (exclusive)
     * @return true if the timestamp falls within the candle period
     */
    public static boolean isWithinCandlePeriod(Instant timestamp, Instant candleStart, Instant candleEnd) {
        return !timestamp.isBefore(candleStart) && timestamp.isBefore(candleEnd);
    }
    
    private static Instant alignToSeconds(Instant timestamp, long intervalSeconds) {
        long epochSeconds = timestamp.getEpochSecond();
        long alignedSeconds = (epochSeconds / intervalSeconds) * intervalSeconds;
        return Instant.ofEpochSecond(alignedSeconds);
    }
    
    private static Instant alignToMinutes(ZonedDateTime zdt, long intervalMinutes) {
        long minute = zdt.getMinute();
        long alignedMinute = (minute / intervalMinutes) * intervalMinutes;
        
        return zdt.withMinute((int) alignedMinute)
                  .withSecond(0)
                  .withNano(0)
                  .toInstant();
    }
    
    private static Instant alignToHours(ZonedDateTime zdt, long intervalHours) {
        long hour = zdt.getHour();
        long alignedHour = (hour / intervalHours) * intervalHours;
        
        return zdt.withHour((int) alignedHour)
                  .withMinute(0)
                  .withSecond(0)
                  .withNano(0)
                  .toInstant();
    }
    
    private static Instant alignToDays(ZonedDateTime zdt, long intervalDays) {
        long dayOfYear = zdt.getDayOfYear();
        long alignedDay = ((dayOfYear - 1) / intervalDays) * intervalDays + 1;
        
        return zdt.withDayOfYear((int) alignedDay)
                  .withHour(0)
                  .withMinute(0)
                  .withSecond(0)
                  .withNano(0)
                  .toInstant();
    }
    
    /**
     * Validates if an interval string is supported.
     * 
     * @param intervalString The interval string to validate
     * @return true if the interval is valid and supported
     */
    public static boolean isValidInterval(String intervalString) {
        try {
            parseInterval(intervalString);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Gets a human-readable description of the interval.
     * 
     * @param intervalString The interval string
     * @return A human-readable description
     */
    public static String getIntervalDescription(String intervalString) {
        try {
            Duration duration = parseInterval(intervalString);
            long seconds = duration.getSeconds();
            
            if (seconds < 60) {
                return seconds + " second" + (seconds == 1 ? "" : "s");
            } else if (seconds < 3600) {
                long minutes = seconds / 60;
                return minutes + " minute" + (minutes == 1 ? "" : "s");
            } else if (seconds < 86400) {
                long hours = seconds / 3600;
                return hours + " hour" + (hours == 1 ? "" : "s");
            } else {
                long days = seconds / 86400;
                return days + " day" + (days == 1 ? "" : "s");
            }
        } catch (IllegalArgumentException e) {
            return "Invalid interval";
        }
    }
}
