package dev.riza.cuan.aggregator;

import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.config.MarketDataIndicator;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.OHLCCandle;
import dev.riza.cuan.util.CircularBuffer;
import dev.riza.cuan.util.TimeIntervalUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Time-based OHLC candle bar aggregator that implements the MarketDataIndicator interface.
 * This aggregator processes AggTradeEvent objects and generates OHLC candles based on
 * configurable time intervals with proper time alignment.
 * 
 * Features:
 * - Dynamic time interval configuration (1s, 5s, 10s, 1m, 5m, 1h, etc.)
 * - Time-aligned candle boundaries
 * - Thread-safe operation
 * - Efficient circular buffer for candle storage
 * - Real-time candle updates via reactive streams
 */
@Component
public class TimeBasedOHLCBarAggregator implements MarketDataIndicator<AggTradeEvent, OHLCCandle> {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeBasedOHLCBarAggregator.class);
    private static final MathContext MC = new MathContext(10, RoundingMode.HALF_UP);
    
    private final SubscribableChannel inputChannel;
    private final Sinks.Many<OHLCCandle> candleSink;
    private final Flux<OHLCCandle> candleStream;
    
    // Configuration
    private final String timeInterval;
    private final Duration intervalDuration;
    private final int bufferSize;
    
    // Thread-safe data structures
    private final CircularBuffer<OHLCCandle> completedCandles;
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    // Current candle state
    private volatile OHLCCandle currentCandle;
    private volatile List<AggTradeEvent> currentCandleTrades;
    private volatile Instant currentCandleStartTime;
    private volatile Instant currentCandleEndTime;
    
    /**
     * Constructor with configurable parameters.
     *
     * @param timeInterval Time interval string (e.g., "1m", "5m", "1h")
     * @param bufferSize Size of the circular buffer for storing completed candles
     */
    @Autowired
    public TimeBasedOHLCBarAggregator(
            @Value("${aggregator.ohlc.timeInterval:1m}") String timeInterval,
            @Value("${aggregator.ohlc.bufferSize:1000}") int bufferSize) {

        this.timeInterval = timeInterval;
        this.bufferSize = bufferSize;

        // Validate and parse time interval
        this.intervalDuration = TimeIntervalUtils.parseInterval(timeInterval);

        // Initialize data structures
        this.completedCandles = new CircularBuffer<>(bufferSize);
        this.currentCandleTrades = new ArrayList<>();

        // Initialize reactive stream
        this.candleSink = Sinks.many().multicast().onBackpressureBuffer();
        this.candleStream = candleSink.asFlux();

        // Initialize input channel
        this.inputChannel = new DirectChannel();
        this.inputChannel.subscribe(message -> {
            @SuppressWarnings("unchecked")
            Message<AggTradeEvent> aggTradeMessage = (Message<AggTradeEvent>) message;
            onMarketData(aggTradeMessage);
        });

        logger.info("TimeBasedOHLCBarAggregator initialized with interval: {} ({}), buffer size: {}",
                   timeInterval, TimeIntervalUtils.getIntervalDescription(timeInterval), bufferSize);
    }

    /**
     * Service activator method that handles incoming AggTradeEvent messages from the aggTradeChannel.
     * This method is automatically called by Spring Integration when messages arrive on the channel.
     *
     * @param message The message containing the AggTradeEvent
     */
    @ServiceActivator(inputChannel = "aggTradeChannel")
    public void handleAggTrade(Message<AggTradeEvent> message) {
        onMarketData(message);
    }
    
    @Override
    public void onMarketData(Message<AggTradeEvent> message) {
        AggTradeEvent trade = message.getPayload();
        
        if (trade == null || trade.getTimestamp() == null) {
            logger.warn("Received invalid trade event: {}", trade);
            return;
        }
        
        lock.writeLock().lock();
        try {
            processTradeEvent(trade);
        } catch (Exception e) {
            logger.error("Error processing trade event: {}", trade, e);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    private void processTradeEvent(AggTradeEvent trade) {
        Instant tradeTime = trade.getTimestamp();
        Instant alignedStartTime = TimeIntervalUtils.alignToInterval(tradeTime, intervalDuration);
        Instant alignedEndTime = TimeIntervalUtils.calculateEndTime(alignedStartTime, intervalDuration);
        
        // Check if we need to start a new candle
        if (currentCandle == null || !TimeIntervalUtils.isWithinCandlePeriod(tradeTime, currentCandleStartTime, currentCandleEndTime)) {
            // Complete current candle if it exists
            if (currentCandle != null) {
                completeCurrentCandle();
            }
            
            // Start new candle
            startNewCandle(trade, alignedStartTime, alignedEndTime);
        }
        
        // Update current candle with trade data
        updateCurrentCandle(trade);
        
        // Emit current candle update (incomplete candle)
        emitCurrentCandleUpdate();
    }
    
    private void startNewCandle(AggTradeEvent firstTrade, Instant startTime, Instant endTime) {
        currentCandleStartTime = startTime;
        currentCandleEndTime = endTime;
        currentCandleTrades = new ArrayList<>();
        
        currentCandle = OHLCCandle.builder()
                .openTime(startTime)
                .closeTime(endTime)
                .symbol(firstTrade.getSymbol())
                .open(firstTrade.getPrice())
                .high(firstTrade.getPrice())
                .low(firstTrade.getPrice())
                .close(firstTrade.getPrice())
                .totalVolume(BigDecimal.ZERO)
                .buyVolume(BigDecimal.ZERO)
                .sellVolume(BigDecimal.ZERO)
                .tradeCount(0)
                .timeInterval(timeInterval)
                .isComplete(false)
                .metadata("Time-based candle")
                .build();
        
        logger.debug("Started new candle for period: {} to {}", startTime, endTime);
    }
    
    private void updateCurrentCandle(AggTradeEvent trade) {
        currentCandleTrades.add(trade);
        
        // Update OHLC values
        BigDecimal price = trade.getPrice();
        currentCandle.setHigh(currentCandle.getHigh().max(price));
        currentCandle.setLow(currentCandle.getLow().min(price));
        currentCandle.setClose(price);
        
        // Update volume metrics
        BigDecimal quantity = trade.getQuantity();
        currentCandle.setTotalVolume(currentCandle.getTotalVolume().add(quantity, MC));
        
        if ("buy".equalsIgnoreCase(trade.getSide())) {
            currentCandle.setBuyVolume(currentCandle.getBuyVolume().add(quantity, MC));
        } else {
            currentCandle.setSellVolume(currentCandle.getSellVolume().add(quantity, MC));
        }
        
        // Update trade count
        currentCandle.setTradeCount(currentCandle.getTradeCount() + 1);
        
        // Calculate VWAP
        calculateVWAP();
    }
    
    private void calculateVWAP() {
        BigDecimal totalPV = BigDecimal.ZERO; // Price * Volume
        BigDecimal totalVolume = BigDecimal.ZERO;
        
        for (AggTradeEvent trade : currentCandleTrades) {
            BigDecimal pv = trade.getPrice().multiply(trade.getQuantity(), MC);
            totalPV = totalPV.add(pv, MC);
            totalVolume = totalVolume.add(trade.getQuantity(), MC);
        }
        
        if (totalVolume.compareTo(BigDecimal.ZERO) > 0) {
            currentCandle.setVwap(totalPV.divide(totalVolume, MC));
        } else {
            currentCandle.setVwap(currentCandle.getClose());
        }
    }
    
    private void completeCurrentCandle() {
        if (currentCandle == null) return;
        
        // Mark candle as complete
        currentCandle.setComplete(true);
        
        // Store in circular buffer
        completedCandles.add(currentCandle);
        
        // Publish completed candle
        publishCandle(currentCandle);
        
        logger.debug("Completed candle: {} trades, OHLC: {}/{}/{}/{}, Volume: {}", 
                    currentCandle.getTradeCount(),
                    currentCandle.getOpen(), currentCandle.getHigh(), 
                    currentCandle.getLow(), currentCandle.getClose(),
                    currentCandle.getTotalVolume());
    }
    
    private void emitCurrentCandleUpdate() {
        if (currentCandle != null) {
            // Create a copy for emission to avoid concurrent modification
            OHLCCandle candleCopy = OHLCCandle.builder()
                    .openTime(currentCandle.getOpenTime())
                    .closeTime(currentCandle.getCloseTime())
                    .symbol(currentCandle.getSymbol())
                    .open(currentCandle.getOpen())
                    .high(currentCandle.getHigh())
                    .low(currentCandle.getLow())
                    .close(currentCandle.getClose())
                    .vwap(currentCandle.getVwap())
                    .totalVolume(currentCandle.getTotalVolume())
                    .buyVolume(currentCandle.getBuyVolume())
                    .sellVolume(currentCandle.getSellVolume())
                    .tradeCount(currentCandle.getTradeCount())
                    .timeInterval(currentCandle.getTimeInterval())
                    .isComplete(false)
                    .metadata(currentCandle.getMetadata())
                    .build();
            
            publishCandle(candleCopy);
        }
    }
    
    private void publishCandle(OHLCCandle candle) {
        try {
            candleSink.tryEmitNext(candle);
        } catch (Exception e) {
            logger.error("Failed to publish candle: {}", candle, e);
        }
    }
    
    @Override
    public MessageChannel getInputChannel() {
        return inputChannel;
    }
    
    @Override
    public Duration getTimeframe() {
        return intervalDuration;
    }
    
    @Override
    public Flux<OHLCCandle> getStream() {
        return candleStream;
    }
    
    /**
     * Gets the current incomplete candle.
     * 
     * @return Current candle or null if no candle is being formed
     */
    public OHLCCandle getCurrentCandle() {
        lock.readLock().lock();
        try {
            return currentCandle;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Gets a list of completed candles from the circular buffer.
     * 
     * @return List of completed candles (oldest to newest)
     */
    public List<OHLCCandle> getCompletedCandles() {
        lock.readLock().lock();
        try {
            return completedCandles.toList();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Gets the most recent N completed candles.
     * 
     * @param count Number of candles to retrieve
     * @return List of recent completed candles
     */
    public List<OHLCCandle> getRecentCandles(int count) {
        lock.readLock().lock();
        try {
            List<OHLCCandle> allCandles = completedCandles.toList();
            int size = allCandles.size();
            if (count >= size) {
                return allCandles;
            }
            return allCandles.subList(size - count, size);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Gets configuration information about this aggregator.
     *
     * @return Configuration string
     */
    public String getConfigInfo() {
        return String.format("TimeBasedOHLCBarAggregator[interval=%s, duration=%s, bufferSize=%d]",
                           timeInterval, intervalDuration, bufferSize);
    }

    /**
     * Manually completes the current candle if one exists.
     * This can be useful for testing or forcing candle completion.
     *
     * @return The completed candle, or null if no current candle exists
     */
    public OHLCCandle forceCompleteCurrentCandle() {
        lock.writeLock().lock();
        try {
            if (currentCandle != null) {
                OHLCCandle completedCandle = currentCandle;
                completeCurrentCandle();
                return completedCandle;
            }
            return null;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Gets statistics about the aggregator's current state.
     *
     * @return Statistics string
     */
    public String getStatistics() {
        lock.readLock().lock();
        try {
            int completedCount = completedCandles.size();
            String currentCandleInfo = currentCandle != null ?
                String.format("trades=%d, volume=%s", currentCandle.getTradeCount(), currentCandle.getTotalVolume()) :
                "none";

            return String.format("TimeBasedOHLCBarAggregator Stats: completed=%d, current=[%s], interval=%s",
                               completedCount, currentCandleInfo, timeInterval);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Checks if the aggregator has any completed candles.
     *
     * @return true if there are completed candles
     */
    public boolean hasCompletedCandles() {
        lock.readLock().lock();
        try {
            return !completedCandles.isEmpty();
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Gets the most recent completed candle.
     *
     * @return The most recent completed candle, or null if none exist
     */
    public OHLCCandle getLastCompletedCandle() {
        lock.readLock().lock();
        try {
            List<OHLCCandle> candles = completedCandles.toList();
            return candles.isEmpty() ? null : candles.get(candles.size() - 1);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Clears all completed candles from the buffer.
     * The current incomplete candle is not affected.
     */
    public void clearCompletedCandles() {
        lock.writeLock().lock();
        try {
            completedCandles.clear();
            logger.info("Cleared all completed candles from buffer");
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Gets the time interval string used by this aggregator.
     *
     * @return The time interval string (e.g., "1m", "5m", "1h")
     */
    public String getTimeInterval() {
        return timeInterval;
    }

    /**
     * Gets the buffer size used for storing completed candles.
     *
     * @return The buffer size
     */
    public int getBufferSize() {
        return bufferSize;
    }
}
