package dev.riza.cuan.aggregator;

import dev.riza.cuan.config.MarketDataIndicator;
import dev.riza.cuan.domain.model.AdaptiveBarData;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.util.CircularBuffer;
import dev.riza.cuan.util.StatisticalUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Adaptive Volume Bar Aggregator that creates bars based on dynamically adjusted volume thresholds.
 * The volume threshold is adapted based on internally calculated market condition metrics:
 * rolling volatility, trades per second (TPS), and recent volume rate.
 * 
 * Features:
 * - Dynamic volume threshold adaptation based on market conditions
 * - Internal calculation of volatility, TPS, and volume rate metrics
 * - Thread-safe operation with concurrent access support
 * - Reactive stream publishing of completed bars
 * - Configurable sensitivity factors and window sizes
 */
@Component
public class AdaptiveVolumeBarAggregator implements MarketDataIndicator<AggTradeEvent, AdaptiveBarData> {
    
    private static final Logger logger = LoggerFactory.getLogger(AdaptiveVolumeBarAggregator.class);
    private static final MathContext MC = new MathContext(10, RoundingMode.HALF_UP);
    
    private final SubscribableChannel inputChannel;
    private final Sinks.Many<AdaptiveBarData> barSink;
    private final Flux<AdaptiveBarData> barStream;
    
    // Configuration parameters
    private final BigDecimal baseVolumeThreshold;
    private final int volatilityCalculationWindowSize;
    private final Duration tpsCalculationWindowDuration;
    private final int volumeRateCalculationWindowSize;
    private final int marketMetricsUpdateFrequency;
    private final double volatilitySensitivityFactor;
    private final double tpsSensitivityFactor;
    private final double volumeRateSensitivityFactor;
    private final BigDecimal minDynamicVolumeThreshold;
    private final BigDecimal maxDynamicVolumeThreshold;
    
    // Thread-safe data structures
    private final CircularBuffer<BigDecimal> recentPrices;
    private final CircularBuffer<AggTradeEvent> recentTradesForVolume;
    private final CircularBuffer<Instant> recentTradeTimestamps;
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    // Current bar state
    private volatile List<AggTradeEvent> currentBarTrades;
    private volatile Instant currentBarOpenTime;
    private volatile String currentSymbol;
    private volatile BigDecimal currentOpenPrice;
    private volatile BigDecimal currentHighPrice;
    private volatile BigDecimal currentLowPrice;
    private volatile BigDecimal currentTotalVolume;
    private volatile BigDecimal currentAggressiveBuyVolume;
    private volatile BigDecimal currentAggressiveSellVolume;
    private volatile BigDecimal currentPriceVolumeProduct;
    private volatile int currentTradeCount;
    
    // Market condition metrics
    private volatile BigDecimal currentRollingVolatility;
    private volatile double currentTradesPerSecond;
    private volatile BigDecimal currentRecentVolumeRate;
    private volatile BigDecimal currentDynamicVolumeThreshold;
    private volatile int tradesProcessedSinceLastMetricsUpdate;
    
    /**
     * Constructor with configurable parameters for adaptive volume bar aggregation.
     */
    @Autowired
    public AdaptiveVolumeBarAggregator(
            @Value("${aggregator.adaptive.baseVolumeThreshold:10.0}") BigDecimal baseVolumeThreshold,
            @Value("${aggregator.adaptive.volatilityWindowSize:50}") int volatilityCalculationWindowSize,
            @Value("${aggregator.adaptive.tpsWindowDurationSeconds:10}") int tpsWindowDurationSeconds,
            @Value("${aggregator.adaptive.volumeRateWindowSize:30}") int volumeRateCalculationWindowSize,
            @Value("${aggregator.adaptive.metricsUpdateFrequency:5}") int marketMetricsUpdateFrequency,
            @Value("${aggregator.adaptive.volatilitySensitivity:1.0}") double volatilitySensitivityFactor,
            @Value("${aggregator.adaptive.tpsSensitivity:1.0}") double tpsSensitivityFactor,
            @Value("${aggregator.adaptive.volumeRateSensitivity:0.5}") double volumeRateSensitivityFactor,
            @Value("${aggregator.adaptive.minVolumeThreshold:1.0}") BigDecimal minDynamicVolumeThreshold,
            @Value("${aggregator.adaptive.maxVolumeThreshold:100.0}") BigDecimal maxDynamicVolumeThreshold) {

        this.baseVolumeThreshold = baseVolumeThreshold;
        this.volatilityCalculationWindowSize = volatilityCalculationWindowSize;
        this.tpsCalculationWindowDuration = Duration.ofSeconds(tpsWindowDurationSeconds);
        this.volumeRateCalculationWindowSize = volumeRateCalculationWindowSize;
        this.marketMetricsUpdateFrequency = marketMetricsUpdateFrequency;
        this.volatilitySensitivityFactor = volatilitySensitivityFactor;
        this.tpsSensitivityFactor = tpsSensitivityFactor;
        this.volumeRateSensitivityFactor = volumeRateSensitivityFactor;
        this.minDynamicVolumeThreshold = minDynamicVolumeThreshold;
        this.maxDynamicVolumeThreshold = maxDynamicVolumeThreshold;
        
        // Initialize circular buffers
        this.recentPrices = new CircularBuffer<>(volatilityCalculationWindowSize);
        this.recentTradesForVolume = new CircularBuffer<>(volumeRateCalculationWindowSize);
        this.recentTradeTimestamps = new CircularBuffer<>(200); // Large enough for TPS calculation
        
        // Initialize current bar state
        this.currentBarTrades = new ArrayList<>();
        this.currentTotalVolume = BigDecimal.ZERO;
        this.currentAggressiveBuyVolume = BigDecimal.ZERO;
        this.currentAggressiveSellVolume = BigDecimal.ZERO;
        this.currentPriceVolumeProduct = BigDecimal.ZERO;
        this.currentTradeCount = 0;
        
        // Initialize market metrics
        this.currentRollingVolatility = BigDecimal.ZERO;
        this.currentTradesPerSecond = 0.0;
        this.currentRecentVolumeRate = BigDecimal.ZERO;
        this.currentDynamicVolumeThreshold = baseVolumeThreshold;
        this.tradesProcessedSinceLastMetricsUpdate = 0;
        
        // Initialize reactive stream
        this.barSink = Sinks.many().multicast().onBackpressureBuffer();
        this.barStream = barSink.asFlux();
        
        // Initialize input channel
        this.inputChannel = new DirectChannel();
        this.inputChannel.subscribe(message -> {
            @SuppressWarnings("unchecked")
            Message<AggTradeEvent> aggTradeMessage = (Message<AggTradeEvent>) message;
            onMarketData(aggTradeMessage);
        });
        
        logger.info("AdaptiveVolumeBarAggregator initialized with baseThreshold: {}, volatilityWindow: {}, " +
                   "tpsWindow: {}s, volumeRateWindow: {}, updateFreq: {}", 
                   baseVolumeThreshold, volatilityCalculationWindowSize, 
                   tpsWindowDurationSeconds, volumeRateCalculationWindowSize, marketMetricsUpdateFrequency);
    }
    
    /**
     * Service activator method that handles incoming AggTradeEvent messages.
     */
    @ServiceActivator(inputChannel = "aggTradeChannel")
    public void handleAggTrade(Message<AggTradeEvent> message) {
        onMarketData(message);
    }
    
    @Override
    public void onMarketData(Message<AggTradeEvent> message) {
        AggTradeEvent trade = message.getPayload();
        
        if (trade == null || trade.getTimestamp() == null) {
            logger.warn("Received invalid trade event: {}", trade);
            return;
        }
        
        lock.writeLock().lock();
        try {
            processTradeEvent(trade);
        } catch (Exception e) {
            logger.error("Error processing trade event: {}", trade, e);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    private void processTradeEvent(AggTradeEvent trade) {
        // Update internal rolling windows for market condition metrics
        updateRollingWindows(trade);
        
        // Update market condition metrics if needed
        tradesProcessedSinceLastMetricsUpdate++;
        if (tradesProcessedSinceLastMetricsUpdate >= marketMetricsUpdateFrequency) {
            updateMarketConditionMetrics();
            updateDynamicVolumeThreshold();
            tradesProcessedSinceLastMetricsUpdate = 0;
        }
        
        // Handle symbol changes
        if (currentSymbol != null && !currentSymbol.equals(trade.getSymbol())) {
            if (currentTradeCount > 0) {
                finalizeCurrentBar(trade.getTimestamp());
            }
            startNewBar(trade);
        } else if (currentSymbol == null) {
            // First trade ever
            startNewBar(trade);
        }
        
        // Add trade to current bar
        addTradeToCurrentBar(trade);
        
        // Check if volume threshold is reached
        if (currentTotalVolume.compareTo(currentDynamicVolumeThreshold) >= 0) {
            finalizeCurrentBar(trade.getTimestamp());
            startNewBar(trade);
        }
    }
    
    @Override
    public MessageChannel getInputChannel() {
        return inputChannel;
    }
    
    @Override
    public Duration getTimeframe() {
        // Adaptive bars don't have fixed timeframes
        return Duration.ZERO;
    }
    
    @Override
    public Flux<AdaptiveBarData> getStream() {
        return barStream;
    }

    /**
     * Updates the rolling windows used for market condition metric calculations.
     */
    private void updateRollingWindows(AggTradeEvent trade) {
        // Update price window for volatility calculation
        recentPrices.add(trade.getPrice());

        // Update trade window for volume rate calculation
        recentTradesForVolume.add(trade);

        // Update timestamp window for TPS calculation
        recentTradeTimestamps.add(trade.getTimestamp());
        cleanupOldTimestamps(trade.getTimestamp());
    }

    /**
     * Removes timestamps older than the TPS calculation window.
     */
    private void cleanupOldTimestamps(Instant currentTime) {
        Instant cutoff = currentTime.minus(tpsCalculationWindowDuration);
        List<Instant> currentTimestamps = recentTradeTimestamps.toList();
        recentTradeTimestamps.clear();

        for (Instant timestamp : currentTimestamps) {
            if (!timestamp.isBefore(cutoff)) {
                recentTradeTimestamps.add(timestamp);
            }
        }
    }

    /**
     * Updates the internal market condition metrics based on rolling windows.
     */
    private void updateMarketConditionMetrics() {
        // Calculate rolling volatility
        if (recentPrices.size() >= 2) {
            currentRollingVolatility = StatisticalUtils.standardDeviation(recentPrices.toList(), MC);
        } else {
            currentRollingVolatility = BigDecimal.ZERO;
        }

        // Calculate trades per second
        if (recentTradeTimestamps.size() > 1) {
            List<Instant> timestamps = recentTradeTimestamps.toList();
            Duration duration = Duration.between(timestamps.get(0), timestamps.get(timestamps.size() - 1));
            if (!duration.isZero()) {
                currentTradesPerSecond = (double) timestamps.size() / duration.toSeconds();
            } else {
                currentTradesPerSecond = timestamps.size(); // All trades in same second
            }
        } else {
            currentTradesPerSecond = 0.0;
        }

        // Calculate recent volume rate (average volume per trade)
        if (!recentTradesForVolume.isEmpty()) {
            BigDecimal totalVolume = BigDecimal.ZERO;
            List<AggTradeEvent> trades = recentTradesForVolume.toList();
            for (AggTradeEvent trade : trades) {
                totalVolume = totalVolume.add(trade.getQuantity(), MC);
            }
            currentRecentVolumeRate = totalVolume.divide(new BigDecimal(trades.size()), MC);
        } else {
            currentRecentVolumeRate = BigDecimal.ZERO;
        }

        logger.debug("Updated market metrics - Volatility: {}, TPS: {:.2f}, VolumeRate: {}",
                    currentRollingVolatility, currentTradesPerSecond, currentRecentVolumeRate);
    }

    /**
     * Updates the dynamic volume threshold based on current market condition metrics.
     */
    private void updateDynamicVolumeThreshold() {
        BigDecimal threshold = baseVolumeThreshold;

        // Volatility adjustment (inverse relationship - higher volatility = lower threshold)
        double volatilityAdjustment = 1.0;
        if (currentRollingVolatility.compareTo(BigDecimal.ZERO) > 0) {
            // Convert volatility to a reasonable scale for adjustment
            double volatilityValue = currentRollingVolatility.doubleValue();
            volatilityAdjustment = 1.0 / (1.0 + volatilityValue * volatilitySensitivityFactor);
            volatilityAdjustment = Math.max(0.1, Math.min(2.0, volatilityAdjustment)); // Clamp between 0.1 and 2.0
        }

        // TPS adjustment (inverse relationship - higher TPS = lower threshold)
        double tpsAdjustment = 1.0;
        if (currentTradesPerSecond > 0) {
            tpsAdjustment = 1.0 / (1.0 + currentTradesPerSecond * tpsSensitivityFactor * 0.1);
            tpsAdjustment = Math.max(0.1, Math.min(2.0, tpsAdjustment)); // Clamp between 0.1 and 2.0
        }

        // Volume rate adjustment (direct relationship - higher avg volume = higher threshold)
        double volumeRateAdjustment = 1.0;
        if (currentRecentVolumeRate.compareTo(BigDecimal.ZERO) > 0) {
            double volumeRateValue = currentRecentVolumeRate.doubleValue();
            volumeRateAdjustment = 1.0 + volumeRateValue * volumeRateSensitivityFactor * 0.1;
            volumeRateAdjustment = Math.max(0.5, Math.min(3.0, volumeRateAdjustment)); // Clamp between 0.5 and 3.0
        }

        // Apply all adjustments
        double totalAdjustment = volatilityAdjustment * tpsAdjustment * volumeRateAdjustment;
        threshold = threshold.multiply(new BigDecimal(totalAdjustment), MC);

        // Clamp final threshold
        if (threshold.compareTo(minDynamicVolumeThreshold) < 0) {
            threshold = minDynamicVolumeThreshold;
        } else if (threshold.compareTo(maxDynamicVolumeThreshold) > 0) {
            threshold = maxDynamicVolumeThreshold;
        }

        currentDynamicVolumeThreshold = threshold;

        logger.debug("Updated dynamic threshold: {} (base: {}, vol_adj: {:.3f}, tps_adj: {:.3f}, vr_adj: {:.3f})",
                    currentDynamicVolumeThreshold, baseVolumeThreshold,
                    volatilityAdjustment, tpsAdjustment, volumeRateAdjustment);
    }

    /**
     * Starts a new adaptive bar with the given trade as the first trade.
     */
    private void startNewBar(AggTradeEvent firstTrade) {
        currentBarOpenTime = firstTrade.getTimestamp();
        currentSymbol = firstTrade.getSymbol();
        currentOpenPrice = firstTrade.getPrice();
        currentHighPrice = firstTrade.getPrice();
        currentLowPrice = firstTrade.getPrice();
        currentTotalVolume = BigDecimal.ZERO;
        currentAggressiveBuyVolume = BigDecimal.ZERO;
        currentAggressiveSellVolume = BigDecimal.ZERO;
        currentPriceVolumeProduct = BigDecimal.ZERO;
        currentTradeCount = 0;
        currentBarTrades = new ArrayList<>();

        logger.debug("Started new adaptive bar for symbol: {} at price: {}", currentSymbol, currentOpenPrice);
    }

    /**
     * Adds a trade to the current bar, updating all relevant metrics.
     */
    private void addTradeToCurrentBar(AggTradeEvent trade) {
        currentBarTrades.add(trade);

        // Update OHLC
        BigDecimal price = trade.getPrice();
        currentHighPrice = currentHighPrice.max(price);
        currentLowPrice = currentLowPrice.min(price);

        // Update volume metrics
        BigDecimal quantity = trade.getQuantity();
        currentTotalVolume = currentTotalVolume.add(quantity, MC);

        // Determine aggressive side based on trade side
        // In Binance: side="buy" means buyer was taker (aggressive buy)
        // side="sell" means seller was taker (aggressive sell)
        if ("buy".equalsIgnoreCase(trade.getSide())) {
            currentAggressiveBuyVolume = currentAggressiveBuyVolume.add(quantity, MC);
        } else {
            currentAggressiveSellVolume = currentAggressiveSellVolume.add(quantity, MC);
        }

        // Update price-volume product for VWAP calculation
        currentPriceVolumeProduct = currentPriceVolumeProduct.add(price.multiply(quantity, MC), MC);

        // Update trade count
        currentTradeCount++;

        logger.trace("Added trade to bar: price={}, volume={}, total_volume={}, trades={}",
                    price, quantity, currentTotalVolume, currentTradeCount);
    }

    /**
     * Finalizes the current bar and emits it as an AdaptiveBarData object.
     */
    private void finalizeCurrentBar(Instant closeTime) {
        if (currentTradeCount == 0) {
            logger.warn("Attempting to finalize bar with no trades");
            return;
        }

        // Calculate VWAP
        BigDecimal vwap = currentTotalVolume.compareTo(BigDecimal.ZERO) > 0 ?
                currentPriceVolumeProduct.divide(currentTotalVolume, MC) :
                currentOpenPrice;

        // Get the last trade's price as close price
        BigDecimal closePrice = currentBarTrades.isEmpty() ?
                currentOpenPrice :
                currentBarTrades.get(currentBarTrades.size() - 1).getPrice();

        // Create market condition snapshot
        AdaptiveBarData.MarketConditionSnapshot snapshot = AdaptiveBarData.MarketConditionSnapshot.builder()
                .rollingVolatility(currentRollingVolatility)
                .tradesPerSecond(currentTradesPerSecond)
                .recentVolumeRate(currentRecentVolumeRate)
                .dynamicVolumeThreshold(currentDynamicVolumeThreshold)
                .baseVolumeThreshold(baseVolumeThreshold)
                .volatilityAdjustment(currentRollingVolatility.doubleValue() * volatilitySensitivityFactor)
                .tpsAdjustment(currentTradesPerSecond * tpsSensitivityFactor)
                .volumeRateAdjustment(currentRecentVolumeRate.doubleValue() * volumeRateSensitivityFactor)
                .calculatedAt(closeTime)
                .volatilityWindowSize(recentPrices.size())
                .tpsWindowDurationSeconds(tpsCalculationWindowDuration.getSeconds())
                .volumeRateWindowSize(recentTradesForVolume.size())
                .build();

        // Create the adaptive bar data
        AdaptiveBarData barData = AdaptiveBarData.builder()
                .openTime(currentBarOpenTime)
                .closeTime(closeTime)
                .symbol(currentSymbol)
                .openPrice(currentOpenPrice)
                .highPrice(currentHighPrice)
                .lowPrice(currentLowPrice)
                .closePrice(closePrice)
                .volumeWeightedAveragePrice(vwap)
                .totalVolumeTraded(currentTotalVolume)
                .aggressiveBuyVolume(currentAggressiveBuyVolume)
                .aggressiveSellVolume(currentAggressiveSellVolume)
                .tradeCount(currentTradeCount)
                .closingReason("ADAPTIVE_VOLUME_THRESHOLD")
                .marketConditionSnapshotAtClose(snapshot)
                .build();

        // Emit the completed bar
        barSink.tryEmitNext(barData);

        logger.info("Completed Adaptive Bar [{}]: {} | OHLC: {}/{}/{}/{} | Volume: {} | Trades: {} | Threshold: {}",
                   currentSymbol, closeTime,
                   currentOpenPrice, currentHighPrice, currentLowPrice, closePrice,
                   currentTotalVolume, currentTradeCount, currentDynamicVolumeThreshold);
    }

    /**
     * Gets the current dynamic volume threshold.
     *
     * @return Current dynamic volume threshold
     */
    public BigDecimal getCurrentDynamicVolumeThreshold() {
        return currentDynamicVolumeThreshold;
    }

    /**
     * Gets the current market condition metrics.
     *
     * @return Current market condition snapshot
     */
    public AdaptiveBarData.MarketConditionSnapshot getCurrentMarketConditions() {
        lock.readLock().lock();
        try {
            return AdaptiveBarData.MarketConditionSnapshot.builder()
                    .rollingVolatility(currentRollingVolatility)
                    .tradesPerSecond(currentTradesPerSecond)
                    .recentVolumeRate(currentRecentVolumeRate)
                    .dynamicVolumeThreshold(currentDynamicVolumeThreshold)
                    .baseVolumeThreshold(baseVolumeThreshold)
                    .calculatedAt(Instant.now())
                    .volatilityWindowSize(recentPrices.size())
                    .tpsWindowDurationSeconds(tpsCalculationWindowDuration.getSeconds())
                    .volumeRateWindowSize(recentTradesForVolume.size())
                    .build();
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Gets statistics about the current bar being formed.
     *
     * @return Statistics string
     */
    public String getCurrentBarStatistics() {
        lock.readLock().lock();
        try {
            if (currentSymbol == null) {
                return "No active bar";
            }
            return String.format("Current Bar [%s]: trades=%d, volume=%s, threshold=%s (%.1f%% filled)",
                               currentSymbol, currentTradeCount, currentTotalVolume,
                               currentDynamicVolumeThreshold,
                               currentTotalVolume.divide(currentDynamicVolumeThreshold, MC).multiply(new BigDecimal("100")).doubleValue());
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Gets configuration information about this aggregator.
     *
     * @return Configuration string
     */
    public String getConfigInfo() {
        return String.format("AdaptiveVolumeBarAggregator[baseThreshold=%s, volWindow=%d, tpsWindow=%ds, " +
                           "volRateWindow=%d, updateFreq=%d, volSens=%.2f, tpsSens=%.2f, vrSens=%.2f]",
                           baseVolumeThreshold, volatilityCalculationWindowSize,
                           tpsCalculationWindowDuration.getSeconds(), volumeRateCalculationWindowSize,
                           marketMetricsUpdateFrequency, volatilitySensitivityFactor,
                           tpsSensitivityFactor, volumeRateSensitivityFactor);
    }

    /**
     * Forces completion of the current bar if one exists.
     * Useful for testing or manual bar completion.
     *
     * @return The completed bar data, or null if no active bar
     */
    public AdaptiveBarData forceCompleteCurrentBar() {
        lock.writeLock().lock();
        try {
            if (currentSymbol != null && currentTradeCount > 0) {
                finalizeCurrentBar(Instant.now());
                // Reset state for next bar
                currentSymbol = null;
                currentTradeCount = 0;
                return null; // Bar was emitted via stream
            }
            return null;
        } finally {
            lock.writeLock().unlock();
        }
    }
}
