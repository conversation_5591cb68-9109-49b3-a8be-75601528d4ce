package dev.riza.cuan.aggregator;

import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.config.MarketDataIndicator;
import dev.riza.cuan.domain.model.AdaptiveBarData;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.util.CircularBuffer;
import dev.riza.cuan.util.StatisticalUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Adaptive Volume Bar Aggregator that creates bars based on dynamically adjusted volume thresholds.
 * The volume threshold is adapted based on internally calculated market condition metrics:
 * rolling volatility, trades per second (TPS), and recent volume rate.
 * 
 * Features:
 * - Dynamic volume threshold adaptation based on market conditions
 * - Internal calculation of volatility, TPS, and volume rate metrics
 * - Thread-safe operation with concurrent access support
 * - Reactive stream publishing of completed bars
 * - Configurable sensitivity factors and window sizes
 */
@Component
public class AdaptiveVolumeBarAggregator implements MarketDataIndicator<AggTradeEvent, AdaptiveBarData> {
    
    private static final Logger logger = LoggerFactory.getLogger(AdaptiveVolumeBarAggregator.class);
    private static final MathContext MC = new MathContext(10, RoundingMode.HALF_UP);
    
    private final MarketDataGateway marketDataGateway;
    private final SubscribableChannel inputChannel;
    private final Sinks.Many<AdaptiveBarData> barSink;
    private final Flux<AdaptiveBarData> barStream;
    
    // Configuration parameters
    private final BigDecimal baseVolumeThreshold;
    private final int volatilityCalculationWindowSize;
    private final Duration tpsCalculationWindowDuration;
    private final int volumeRateCalculationWindowSize;
    private final int marketMetricsUpdateFrequency;
    private final double volatilitySensitivityFactor;
    private final double tpsSensitivityFactor;
    private final double volumeRateSensitivityFactor;
    private final BigDecimal minDynamicVolumeThreshold;
    private final BigDecimal maxDynamicVolumeThreshold;
    
    // Thread-safe data structures
    private final CircularBuffer<BigDecimal> recentPrices;
    private final CircularBuffer<AggTradeEvent> recentTradesForVolume;
    private final CircularBuffer<Instant> recentTradeTimestamps;
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    // Current bar state
    private volatile List<AggTradeEvent> currentBarTrades;
    private volatile Instant currentBarOpenTime;
    private volatile String currentSymbol;
    private volatile BigDecimal currentOpenPrice;
    private volatile BigDecimal currentHighPrice;
    private volatile BigDecimal currentLowPrice;
    private volatile BigDecimal currentTotalVolume;
    private volatile BigDecimal currentAggressiveBuyVolume;
    private volatile BigDecimal currentAggressiveSellVolume;
    private volatile BigDecimal currentPriceVolumeProduct;
    private volatile int currentTradeCount;
    
    // Market condition metrics
    private volatile BigDecimal currentRollingVolatility;
    private volatile double currentTradesPerSecond;
    private volatile BigDecimal currentRecentVolumeRate;
    private volatile BigDecimal currentDynamicVolumeThreshold;
    private volatile int tradesProcessedSinceLastMetricsUpdate;
    
    /**
     * Constructor with configurable parameters for adaptive volume bar aggregation.
     */
    @Autowired
    public AdaptiveVolumeBarAggregator(
            MarketDataGateway marketDataGateway,
            @Value("${aggregator.adaptive.baseVolumeThreshold:10.0}") BigDecimal baseVolumeThreshold,
            @Value("${aggregator.adaptive.volatilityWindowSize:50}") int volatilityCalculationWindowSize,
            @Value("${aggregator.adaptive.tpsWindowDurationSeconds:10}") int tpsWindowDurationSeconds,
            @Value("${aggregator.adaptive.volumeRateWindowSize:30}") int volumeRateCalculationWindowSize,
            @Value("${aggregator.adaptive.metricsUpdateFrequency:5}") int marketMetricsUpdateFrequency,
            @Value("${aggregator.adaptive.volatilitySensitivity:1.0}") double volatilitySensitivityFactor,
            @Value("${aggregator.adaptive.tpsSensitivity:1.0}") double tpsSensitivityFactor,
            @Value("${aggregator.adaptive.volumeRateSensitivity:0.5}") double volumeRateSensitivityFactor,
            @Value("${aggregator.adaptive.minVolumeThreshold:1.0}") BigDecimal minDynamicVolumeThreshold,
            @Value("${aggregator.adaptive.maxVolumeThreshold:100.0}") BigDecimal maxDynamicVolumeThreshold) {
        
        this.marketDataGateway = marketDataGateway;
        this.baseVolumeThreshold = baseVolumeThreshold;
        this.volatilityCalculationWindowSize = volatilityCalculationWindowSize;
        this.tpsCalculationWindowDuration = Duration.ofSeconds(tpsWindowDurationSeconds);
        this.volumeRateCalculationWindowSize = volumeRateCalculationWindowSize;
        this.marketMetricsUpdateFrequency = marketMetricsUpdateFrequency;
        this.volatilitySensitivityFactor = volatilitySensitivityFactor;
        this.tpsSensitivityFactor = tpsSensitivityFactor;
        this.volumeRateSensitivityFactor = volumeRateSensitivityFactor;
        this.minDynamicVolumeThreshold = minDynamicVolumeThreshold;
        this.maxDynamicVolumeThreshold = maxDynamicVolumeThreshold;
        
        // Initialize circular buffers
        this.recentPrices = new CircularBuffer<>(volatilityCalculationWindowSize);
        this.recentTradesForVolume = new CircularBuffer<>(volumeRateCalculationWindowSize);
        this.recentTradeTimestamps = new CircularBuffer<>(200); // Large enough for TPS calculation
        
        // Initialize current bar state
        this.currentBarTrades = new ArrayList<>();
        this.currentTotalVolume = BigDecimal.ZERO;
        this.currentAggressiveBuyVolume = BigDecimal.ZERO;
        this.currentAggressiveSellVolume = BigDecimal.ZERO;
        this.currentPriceVolumeProduct = BigDecimal.ZERO;
        this.currentTradeCount = 0;
        
        // Initialize market metrics
        this.currentRollingVolatility = BigDecimal.ZERO;
        this.currentTradesPerSecond = 0.0;
        this.currentRecentVolumeRate = BigDecimal.ZERO;
        this.currentDynamicVolumeThreshold = baseVolumeThreshold;
        this.tradesProcessedSinceLastMetricsUpdate = 0;
        
        // Initialize reactive stream
        this.barSink = Sinks.many().multicast().onBackpressureBuffer();
        this.barStream = barSink.asFlux();
        
        // Initialize input channel
        this.inputChannel = new DirectChannel();
        this.inputChannel.subscribe(message -> {
            @SuppressWarnings("unchecked")
            Message<AggTradeEvent> aggTradeMessage = (Message<AggTradeEvent>) message;
            onMarketData(aggTradeMessage);
        });
        
        logger.info("AdaptiveVolumeBarAggregator initialized with baseThreshold: {}, volatilityWindow: {}, " +
                   "tpsWindow: {}s, volumeRateWindow: {}, updateFreq: {}", 
                   baseVolumeThreshold, volatilityCalculationWindowSize, 
                   tpsWindowDurationSeconds, volumeRateCalculationWindowSize, marketMetricsUpdateFrequency);
    }
    
    /**
     * Service activator method that handles incoming AggTradeEvent messages.
     */
    @ServiceActivator(inputChannel = "aggTradeChannel")
    public void handleAggTrade(Message<AggTradeEvent> message) {
        onMarketData(message);
    }
    
    @Override
    public void onMarketData(Message<AggTradeEvent> message) {
        AggTradeEvent trade = message.getPayload();
        
        if (trade == null || trade.getTimestamp() == null) {
            logger.warn("Received invalid trade event: {}", trade);
            return;
        }
        
        lock.writeLock().lock();
        try {
            processTradeEvent(trade);
        } catch (Exception e) {
            logger.error("Error processing trade event: {}", trade, e);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    private void processTradeEvent(AggTradeEvent trade) {
        // Update internal rolling windows for market condition metrics
        updateRollingWindows(trade);
        
        // Update market condition metrics if needed
        tradesProcessedSinceLastMetricsUpdate++;
        if (tradesProcessedSinceLastMetricsUpdate >= marketMetricsUpdateFrequency) {
            updateMarketConditionMetrics();
            updateDynamicVolumeThreshold();
            tradesProcessedSinceLastMetricsUpdate = 0;
        }
        
        // Handle symbol changes
        if (currentSymbol != null && !currentSymbol.equals(trade.getSymbol())) {
            if (currentTradeCount > 0) {
                finalizeCurrentBar(trade.getTimestamp());
            }
            startNewBar(trade);
        } else if (currentSymbol == null) {
            // First trade ever
            startNewBar(trade);
        }
        
        // Add trade to current bar
        addTradeToCurrentBar(trade);
        
        // Check if volume threshold is reached
        if (currentTotalVolume.compareTo(currentDynamicVolumeThreshold) >= 0) {
            finalizeCurrentBar(trade.getTimestamp());
            startNewBar(trade);
        }
    }
    
    @Override
    public MessageChannel getInputChannel() {
        return inputChannel;
    }
    
    @Override
    public Duration getTimeframe() {
        // Adaptive bars don't have fixed timeframes
        return Duration.ZERO;
    }
    
    @Override
    public Flux<AdaptiveBarData> getStream() {
        return barStream;
    }
}
