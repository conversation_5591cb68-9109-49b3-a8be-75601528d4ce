package dev.riza.cuan.strategy;

import dev.riza.cuan.aggregator.TimeBasedOHLCBarAggregator;
import dev.riza.cuan.config.IndicatorRegistry;
import dev.riza.cuan.domain.model.OHLCCandle;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Trading strategy implementation that demonstrates the usage of TimeBasedOHLCBarAggregator
 * for generating OHLC candles and processing market data.
 */
@Component
public class MyStrategy {

    private static final Logger logger = LoggerFactory.getLogger(MyStrategy.class);

    private final TimeBasedOHLCBarAggregator ohlcAggregator;
    private final IndicatorRegistry indicatorRegistry;

    @Autowired
    public MyStrategy(TimeBasedOHLCBarAggregator ohlcAggregator,
                     IndicatorRegistry indicatorRegistry) {
        this.ohlcAggregator = ohlcAggregator;
        this.indicatorRegistry = indicatorRegistry;
    }

    @PostConstruct
    public void initialize() {
        // Register the OHLC aggregator with the indicator registry
        indicatorRegistry.register(ohlcAggregator);

        // Subscribe to OHLC candle updates
        ohlcAggregator.getStream().subscribe(this::onCandleUpdate);

        logger.info("MyStrategy initialized with OHLC aggregator: {}", ohlcAggregator.getConfigInfo());
    }

    /**
     * Handles OHLC candle updates from the aggregator.
     * This method is called for both completed and incomplete candles.
     *
     * @param candle The OHLC candle (completed or incomplete)
     */
    private void onCandleUpdate(OHLCCandle candle) {
        if (candle.isComplete()) {
            onCompletedCandle(candle);
        } else {
            onIncompleteCandle(candle);
        }
    }

    /**
     * Processes completed OHLC candles for trading decisions.
     *
     * @param candle The completed OHLC candle
     */
    private void onCompletedCandle(OHLCCandle candle) {
        logger.info("Completed Candle [{}]: {} | OHLC: {}/{}/{}/{} | Volume: {} | Trades: {}",
                   candle.getTimeInterval(),
                   candle.getSymbol(),
                   candle.getOpen(), candle.getHigh(), candle.getLow(), candle.getClose(),
                   candle.getTotalVolume(),
                   candle.getTradeCount());

        // Example trading logic based on completed candles
        analyzeCandle(candle);
    }

    /**
     * Processes incomplete OHLC candles for real-time monitoring.
     *
     * @param candle The incomplete OHLC candle
     */
    private void onIncompleteCandle(OHLCCandle candle) {
        // Log incomplete candles less frequently to avoid spam
        if (candle.getTradeCount() % 10 == 0) {
            logger.debug("Incomplete Candle [{}]: {} | Current: {} | Trades: {} | Volume: {}",
                        candle.getTimeInterval(),
                        candle.getSymbol(),
                        candle.getClose(),
                        candle.getTradeCount(),
                        candle.getTotalVolume());
        }
    }

    /**
     * Analyzes completed candles for trading opportunities.
     *
     * @param candle The completed candle to analyze
     */
    private void analyzeCandle(OHLCCandle candle) {
        // Example: Simple price movement analysis
        if (candle.getOpen() != null && candle.getClose() != null) {
            double priceChange = candle.getClose().subtract(candle.getOpen()).doubleValue();
            double priceChangePercent = (priceChange / candle.getOpen().doubleValue()) * 100;

            if (Math.abs(priceChangePercent) > 0.1) { // 0.1% threshold
                logger.info("Significant price movement detected: {:.2f}% in {} candle",
                           String.format("%.2f", priceChangePercent), candle.getTimeInterval());
            }
        }

        // Example: Volume analysis
        if (candle.getBuyVolume() != null && candle.getSellVolume() != null) {
            double buyRatio = candle.getBuyVolume().doubleValue() / candle.getTotalVolume().doubleValue();
            if (buyRatio > 0.7) {
                logger.info("Strong buying pressure detected: {}% buy volume", String.format("%.1f", buyRatio * 100));
            } else if (buyRatio < 0.3) {
                logger.info("Strong selling pressure detected: {}% sell volume", String.format("%.1f", (1 - buyRatio) * 100));
            }
        }
    }

    /**
     * Gets statistics about the current strategy state.
     *
     * @return Statistics string
     */
    public String getStrategyStatistics() {
        return String.format("MyStrategy Stats: %s", ohlcAggregator.getStatistics());
    }
}
