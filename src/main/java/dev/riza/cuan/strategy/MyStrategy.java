package dev.riza.cuan.strategy;

import dev.riza.cuan.aggregator.AdaptiveBarAggregator;
import dev.riza.cuan.config.IndicatorRegistry;
import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.domain.model.AdaptiveBar;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.MarketConditionEvent;
import dev.riza.cuan.indicator.MarketActivityMonitor;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

/**
 * Trading strategy implementation that uses the VWAPIndicatorAdaptive for generating
 * trading signals and executes trades through the simulation system.
 */
@Component
public class MyStrategy {

    private final MarketActivityMonitor marketActivityMonitor;
    private final IndicatorRegistry indicatorRegistry;
    private final AdaptiveBarAggregator adaptiveBarAggregator;
    private MarketConditionEvent marketConditionEvent;

    @Autowired
    public MyStrategy(MarketActivityMonitor marketActivityMonitor,
                      IndicatorRegistry indicatorRegistry, AdaptiveBarAggregator adaptiveBarAggregator) {
        this.marketActivityMonitor = marketActivityMonitor;
        this.indicatorRegistry = indicatorRegistry;
        this.adaptiveBarAggregator = adaptiveBarAggregator;
    }

    @PostConstruct
    public void initialize() {
        indicatorRegistry.register(marketActivityMonitor);
        marketActivityMonitor.getStream()
                .doOnNext(marketConditionEvent -> this.marketConditionEvent = marketConditionEvent)
                .subscribe();
        adaptiveBarAggregator.getStream().subscribe(this::onBarUpdate);

    }

    private void onBarUpdate(AdaptiveBar adaptiveBar) {
        System.out.println("Bar Update: " + adaptiveBar);
    }

    @ServiceActivator(inputChannel = "aggTradeChannel")
    public void handleMarketData(Message<AggTradeEvent> message) {
        adaptiveBarAggregator.onMarketData(message, marketConditionEvent);

    }
}
