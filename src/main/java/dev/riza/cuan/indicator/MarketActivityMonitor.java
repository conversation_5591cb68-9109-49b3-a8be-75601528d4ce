package dev.riza.cuan.core.analyzer;



import dev.riza.cuan.config.MarketDataGateway;
import dev.riza.cuan.core.model.MarketConditionEvent;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.util.CircularBuffer;
import dev.riza.cuan.util.StatisticalUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Component
public class MarketActivityMonitor {

    private final MarketDataGateway marketDataGateway;
    private final CircularBuffer<BigDecimal> recentPrices;
    private final CircularBuffer<AggTradeEvent> recentTradesForVolumeRate;
    private final CircularBuffer<Instant> tradeTimestamps;

    private final int volatilityWindowSize;
    private final int volumeRateWindowSize;
    private final Duration tpsWindowDuration;

    private static final MathContext MC = new MathContext(10, RoundingMode.HALF_UP);
    private int tradeCount = 0;
    private final int publishConditionEventEveryNTrades;

    @Autowired
    public MarketActivityMonitor(MarketDataGateway marketDataGateway,
                                 @Value("${core.analyzer.volatilityWindowSize:20}") int volatilityWindowSize,
                                 @Value("${core.analyzer.volumeRateWindowSize:50}") int volumeRateWindowSize,
                                 @Value("${core.analyzer.tpsWindowSeconds:10}") int tpsWindowSeconds,
                                 @Value("${core.analyzer.publishEventEvery:5}") int publishConditionEventEveryNTrades) {
        this.marketDataGateway = marketDataGateway;
        this.volatilityWindowSize = volatilityWindowSize;
        this.volumeRateWindowSize = volumeRateWindowSize;
        this.tpsWindowDuration = Duration.ofSeconds(tpsWindowSeconds);
        this.publishConditionEventEveryNTrades = publishConditionEventEveryNTrades;

        this.recentPrices = new CircularBuffer<>(volatilityWindowSize);
        this.recentTradesForVolumeRate = new CircularBuffer<>(volumeRateWindowSize);
        this.tradeTimestamps = new CircularBuffer<>(200); // Cukup besar untuk menampung trade dalam tpsWindowDuration
    }

    @ServiceActivator(inputChannel = "aggTradeChannel")
    public void handleAggTrade(Message<AggTradeEvent> message) {
        AggTradeEvent trade = message.getPayload();
        Instant now = trade.getTimestamp(); // Gunakan timestamp trade

        // Update untuk Volatilitas
        recentPrices.add(trade.getPrice());

        // Update untuk Volume Rate
        recentTradesForVolumeRate.add(trade);

        // Update untuk TPS
        tradeTimestamps.add(now);
        cleanupOldTimestamps(now);

        tradeCount++;
        if (tradeCount >= publishConditionEventEveryNTrades) {
            tradeCount = 0;
            publishMarketCondition(now);
        }
    }

    private void cleanupOldTimestamps(Instant now) {
        Instant cutoff = now.minus(tpsWindowDuration);
        // Hapus timestamp yang lebih tua dari cutoff
        // Implementasi di CircularBuffer mungkin perlu penyesuaian atau lakukan di sini
        List<Instant> currentTimestamps = tradeTimestamps.toList();
        tradeTimestamps.clear(); // Hati-hati, ini akan menghapus semua jika tidak dilakukan dengan benar
        for (Instant ts : currentTimestamps) {
            if (!ts.isBefore(cutoff)) {
                tradeTimestamps.add(ts); // Tambahkan kembali yang masih valid
            }
        }
    }

    private void publishMarketCondition(Instant timestamp) {
        BigDecimal volatility = BigDecimal.ZERO;
        if (recentPrices.size() >= 2) { // Perlu minimal 2 data poin untuk standar deviasi
            volatility = StatisticalUtils.standardDeviation(recentPrices.toList(), MC);
        }

        double tps = 0.0;
        if (tradeTimestamps.size() > 1) {
            List<Instant> currentTimestamps = tradeTimestamps.toList();
            Duration duration = Duration.between(currentTimestamps.get(0), currentTimestamps.get(currentTimestamps.size() - 1));
            if (!duration.isZero()) {
                tps = (double) currentTimestamps.size() / duration.toSeconds();
            } else if (currentTimestamps.size() > 0) {
                tps = currentTimestamps.size(); // Jika semua dalam 1 detik
            }
        }


        BigDecimal volumeRate = BigDecimal.ZERO;
        if (!recentTradesForVolumeRate.isEmpty()) {
            for (AggTradeEvent t : recentTradesForVolumeRate.toList()) {
                volumeRate = volumeRate.add(t.getQuantity());
            }
            // Bisa dinormalisasi per detik jika diinginkan
        }

        MarketConditionEvent conditionEvent = MarketConditionEvent.builder()
                .timestamp(timestamp)
                .rollingVolatility(volatility)
                .tradesPerSecond(tps)
                .volumeRate(volumeRate)
                .build();

        // System.out.println("Publishing MarketCondition: " + conditionEvent);
        marketDataGateway.publish(conditionEvent); // Perlu channel baru di gateway dan config
    }
}
