package dev.riza.cuan.config;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class IndicatorRegistry {

    private final Set<MarketDataIndicator> activeIndicators = ConcurrentHashMap.newKeySet();

    public void register(MarketDataIndicator indicator) {
        activeIndicators.add(indicator);
    }

    public void unregister(MarketDataIndicator indicator) {
        activeIndicators.remove(indicator);
    }

    public Set<MarketDataIndicator> getActiveIndicators() {
        return Collections.unmodifiableSet(activeIndicators);
    }
}
