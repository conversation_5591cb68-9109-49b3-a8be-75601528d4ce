package dev.riza.cuan.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.messaging.MessageChannel;

@Configuration
public class IntegrationChannelConfig {

    @Bean
    public MessageChannel depthDataChannel() {
        return new DirectChannel(); // SubscribableChannel
    }

    @Bean
    public MessageChannel aggTradeChannel() {
        return new DirectChannel(); // SubscribableChannel
    }

    @Bean
    public MessageChannel ohlcCandleChannel() {
        return new DirectChannel(); // SubscribableChannel for OHLC candles
    }

    @Bean
    public MessageChannel adaptiveBarChannel() {
        return new DirectChannel(); // SubscribableChannel for adaptive bars
    }

    @Bean
    public IntegrationFlow marketDataRouterFlow(IndicatorRouter router) {
        return IntegrationFlow
                .from("aggTradeChannel")
                .route(router)
                .get();
    }
}
