package dev.riza.cuan.config;


import dev.riza.cuan.domain.model.AdaptiveBarData;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.OHLCCandle;
import dev.riza.cuan.domain.model.OrderBookSnapshot;
import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;

@MessagingGateway
public interface MarketDataGateway {

    @Gateway(requestChannel = "depthDataChannel")
    void publish(OrderBookSnapshot event);

    @Gateway(requestChannel = "aggTradeChannel")
    void publish(AggTradeEvent trade);

    @Gateway(requestChannel = "ohlcCandleChannel")
    void publish(OHLCCandle candle);

    @Gateway(requestChannel = "adaptiveBarChannel")
    void publish(AdaptiveBarData adaptiveBar);
}
