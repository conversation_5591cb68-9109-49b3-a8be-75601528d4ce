package dev.riza.cuan.config;


import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.OrderBookSnapshot;
import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;

@MessagingGateway
public interface MarketDataGateway {

    @Gateway(requestChannel = "depthDataChannel")
    void publish(OrderBookSnapshot event);

    @Gateway(requestChannel = "aggTradeChannel")
    void publish(AggTradeEvent trade);
}
