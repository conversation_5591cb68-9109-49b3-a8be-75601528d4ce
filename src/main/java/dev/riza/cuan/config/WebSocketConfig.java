package dev.riza.cuan.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import dev.riza.cuan.websocket.AdaptiveBarWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * WebSocket configuration for real-time market data streaming.
 * Configures endpoints for streaming adaptive bars and other market data.
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private final AdaptiveBarWebSocketHandler adaptiveBarHandler;

    @Autowired
    public WebSocketConfig(AdaptiveBarWebSocketHandler adaptiveBarHandler) {
        this.adaptiveBarHandler = adaptiveBarHandler;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // Register adaptive bar WebSocket endpoint
        registry.addHandler(adaptiveBarHandler, "/ws/adaptive-bars")
                .setAllowedOrigins("*"); // Allow all origins for development
    }
}
