package dev.riza.cuan.domain.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * Represents an adaptive volume-based bar with market condition metrics.
 * This model contains OHLC data, volume metrics, and the market conditions
 * that were used to determine when the bar should be closed.
 */
@Data
@Builder
public class AdaptiveBarData {
    
    /**
     * The timestamp when this bar period opened (first trade)
     */
    private Instant openTime;
    
    /**
     * The timestamp when this bar period closed (last trade)
     */
    private Instant closeTime;
    
    /**
     * The trading symbol for this bar (e.g., "BTCUSDT")
     */
    private String symbol;
    
    /**
     * The opening price of the bar (first trade price)
     */
    private BigDecimal openPrice;
    
    /**
     * The highest price during the bar period
     */
    private BigDecimal highPrice;
    
    /**
     * The lowest price during the bar period
     */
    private BigDecimal lowPrice;
    
    /**
     * The closing price of the bar (last trade price)
     */
    private BigDecimal closePrice;
    
    /**
     * Volume-weighted average price for the bar period
     */
    private BigDecimal volumeWeightedAveragePrice;
    
    /**
     * Total volume traded during the bar period
     */
    private BigDecimal totalVolumeTraded;
    
    /**
     * Volume from aggressive buy trades (buyer was taker)
     */
    private BigDecimal aggressiveBuyVolume;
    
    /**
     * Volume from aggressive sell trades (seller was taker)
     */
    private BigDecimal aggressiveSellVolume;
    
    /**
     * Total number of trades that occurred during the bar period
     */
    private int tradeCount;
    
    /**
     * The reason why this bar was closed
     */
    private String closingReason;
    
    /**
     * Snapshot of market conditions when this bar was closed
     */
    private MarketConditionSnapshot marketConditionSnapshotAtClose;
    
    /**
     * Inner class representing market condition metrics at bar close
     */
    @Data
    @Builder
    public static class MarketConditionSnapshot {
        
        /**
         * Rolling volatility (standard deviation of recent prices)
         */
        private BigDecimal rollingVolatility;
        
        /**
         * Current trades per second rate
         */
        private double tradesPerSecond;
        
        /**
         * Recent volume rate (average volume per trade)
         */
        private BigDecimal recentVolumeRate;
        
        /**
         * The dynamic volume threshold that was used for this bar
         */
        private BigDecimal dynamicVolumeThreshold;
        
        /**
         * Base volume threshold before adjustments
         */
        private BigDecimal baseVolumeThreshold;
        
        /**
         * Volatility adjustment factor applied
         */
        private double volatilityAdjustment;
        
        /**
         * TPS adjustment factor applied
         */
        private double tpsAdjustment;
        
        /**
         * Volume rate adjustment factor applied (if used)
         */
        private double volumeRateAdjustment;
        
        /**
         * Timestamp when these metrics were calculated
         */
        private Instant calculatedAt;
        
        /**
         * Number of trades used in volatility calculation
         */
        private int volatilityWindowSize;
        
        /**
         * Duration used for TPS calculation
         */
        private long tpsWindowDurationSeconds;
        
        /**
         * Number of trades used in volume rate calculation
         */
        private int volumeRateWindowSize;
    }
}
