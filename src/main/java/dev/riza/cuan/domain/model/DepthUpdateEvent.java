package dev.riza.cuan.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

@Getter
@ToString(exclude = {"bids", "asks"})
public class DepthUpdateEvent {

    @JsonProperty("e")
    private String eventType;

    @JsonProperty("E")
    private long eventTime;

    @JsonProperty("T")
    private long transactionTime;

    @JsonProperty("s")
    private String symbol;

    @JsonProperty("U")
    private long firstUpdateId;  // U

    @JsonProperty("u")
    private long finalUpdateId;  // u

    @JsonProperty("pu")
    private long previousFinalUpdateId; // pu

    @JsonProperty("b")
    private List<List<String>> bids;

    @JsonProperty("a")
    private List<List<String>> asks;
    
    public String getDebugInfo() {
        return String.format("DepthEvent{symbol=%s, U=%d, u=%d, pu=%d, bids=%d, asks=%d}", 
            symbol, firstUpdateId, finalUpdateId, previousFinalUpdateId, 
            bids != null ? bids.size() : 0, 
            asks != null ? asks.size() : 0);
    }
}
