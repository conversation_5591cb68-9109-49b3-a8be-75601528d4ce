package dev.riza.cuan.domain.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * Represents an OHLC (Open, High, Low, Close) candle for time-based market data aggregation.
 * This model contains all the essential information for a trading candle including price data,
 * volume metrics, and timing information.
 */
@Data
@Builder
public class OHLCCandle {
    
    /**
     * The timestamp when this candle period opened
     */
    private Instant openTime;
    
    /**
     * The timestamp when this candle period closed
     */
    private Instant closeTime;
    
    /**
     * The trading symbol for this candle (e.g., "BTCUSDT")
     */
    private String symbol;
    
    /**
     * The opening price of the candle period
     */
    private BigDecimal open;
    
    /**
     * The highest price during the candle period
     */
    private BigDecimal high;
    
    /**
     * The lowest price during the candle period
     */
    private BigDecimal low;
    
    /**
     * The closing price of the candle period
     */
    private BigDecimal close;
    
    /**
     * Volume-weighted average price for the candle period
     */
    private BigDecimal vwap;
    
    /**
     * Total volume traded during the candle period
     */
    private BigDecimal totalVolume;
    
    /**
     * Volume from buyer-initiated trades
     */
    private BigDecimal buyVolume;
    
    /**
     * Volume from seller-initiated trades
     */
    private BigDecimal sellVolume;
    
    /**
     * Total number of trades that occurred during the candle period
     */
    private int tradeCount;
    
    /**
     * The time interval for this candle (e.g., "1m", "5m", "1h")
     */
    private String timeInterval;
    
    /**
     * Indicates whether this candle is complete (closed) or still being formed
     */
    private boolean isComplete;
    
    /**
     * Additional metadata about how this candle was formed
     */
    private String metadata;
}
