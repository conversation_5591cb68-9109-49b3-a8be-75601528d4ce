package dev.riza.cuan.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import dev.riza.cuan.aggregator.AdaptiveVolumeBarAggregator;
import dev.riza.cuan.domain.model.AdaptiveBarData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import reactor.core.Disposable;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * WebSocket handler for streaming adaptive bar data to connected clients.
 * Subscribes to the AdaptiveVolumeBarAggregator stream and broadcasts
 * completed bars to all connected WebSocket clients.
 */
@Component
public class AdaptiveBarWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(AdaptiveBarWebSocketHandler.class);

    private final AdaptiveVolumeBarAggregator adaptiveAggregator;
    private final ObjectMapper objectMapper;
    private final ConcurrentMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private Disposable subscription;

    @Autowired
    public AdaptiveBarWebSocketHandler(AdaptiveVolumeBarAggregator adaptiveAggregator) {
        this.adaptiveAggregator = adaptiveAggregator;
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        
        // Subscribe to adaptive bar stream
        subscribeToAdaptiveBars();
    }

    private void subscribeToAdaptiveBars() {
        subscription = adaptiveAggregator.getStream()
                .subscribe(
                    this::broadcastAdaptiveBar,
                    error -> logger.error("Error in adaptive bar stream", error),
                    () -> logger.info("Adaptive bar stream completed")
                );
        
        logger.info("Subscribed to adaptive bar stream for WebSocket broadcasting");
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        
        logger.info("WebSocket connection established: {} (Total connections: {})", 
                   sessionId, sessions.size());
        
        // Send welcome message with current market conditions
        sendWelcomeMessage(session);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String payload = message.getPayload().toString();
        logger.debug("Received WebSocket message from {}: {}", session.getId(), payload);
        
        // Handle client messages (e.g., subscription requests, configuration)
        try {
            handleClientMessage(session, payload);
        } catch (Exception e) {
            logger.error("Error handling WebSocket message from {}: {}", session.getId(), payload, e);
            sendErrorMessage(session, "Error processing message: " + e.getMessage());
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket transport error for session {}: {}", session.getId(), exception.getMessage());
        sessions.remove(session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);
        
        logger.info("WebSocket connection closed: {} - {} (Remaining connections: {})", 
                   sessionId, closeStatus, sessions.size());
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * Broadcasts an adaptive bar to all connected WebSocket clients.
     */
    private void broadcastAdaptiveBar(AdaptiveBarData adaptiveBar) {
        if (sessions.isEmpty()) {
            return; // No clients connected
        }

        try {
            // Create WebSocket message
            WebSocketMessage message = createAdaptiveBarMessage(adaptiveBar);
            String jsonMessage = objectMapper.writeValueAsString(message);

            // Broadcast to all connected sessions
            sessions.values().parallelStream().forEach(session -> {
                try {
                    if (session.isOpen()) {
                        session.sendMessage(new TextMessage(jsonMessage));
                    } else {
                        // Remove closed sessions
                        sessions.remove(session.getId());
                    }
                } catch (IOException e) {
                    logger.error("Error sending adaptive bar to WebSocket session {}: {}", 
                               session.getId(), e.getMessage());
                    sessions.remove(session.getId());
                }
            });

            logger.debug("Broadcasted adaptive bar to {} WebSocket clients: {} [{}]", 
                        sessions.size(), adaptiveBar.getSymbol(), adaptiveBar.getCloseTime());

        } catch (Exception e) {
            logger.error("Error broadcasting adaptive bar: {}", adaptiveBar, e);
        }
    }

    /**
     * Creates a WebSocket message for an adaptive bar.
     */
    private WebSocketMessage createAdaptiveBarMessage(AdaptiveBarData adaptiveBar) {
        return WebSocketMessage.builder()
                .type("ADAPTIVE_BAR")
                .timestamp(adaptiveBar.getCloseTime())
                .symbol(adaptiveBar.getSymbol())
                .data(adaptiveBar)
                .build();
    }

    /**
     * Sends a welcome message to a newly connected client.
     */
    private void sendWelcomeMessage(WebSocketSession session) {
        try {
            WebSocketMessage welcomeMessage = WebSocketMessage.builder()
                    .type("WELCOME")
                    .timestamp(java.time.Instant.now())
                    .symbol("SYSTEM")
                    .data(WelcomeData.builder()
                            .message("Connected to Adaptive Bar WebSocket")
                            .aggregatorConfig(adaptiveAggregator.getConfigInfo())
                            .currentMarketConditions(adaptiveAggregator.getCurrentMarketConditions())
                            .currentBarStats(adaptiveAggregator.getCurrentBarStatistics())
                            .build())
                    .build();

            String jsonMessage = objectMapper.writeValueAsString(welcomeMessage);
            session.sendMessage(new TextMessage(jsonMessage));

        } catch (Exception e) {
            logger.error("Error sending welcome message to session {}: {}", session.getId(), e.getMessage());
        }
    }

    /**
     * Handles client messages (subscription requests, etc.).
     */
    private void handleClientMessage(WebSocketSession session, String payload) throws Exception {
        // Parse client message and handle accordingly
        // For now, just echo back a confirmation
        WebSocketMessage response = WebSocketMessage.builder()
                .type("CONFIRMATION")
                .timestamp(java.time.Instant.now())
                .symbol("SYSTEM")
                .data("Message received: " + payload)
                .build();

        String jsonResponse = objectMapper.writeValueAsString(response);
        session.sendMessage(new TextMessage(jsonResponse));
    }

    /**
     * Sends an error message to a specific client.
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) {
        try {
            WebSocketMessage errorMsg = WebSocketMessage.builder()
                    .type("ERROR")
                    .timestamp(java.time.Instant.now())
                    .symbol("SYSTEM")
                    .data(errorMessage)
                    .build();

            String jsonMessage = objectMapper.writeValueAsString(errorMsg);
            session.sendMessage(new TextMessage(jsonMessage));

        } catch (Exception e) {
            logger.error("Error sending error message to session {}: {}", session.getId(), e.getMessage());
        }
    }

    /**
     * Gets the number of connected WebSocket clients.
     */
    public int getConnectedClientsCount() {
        return sessions.size();
    }

    /**
     * Cleanup method to dispose of the subscription when the handler is destroyed.
     */
    public void destroy() {
        if (subscription != null && !subscription.isDisposed()) {
            subscription.dispose();
            logger.info("Disposed adaptive bar stream subscription");
        }
    }

    /**
     * WebSocket message structure for JSON serialization.
     */
    public static class WebSocketMessage {
        private String type;
        private java.time.Instant timestamp;
        private String symbol;
        private Object data;

        // Builder pattern
        public static WebSocketMessageBuilder builder() {
            return new WebSocketMessageBuilder();
        }

        // Getters and setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public java.time.Instant getTimestamp() { return timestamp; }
        public void setTimestamp(java.time.Instant timestamp) { this.timestamp = timestamp; }
        
        public String getSymbol() { return symbol; }
        public void setSymbol(String symbol) { this.symbol = symbol; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }

        public static class WebSocketMessageBuilder {
            private String type;
            private java.time.Instant timestamp;
            private String symbol;
            private Object data;

            public WebSocketMessageBuilder type(String type) { this.type = type; return this; }
            public WebSocketMessageBuilder timestamp(java.time.Instant timestamp) { this.timestamp = timestamp; return this; }
            public WebSocketMessageBuilder symbol(String symbol) { this.symbol = symbol; return this; }
            public WebSocketMessageBuilder data(Object data) { this.data = data; return this; }

            public WebSocketMessage build() {
                WebSocketMessage message = new WebSocketMessage();
                message.type = this.type;
                message.timestamp = this.timestamp;
                message.symbol = this.symbol;
                message.data = this.data;
                return message;
            }
        }
    }

    /**
     * Welcome message data structure.
     */
    public static class WelcomeData {
        private String message;
        private String aggregatorConfig;
        private AdaptiveBarData.MarketConditionSnapshot currentMarketConditions;
        private String currentBarStats;

        public static WelcomeDataBuilder builder() {
            return new WelcomeDataBuilder();
        }

        // Getters and setters
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getAggregatorConfig() { return aggregatorConfig; }
        public void setAggregatorConfig(String aggregatorConfig) { this.aggregatorConfig = aggregatorConfig; }
        
        public AdaptiveBarData.MarketConditionSnapshot getCurrentMarketConditions() { return currentMarketConditions; }
        public void setCurrentMarketConditions(AdaptiveBarData.MarketConditionSnapshot currentMarketConditions) { this.currentMarketConditions = currentMarketConditions; }
        
        public String getCurrentBarStats() { return currentBarStats; }
        public void setCurrentBarStats(String currentBarStats) { this.currentBarStats = currentBarStats; }

        public static class WelcomeDataBuilder {
            private String message;
            private String aggregatorConfig;
            private AdaptiveBarData.MarketConditionSnapshot currentMarketConditions;
            private String currentBarStats;

            public WelcomeDataBuilder message(String message) { this.message = message; return this; }
            public WelcomeDataBuilder aggregatorConfig(String aggregatorConfig) { this.aggregatorConfig = aggregatorConfig; return this; }
            public WelcomeDataBuilder currentMarketConditions(AdaptiveBarData.MarketConditionSnapshot currentMarketConditions) { this.currentMarketConditions = currentMarketConditions; return this; }
            public WelcomeDataBuilder currentBarStats(String currentBarStats) { this.currentBarStats = currentBarStats; return this; }

            public WelcomeData build() {
                WelcomeData data = new WelcomeData();
                data.message = this.message;
                data.aggregatorConfig = this.aggregatorConfig;
                data.currentMarketConditions = this.currentMarketConditions;
                data.currentBarStats = this.currentBarStats;
                return data;
            }
        }
    }
}
