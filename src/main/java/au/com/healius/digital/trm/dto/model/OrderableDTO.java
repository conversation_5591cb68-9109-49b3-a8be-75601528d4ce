package au.com.healius.digital.trm.dto.model;


import au.com.healius.digital.trm.domain.enumeration.RebatabilityOptions;
import lombok.Data;

import java.io.Serializable;
import java.time.OffsetDateTime;

@Data
public class OrderableDTO implements Serializable, Comparable<OrderableDTO> {

    private String id;

    private String name;
    private String nameSearch;
    private String code;

    private String rcpaName;
    private String rcpaNameSearch;
    private String snomedName;
    private String snomedNameSearch;
    private String snomedCode;
    private String snomedAUCode;

    private String qmlName;
    private String qmlCode;
    private String lavName;
    private String lavCode;
    private String dorName;
    private String dorCode;
    private String wdpName;
    private String wdpCode;
    private String abbottName;
    private String abbottCode;
    private String tmlName;
    private String tmlCode;

    private String department;
    private String synonyms;
    private String synonymsSearch;
    private String information;

    private boolean commonTest;
    private boolean isCytology;
    private boolean findUsSpecialTest;
    private boolean isReplaceByOtherTest;
    private boolean bookable;
    private boolean timeslotBookable;

    private String replacementSnomedCode;

    private OffsetDateTime replacementSnomedCodeEffectiveDate;

    private String replacesSnomedCode;

    private OffsetDateTime replacesSnomedCodeEffectiveDate;

    @Deprecated(forRemoval = false, since = "20/01/2025")
    //TODO: Check if removing breaks anything
    private RebatabilityOptions rebatability;

    private RebatabilityOptions rebatabilityState;

    private OrderableFindUsDTO findUsBookingInfo;

    @Override
    public int compareTo(OrderableDTO o) {
        return this.name.compareTo(o.name);
    }
}
