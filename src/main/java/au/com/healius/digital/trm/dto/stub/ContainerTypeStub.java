package au.com.healius.digital.trm.dto.stub;

import au.com.healius.digital.trm.domain.enumeration.ContainerTypeImages;

import java.time.ZonedDateTime;

public interface ContainerTypeStub {

    String getId();
    ZonedDateTime getCreatedDate();
    String getCreatedBy();
    ZonedDateTime getModifiedDate();
    String getModifiedBy();

    String getName();
    ContainerTypeImages getIcon();
    int getSequence();
    boolean getActive();

    boolean getHidden();

    boolean getCommonContainerType();

}