package au.com.healius.digital.trm.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
public class CompanyDTO implements Serializable {

    private String id;
    private String idOld;
    private int seq;
//    private ZonedDateTime updateDT;
    private int buId;
    private int companyId;
    private String name;
    private String shortName;
    private String code;
    private int nataCode;
    private String description;
    private String smsUsername;
    private String smsPassword;
    @ToString.Exclude
    private String eReferralMessage;
    @ToString.Exclude
    private String eReferralLateMessage;
    @ToString.Exclude
    private String eReferralErrorDOBMessage;
    private String eReferralURL;
//    private Double mapCenterX;
//    private Double mapCenterY;
//    private Double mapZoomInit;
//    private Double mapZoomMax;
//    private Double mapZoomMin;
//    private String mapComponentRestrictionsCountry;
//    private String mapKey;
//    private String mapKeyProd;
//    private String mapKeyDev;
//    private String mapKeyLocal;
//    private Integer phoneAreaCode;
//    private String callCenterPhone;
//    private LabDTO centralLab;
//    private List<LabDTO> labs;
//    private String domain;
//    private String onlineBookingCompanyName;
//    private String onlineBookingContactPhone;
//    private String onlineBookingContactName;
//    private String trm;
//    private Boolean trmUseUltra;
//    private String collect;
//    private String accLiveLocationsExternal;
//    private Boolean appendFaxToPhone;
    private boolean allowCollectorPortalR1;
    private boolean allowCollectorPortal;
//    private String themeName;
//    private String homeURL;
//    private String addressLine1;
//    private String addressLine2;
//    private String suburb;
//    private State state;
//    private String postalCode;
//    private String contactPhone;
//    private String emailAddress;
//    private String displayName;
//    private ReferrerType providerType;
//    private int dropDownListOrder;
//    private double latitude;
//    private double longitude;
//    private String timeZone;
//    private short formattedReportType;
    private String website;
    private String statement;

    private String additionalTestEmail;
    private String medicineSpecialistConsultationEmail;
    private String pendingAssociationNotificationEmail;
//    private String parentCompany;

    private boolean medWayEnabled;
    private String state;

    private String invoiceAddressBlurb;
}
