package au.com.healius.digital.trm.dto.model;

import au.com.healius.digital.trm.domain.enumeration.CollectionFrequency;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
@AllArgsConstructor
public class SearchDTO {

  private String name;
  private String code;
  private String neo4jId;
  private String synonyms;
  private Boolean testAvailable;
  private String rebatability;
  private Integer specimensRequired;
  private Boolean isReplaceByOtherTest;
  private OffsetDateTime currentOrderableEffectDate;
  private CollectionFrequency collectionFrequency;
  private String orderableCurrentName;
  private String orderableCurrentNeo4jId;
  private Boolean apartOfGroup;
  private Float rank;

}
