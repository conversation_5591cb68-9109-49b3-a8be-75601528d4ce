package au.com.healius.digital.trm.dto.stub;

import au.com.healius.digital.trm.domain.enumeration.ContainerImages;
import au.com.healius.digital.trm.domain.enumeration.ContainerLayout;

import java.time.ZonedDateTime;

public interface ContainerStub {

    String getId();
    ZonedDateTime getCreatedDate();
    String getCreatedBy();
    ZonedDateTime getModifiedDate();
    String getModifiedBy();
    boolean getActive();
    boolean getCommonContainer();

    String getName();
    int getLabelCount();
    String getSummaryLabel();
    String getColourDescription();
    String getSpecification();
    String getTextColourCode();
    String getFillColourCode();
    String getBorderColourCode();
    String getDescription();
    int getSequence();
    ContainerImages getImage();
    ContainerLayout getContainerLayout();

}
