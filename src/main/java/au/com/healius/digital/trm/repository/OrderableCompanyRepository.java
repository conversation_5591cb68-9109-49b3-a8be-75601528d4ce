package au.com.healius.digital.trm.repository;

import au.com.healius.digital.trm.domain.OrderableCompany;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Repository interface for managing OrderableCompany entities in the Neo4j database.
 * Provides methods for searching and retrieving orderable companies based on various criteria
 * such as business unit, visibility, and test availability.
 */
public interface OrderableCompanyRepository extends Neo4jRepository<OrderableCompany, String> {

  /**
   * Performs a fulltext search for orderable companies based on search criteria, business unit, and application visibility.
   * Results are weighted based on matches in code, name, and synonyms fields.
   * Only returns companies where the test is available.
   *
   * @param search The search term to match against
   * @param bu The business unit code to filter by
   * @param application The application context for visibility filtering
   * @return List of matching OrderableCompany entities, ordered by weighted relevance score
   */
  @Transactional(readOnly = true)
  @Query("""
      CALL db.index.fulltext.queryNodes('OrderableCompany_fulltext_index', $search)
                  YIELD node, score
                  WHERE node.bu = $bu AND $application in node.visibilities AND node.testAvailable = true AND node.enabled = true
                  RETURN node, score
                  ORDER BY score DESC
      """)
  List<OrderableCompany> findBySearch(@Param("search") String search, @Param("bu") String bu,
      @Param("application") String application);

  /**
   * Finds orderable companies based on common test status, application visibility, and business unit.
   *
   * @param commonTest Whether to filter for common tests
   * @param application The application context for visibility filtering
   * @param bu The business unit code to filter by
   * @return List of matching OrderableCompany entities
   */
  @Transactional(readOnly = true)
  @Query("""
      MATCH (oc:OrderableCompany)
      WHERE oc.commonTest = $commonTest AND oc.bu = $bu AND $application in oc.visibilities
      return oc
      """)
  List<OrderableCompany> findByCommon(@Param("commonTest") boolean commonTest,
      @Param("application") String application, @Param("bu") String bu);

  /**
   * Finds an orderable company by its company orderable ID.
   *
   * @param companyOrderableId The company orderable ID to search for
   * @return The matching OrderableCompany entity
   */
  OrderableCompany findByCompanyOrderableId(@Param("companyOrderableId") String companyOrderableId);
}
