package au.com.healius.digital.trm.domain;


import au.com.healius.digital.trm.domain.enumeration.InstantResultsDisplayType;
import au.com.healius.digital.trm.domain.enumeration.InstantResultsType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Node("InstantResult")
public class InstantResult extends Entity implements Serializable {

    private String name;
    private InstantResultsType type;
    private String measurement;
    private Long minValue;
    private Long maxValue;
    private Long decimals;
    private InstantResultsDisplayType displayType;
    private String errorMessage;
    private Boolean active;

    @Relationship(value = "INSTANT_RESULTS_ORDERABLE", direction = Relationship.Direction.OUTGOING)
    private List<Orderable> orderables = new LinkedList<>();

    @Relationship(value = "COMPANY_INSTANT_RESULTS", direction = Relationship.Direction.OUTGOING)
    private List<CompanyInstantResult> companyInstantResults;
}
