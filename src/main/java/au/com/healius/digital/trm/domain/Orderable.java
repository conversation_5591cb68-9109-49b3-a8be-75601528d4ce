package au.com.healius.digital.trm.domain;

import au.com.healius.digital.trm.domain.enumeration.OrderableVisibility;
import au.com.healius.digital.trm.domain.enumeration.RebatabilityOptions;
import au.com.healius.digital.trm.domain.enumeration.RebatabilityType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.LinkedList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Node("Orderable")
public class Orderable extends Entity implements Serializable, Comparable<Orderable> {

    private String name;
    private String nameSearch;
    private String code;

    private String rcpaName;
    private String rcpaNameSearch;
    private String snomedName;
    private String snomedNameSearch;
    private String snomedCode;
    private String snomedAUCode;

    private String department;
    private String synonyms;
    private List<String> synonymList = new LinkedList<>();
    private String synonymsSearch;
    private String information;

    private String resultsAvailable;
    private String testFrequency;

    private String prepaymentUrl;

    private boolean commonTest;
    private boolean testAvailable;
    private boolean isCytology;
    private boolean findUsSpecialTest;
    private boolean isReplaceByOtherTest;

    @Deprecated(forRemoval = false, since = "20/01/2025")
    private RebatabilityType rebatability;

    private RebatabilityOptions rebatabilityState;

    private List<OrderableVisibility> visibilities = new LinkedList<>();

    // Used by the older orderable
    @Relationship(value = "ORDERABLE_REPLACED", direction = Relationship.Direction.OUTGOING)
    private Orderable currentOrderable;
    private OffsetDateTime currentOrderableEffectiveDate;

    // Used by the replaced orderable
    @Relationship(value = "ORDERABLE_REPLACED", direction = Relationship.Direction.INCOMING)
    private Orderable formerOrderable;
    private OffsetDateTime formerOrderableEffectiveDate;


    @Override
    public int compareTo(Orderable o) {
        return name.compareTo(o.getName());
    }
}
