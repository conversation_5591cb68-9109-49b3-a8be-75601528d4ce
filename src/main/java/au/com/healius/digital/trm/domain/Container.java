package au.com.healius.digital.trm.domain;

import au.com.healius.digital.trm.domain.enumeration.ContainerImages;
import au.com.healius.digital.trm.domain.enumeration.ContainerLayout;
import com.google.common.collect.ComparisonChain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString(exclude = "containerType")
@Node(value = "Container")
public class Container extends Entity implements Serializable, Comparable<Container> {

    @Relationship(value = "CONTAINER_COMPANY")
    private Company company;

    @Relationship(value = "CONTAINER_CONTAINER_TYPE")
    private ContainerType containerType;

    private String name;
    private int labelCount;

    private String summaryLabel;
    private String colourDescription;
    private String specification;
    private String textColourCode;
    private String fillColourCode;
    private String borderColourCode;

    //    name + ( colourDescription ) + specification
    private String description;

    private int sequence;
    private ContainerImages image;
    private ContainerLayout containerLayout;
    private boolean active;
    private boolean commonContainer;

    @Override
    public int compareTo(Container o) {
        return ComparisonChain.start()
                .compare(sequence, o.getSequence())
                .result();
    }

}
