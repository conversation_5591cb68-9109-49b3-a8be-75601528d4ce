package au.com.healius.digital.trm.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

@Data
public class CompanyOrderableDTO implements Serializable {

    private String id;
    private ZonedDateTime createdDate;
    private ZonedDateTime modifiedDate;
    private CompanyDTO company;
    private OrderableDTO orderable;
    private Long price;
    private String name;
    private String code;
    private boolean tier;
    private boolean allowPriceOverride;
    private String trm;
}
