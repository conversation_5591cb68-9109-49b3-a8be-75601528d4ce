/*
 * SVC Tests API
 * Healius Test api
 *
 * OpenAPI spec version: 1.0.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package au.com.healius.digital.trm.dto.model;


import au.com.healius.digital.trm.domain.enumeration.OrderableVisibility;
import au.com.healius.digital.trm.domain.enumeration.RebatabilityOptions;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * OrderableResponse
 */

@Data
@Builder
public class OrderableResponse {

  @SerializedName("id")
  private String id;

  @SerializedName("name")
  private String name;

  @SerializedName("code")
  private String code;

  @SerializedName("rcpaName")
  private String rcpaName;

  @SerializedName("snomedName")
  private String snomedName;

  @SerializedName("snomedCode")
  private String snomedCode;

  @SerializedName("snomedAUCode")
  private String snomedAUCode;

  @SerializedName("department")
  private String department;

  @SerializedName("synonyms")
  private List<String> synonyms;

  @SerializedName("information")
  private String information;

  @SerializedName("resultsAvailable")
  private String resultsAvailable;

  @SerializedName("testFrequency")
  private String testFrequency;

  @SerializedName("prepaymentUrl")
  private String prepaymentUrl;

  @SerializedName("commonTest")
  private Boolean commonTest;

  @SerializedName("testAvailable")
  private Boolean testAvailable;

  @SerializedName("cytology")
  private Boolean cytology;

  @SerializedName("findUsSpecialTest")
  private Boolean findUsSpecialTest;

  @SerializedName("isReplaceByOtherTest")
  private Boolean isReplaceByOtherTest;

  @SerializedName("currentOrderable")
  private OrderableResponse currentOrderable;

  @SerializedName("currentOrderableId")
  private String currentOrderableId;

  @SerializedName("currentOrderableEffectiveDate")
  private OffsetDateTime currentOrderableEffectiveDate;

  @SerializedName("formerOrderable")
  private OrderableResponse formerOrderable;

  @SerializedName("formerOrderableId")
  private String formerOrderableId;

  @SerializedName("formerOrderableEffectiveDate")
  private OffsetDateTime formerOrderableEffectiveDate;

  @SerializedName("rebatability")
  private RebatabilityOptions rebatability;

  @SerializedName("visibilities")
  private List<OrderableVisibility> visibilities;

  @SerializedName("findUsBookingInfo")
  private String findUsBookingInfo;

  @SerializedName("modifiedBy")
  private String modifiedBy;

}
