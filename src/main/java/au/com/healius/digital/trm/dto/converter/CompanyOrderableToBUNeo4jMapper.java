package au.com.healius.digital.trm.dto.converter;

import au.com.healius.digital.trm.domain.CompanyOrderable;
import au.com.healius.digital.trm.domain.enumeration.CollectionFrequency;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
public class CompanyOrderableToBUNeo4jMapper implements Converter<CompanyOrderable, CompanyOrderable> {

    @Override
    public CompanyOrderable convert(CompanyOrderable source) {
        CompanyOrderable target = new CompanyOrderable();
        target.setId(source.getId());
        target.setEnabled(source.isEnabled());
        target.setName(source.getName());
        target.setCode(source.getCode());
        target.setSpecimenRequired(source.getSpecimenRequired());
        if (source.getCollectionFrequency() != null) {
            target.setCollectionFrequency(CollectionFrequency.valueOf(source.getCollectionFrequency().name()));
        }
        return target;
    }
}
