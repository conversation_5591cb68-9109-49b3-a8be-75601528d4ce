package au.com.healius.digital.trm.repository;

import au.com.healius.digital.trm.domain.CompanyInstantResultProperties;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * Repository interface for managing CompanyInstantResultProperties entities in the Neo4j database.
 * Provides methods for managing relationships between CompanyInstantResult and their properties.
 */
public interface CompanyInstantResultPropertiesRepository extends
    Neo4jRepository<CompanyInstantResultProperties, String> {

  /**
   * Removes a property from a CompanyInstantResult and deletes the property entity.
   *
   * @param companyInstantResultId The ID of the CompanyInstantResult
   * @param propertyId The ID of the property to remove
   */
  @Transactional
  @Query("""
    MATCH (companyInstantResult:CompanyInstantResult {id: $companyInstantResultId})-[rel:INSTANT_RESULT_PROPERTIES]->(property:CompanyInstantResultProperties {id: $propertyId})
    DELETE rel, property
    """)
  void removePropertyFromCompanyInstantResult(String companyInstantResultId, String propertyId);

  /**
   * Creates a relationship between a CompanyInstantResult and a property.
   *
   * @param companyInstantResultId The ID of the CompanyInstantResult
   * @param propertyId The ID of the property to link
   */
  @Transactional
  @Query("""
    MATCH (companyInstantResult:CompanyInstantResult {id: $companyInstantResultId})
    MATCH (property:CompanyInstantResultProperties {id: $propertyId})
    MERGE (companyInstantResult)-[:INSTANT_RESULT_PROPERTIES]->(property)
    """)
  void linkPropertyToCompanyInstantResult(String companyInstantResultId, String propertyId);
}
