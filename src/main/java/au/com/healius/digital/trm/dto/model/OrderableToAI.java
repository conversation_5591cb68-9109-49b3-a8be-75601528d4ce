package au.com.healius.digital.trm.dto.model;


import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * OrderableToAI
 */

@Data
@Builder
public class OrderableToAI {
    @SerializedName("id")
    private String id;

    @SerializedName("name")
    private String name;

    @SerializedName("snomedCTAU")
    private String snomedCTAU;

    @SerializedName("snomedLocal")
    private String snomedLocal;

    @SerializedName("synonyms")
    private List<String> synonyms;

    @SerializedName("buCodes")
    private List<BUCodes> buCodes;

}


