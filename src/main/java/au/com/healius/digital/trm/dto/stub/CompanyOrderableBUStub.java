package au.com.healius.digital.trm.dto.stub;


import au.com.healius.digital.trm.domain.enumeration.CollectionFrequency;
import au.com.healius.digital.trm.domain.enumeration.ContainerRule;

public interface CompanyOrderableBUStub {

    String getId();

    String getName();

    String getCode();

    Integer getSpecimenRequired();

    CollectionFrequency getCollectionFrequency();

    Long getPrice();

    Long getMinPriceOverride();

    boolean isAllowPriceOverride();

    boolean isExemptionTest();

    boolean isPatientSelfClaim();

    boolean isEnabled();

    ContainerRule getContainerRule();

    String getBuSortDestination();

    String getClinicalNotesSpecificText();

    Boolean getGeneralClinicalNotes();

    Boolean getSendAwayTest();

    String getSendAwayTestDestination();

    Boolean getRecordSiteOfCollection();

    Boolean getLastDose();

    Boolean getMustBeFasting();

    String getCollectionSOP();

    String getCollectionSOPLink();

    String getQuestionnaire();

    String getQuestionnaireLink();

    String getPatientPreparation1();

    String getPatientPreparation1Link();

    String getPatientPreparation2();

    String getPatientPreparation2Link();

    String getHandlingAndSpecialInstructions();


}
