package au.com.healius.digital.trm.repository;

import au.com.healius.digital.trm.domain.ContainerType;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for managing ContainerType entities in the Neo4j database.
 * Provides methods for querying container types and their relationships with containers and companies.
 */
@Repository
public interface ContainerTypeRepository extends Neo4jRepository<ContainerType, String> {

    /**
     * Finds a container type by its name.
     *
     * @param name The name of the container type to find
     * @return An Optional containing the container type if found, empty otherwise
     */
    Optional<ContainerType> findByName(String name);

    /**
     * Retrieves all container types based on their active status.
     *
     * @param active Flag indicating whether to retrieve active container types
     * @return List of container types matching the active status
     */
    @Transactional(readOnly = true)
    List<ContainerType> findByActive(boolean active);

    /**
     * Checks if a container type is currently in use by any active containers.
     *
     * @param containerTypeId The ID of the container type to check
     * @return true if the container type is in use by any active containers, false otherwise
     */
    @Query("""
            MATCH (containerType:ContainerType{id: $containerTypeId})
            RETURN EXISTS((containerType)<-[]-(:Container{active: true}))
            """)
    boolean isInUse(String containerTypeId);

    /**
     * Retrieves all active container types associated with common containers for a specific company.
     * This method is typically used for collection purposes.
     *
     * @param company The ID of the company
     * @return List of container types associated with common containers for the specified company
     */
    @Transactional(readOnly = true)
    @Query(value =
            """
            MATCH (company:Company {id: $company})<-[REL_CONTAINER_COMPANY:CONTAINER_COMPANY]-(container:Container {active: true})-[REL_CONTAINER_CONTAINER_TYPE:CONTAINER_CONTAINER_TYPE]->(containerType:ContainerType{active: true})
            RETURN DISTINCT containerType, collect(container), collect(REL_CONTAINER_CONTAINER_TYPE)
            """
    )
    List<ContainerType> findAllContainerTypeForCollection(@Param("company") String company);

    List<ContainerType> findAllByActive(boolean active);

}
