package au.com.healius.digital.trm.repository;

import au.com.healius.digital.trm.domain.SpecimenType;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Repository interface for managing SpecimenType entities in the Neo4j database.
 * Provides basic CRUD operations for specimen types through the Neo4jRepository interface.
 */
@Repository
public interface SpecimenTypeRepository extends Neo4jRepository<SpecimenType, String> {


    @Transactional(readOnly = true)
    Optional<SpecimenType> findByName(String s);
}
