package au.com.healius.digital.admin.web.api;


import au.com.healius.digital.admin.service.CompanyOrderableService;
import au.com.healius.digital.trm.domain.CompanyOrderable;
import au.com.healius.digital.trm.dto.model.OrderableBU;
import au.com.healius.digital.trm.dto.model.OrderablePaymentResponse;
import au.com.healius.digital.trm.dto.model.OrderableTRMResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/v1/companyOrderable/{id}")
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('APPROLE_DigitalAdmin','APPROLE_ADMIN_READ_ONLY')")
public class CompanyOrderableResource {

    private final CompanyOrderableService companyOrderableService;

    @PostMapping("/payment")
    public ResponseEntity<CompanyOrderable> updatePayment(@RequestBody OrderablePaymentResponse data) {
        return ResponseEntity.ok(companyOrderableService.updatePayment(data));
    }

    @PostMapping("/trm")
    public ResponseEntity<CompanyOrderable> updateTrm(@RequestBody OrderableTRMResponse data) {
        return ResponseEntity.ok(companyOrderableService.updateTRM(data));
    }

    @PutMapping("/bu")
    public ResponseEntity<CompanyOrderable> updateBu(@RequestBody OrderableBU data) {
        return ResponseEntity.ok(companyOrderableService.updateBU(data));
    }
}
