package au.com.healius.digital.admin.service;

import au.com.healius.digital.trm.domain.CompanyOrderable;
import au.com.healius.digital.trm.domain.Orderable;
import au.com.healius.digital.trm.domain.enumeration.RebatabilityOptions;
import au.com.healius.digital.trm.dto.converter.CompanyOrderableToOrderableCompanyMapper;
import au.com.healius.digital.trm.dto.model.OrderableDepth1;
import au.com.healius.digital.trm.dto.model.OrderableResponse;
import au.com.healius.digital.trm.dto.stub.OrderableStub;
import au.com.healius.digital.trm.repository.CompanyOrderableRepository;
import au.com.healius.digital.trm.repository.OrderableCompanyRepository;
import au.com.healius.digital.trm.repository.OrderableRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@RequiredArgsConstructor
public class OrderableService {

    private final OrderableRepository orderableRepository;
    private final CompanyOrderableService companyOrderableService;
    private final Neo4jTemplate neo4jTemplate;
    private final OrderableCompanyRepository orderableCompanyRepository;
    private final CompanyOrderableRepository companyOrderableRepository;
    private final CompanyOrderableToOrderableCompanyMapper mapper;

    public Orderable createOrderable(OrderableResponse orderableResponse) {
        Orderable orderable = new Orderable();
        BeanUtils.copyProperties(orderableResponse,
            orderable,
            "replacedByOtherTest",
            "modifiedDate", "createdDate");

        orderable.setRebatabilityState(orderableResponse.getRebatability());
        if( orderableResponse.getSynonyms() != null) {
            orderable.setSynonymList(orderableResponse.getSynonyms());
            orderable.setSynonyms(String.join("|", orderableResponse.getSynonyms()));
        } else {
            orderable.setSynonyms("");
            orderable.setSynonymList(Collections.emptyList());
        }


        Orderable created = orderableRepository.save(orderable);
        companyOrderableService.createAllBUCompanyOrderable(created.getId(),
            orderableResponse.getModifiedBy());
        return created;
    }

    public Orderable updateOrderable(OrderableResponse orderableResponse) {

        OrderableDepth1 depth = orderableRepository.findObjectById(orderableResponse.getId())
            .orElseThrow();

        Orderable orderable = depth.getOrderable();

        BeanUtils.copyProperties(orderableResponse,
            orderable,
            "id",
            "currentOrderable",
            "formerOrderable",
            "modifiedDate",
            "synonyms",
            "synonymList",
            "createdDate",
            "formerOrderableEffectiveDate",
            "currentOrderableEffectiveDate");

        orderable.setSynonyms(String.join("|", orderableResponse.getSynonyms()));
        orderable.setSynonymList(orderableResponse.getSynonyms());
        orderable.setReplaceByOtherTest(orderableResponse.getIsReplaceByOtherTest());
        orderable.setRebatabilityState(orderableResponse.getRebatability());

        List<CompanyOrderable> updateCompanyOrderables = companyOrderableRepository.getByOrderableWithChildren(orderable.getId());
        updateCompanyOrderables.forEach(i -> companyOrderableService.updateOrderableCompany(i, orderable));

        boolean isCurrentlyReplaced = orderableResponse.getIsReplaceByOtherTest();

        if(!isCurrentlyReplaced) {
            if(depth.getCurrentOrderableId() != null) {
                orderableRepository.removeCurrentOrderableRelationship(depth.getOrderable().getId(), depth.getCurrentOrderableId());
                orderable.setCurrentOrderableEffectiveDate(null);
                Orderable current = orderableRepository.findById(depth.getCurrentOrderableId()).orElseThrow();
                current.setFormerOrderableEffectiveDate(null);
                neo4jTemplate.saveAs(current, OrderableStub.class);
            }
        }

        if(orderableResponse.getCurrentOrderableId() != null) {
            assert (isCurrentlyReplaced);
            orderableRepository.removeCurrentOrderableRelationship(orderable.getId(), depth.getCurrentOrderableId());
            Orderable current = orderableRepository.findById(orderableResponse.getCurrentOrderableId()).orElseThrow();
            orderableRepository.linkCurrentOrderable(orderable.getId(), current.getId());
            orderable.setCurrentOrderableEffectiveDate(orderableResponse.getCurrentOrderableEffectiveDate());
            current.setFormerOrderableEffectiveDate(orderableResponse.getCurrentOrderableEffectiveDate());
            neo4jTemplate.saveAs(current, OrderableStub.class);
        }

        if (orderableResponse.getRebatability() == RebatabilityOptions.NON_REBATABLE || orderableResponse.getRebatability() == RebatabilityOptions.CRITERIA_BASED) {
            companyOrderableService.resetPayment(orderable.getId());
        }

        neo4jTemplate.saveAs(orderable,
            OrderableStub.class);
        return orderable;

    }

}
