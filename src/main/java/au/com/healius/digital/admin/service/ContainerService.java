package au.com.healius.digital.admin.service;

import au.com.healius.digital.admin.session.AdminSession;
import au.com.healius.digital.trm.domain.Container;
import au.com.healius.digital.trm.dto.model.ContainerDTO;
import au.com.healius.digital.trm.dto.stub.ContainerStub;
import au.com.healius.digital.trm.repository.ContainerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class ContainerService {

    private final ContainerRepository repository;
    private final Neo4jTemplate neo4jTemplate;
    private final AdminSession adminSession;

    public ContainerStub create(ContainerDTO container) {
        Container newContainer = new Container();
        BeanUtils.copyProperties(container,
            newContainer,
            "id",
            "company",
            "containerType",
            "createdDate",
            "modifiedDate",
            "companyId",
            "containerTypeId");
        newContainer.setCreatedDate(ZonedDateTime.now());
        newContainer.setCreatedBy(adminSession.getUserId());
        ContainerStub update = neo4jTemplate.saveAs(newContainer,
            ContainerStub.class);
        repository.setContainerCompany(update.getId(),
            container.getCompanyId());
        repository.setContainerContainerType(update.getId(),
            container.getContainerTypeId());
        return update;
    }

    public ContainerStub update(String id, ContainerDTO update) {
        Container container = repository.findById(id)
            .orElseThrow();

        BeanUtils.copyProperties(update,
            container,
            "id",
            "company",
            "containerType",
            "companyId",
            "containerTypeId",
            "createdBy",
            "createdDate");

        container.setModifiedBy(adminSession.getUserId());
        container.setModifiedDate(ZonedDateTime.now());

        ContainerStub updatedContainer = neo4jTemplate.saveAs(container,
            ContainerStub.class);
        if(container.getCompany() == null) {
            repository.setContainerCompany(updatedContainer.getId(),
                update.getCompanyId());
        } else {
            if (!Objects.equals(container.getCompany()
                    .getId(),
                update.getCompanyId())) {
                repository.setContainerCompany(updatedContainer.getId(),
                    update.getCompanyId());
            }
        }

        if(container.getContainerType() == null) {
            repository.setContainerContainerType(updatedContainer.getId(),
                update.getContainerTypeId());
            return updatedContainer;
        }
            if (!Objects.equals(container.getContainerType()
                    .getId(),
                update.getContainerTypeId())) {
                repository.setContainerContainerType(updatedContainer.getId(),
                    update.getContainerTypeId());
            }


        return updatedContainer;
    }

}
