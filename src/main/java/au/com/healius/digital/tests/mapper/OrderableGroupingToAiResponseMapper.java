package au.com.healius.digital.tests.mapper;

import au.com.healius.digital.trm.domain.OrderableGrouping;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class OrderableGroupingToAiResponseMapper implements
    Converter<OrderableGrouping, au.com.healius.digital.tests.generated.model.OrderableGroupingToAI> {
    @Override
    public au.com.healius.digital.tests.generated.model.OrderableGroupingToAI convert(
        OrderableGrouping source) {
        au.com.healius.digital.tests.generated.model.OrderableGroupingToAI target =
            new au.com.healius.digital.tests.generated.model.OrderableGroupingToAI();
        target.setId(source.getId());
        target.setName(source.getName());
        target.setBu(source.getCompany().getCode());
        target.setSnomedLocal(source.getCode());
        target.setSynonyms(String.join("|", source.getSynonymList()));
        target.setSynonymList(source.getSynonymList());
        return target;
    }
}
