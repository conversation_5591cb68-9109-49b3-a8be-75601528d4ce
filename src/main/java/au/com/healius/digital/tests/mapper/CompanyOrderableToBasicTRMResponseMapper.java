package au.com.healius.digital.tests.mapper;

import au.com.healius.digital.tests.generated.model.CompanyOrderableBasicTRMResponse;
import au.com.healius.digital.tests.generated.model.HandlingIconsResponse;
import au.com.healius.digital.trm.domain.CompanyOrderable;
import au.com.healius.digital.trm.domain.ContainerCompany;
import au.com.healius.digital.trm.dto.enums.HandlingIcons;
import au.com.healius.digital.trm.dto.enums.StorageIcons;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.BooleanSupplier;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CompanyOrderableToBasicTRMResponseMapper implements Converter<CompanyOrderable, CompanyOrderableBasicTRMResponse> {

    @Override
    public CompanyOrderableBasicTRMResponse convert(CompanyOrderable source) {
        CompanyOrderableBasicTRMResponse target = new CompanyOrderableBasicTRMResponse();
        if(source.getContainers().isEmpty()) {
            target.setColour(null);
            target.setIcons(Collections.emptyList());
        } else {
            ContainerCompany containerCompany = source.getContainers().getFirst();
            if(containerCompany.getContainer() != null) {
            target.setColour(containerCompany.getContainer().getFillColourCode());
            }
            target.setIcons(icons(containerCompany));
        }
        boolean hasDetail = !source.getContainers().isEmpty();

        target.setHasDetailedTRM(hasDetail);
        return target;
    }


    private List<HandlingIconsResponse> icons(ContainerCompany source) {
        List<HandlingIconsResponse> icons = new ArrayList<>();

        Map<BooleanSupplier, HandlingIcons> iconConditions = Map.ofEntries(
            Map.entry(() -> Boolean.TRUE.equals(source.getIceBrick()), HandlingIcons.ICEBRICK),
            Map.entry(() -> Boolean.TRUE.equals(source.getExposure()), HandlingIcons.EXPOSURE),
            Map.entry(() -> Boolean.TRUE.equals(source.getFreeze()), HandlingIcons.FREEZE),
            Map.entry(() -> Boolean.TRUE.equals(source.getDoNotFreeze()), HandlingIcons.NOFREEZE),
            Map.entry(() -> Boolean.TRUE.equals(source.getAttention()), HandlingIcons.ATTENTION),
            Map.entry(() -> Boolean.TRUE.equals(source.getDose()), HandlingIcons.DOSE),
            Map.entry(() -> Boolean.TRUE.equals(source.getDryIce()), HandlingIcons.DRYICE),
            Map.entry(() -> Boolean.TRUE.equals(source.getCover()), HandlingIcons.COVER),
            Map.entry(() -> Boolean.TRUE.equals(source.getHAndW()), HandlingIcons.HEIGHTANDWEIGHT),
            Map.entry(() -> Boolean.TRUE.equals(source.getQuestionnaire()), HandlingIcons.QUESTIONAIRE),
            Map.entry(() -> Boolean.TRUE.equals(source.getGlucLoad()), HandlingIcons.GLUCOSELOAD),
            Map.entry(() -> Boolean.TRUE.equals(source.getUrgent()), HandlingIcons.URGENT),
            Map.entry(() -> Boolean.TRUE.equals(source.getPriority()), HandlingIcons.PRIORITY),
            Map.entry(() -> Boolean.TRUE.equals(source.getDedicatedTube()), HandlingIcons.DEDICATEDTUBE),
            Map.entry(() -> Boolean.TRUE.equals(source.getCentrifuge()), HandlingIcons.CENTRIFUGE),
            Map.entry(() -> Boolean.TRUE.equals(source.getDoNotCentrifuge()), HandlingIcons.NOCENTRIFUGE)
        );

        iconConditions.forEach((condition, icon) -> {
            if (condition.getAsBoolean()) {
                icons.add(iconBuilder(icon));
            }
        });

        return icons;
    }

    private HandlingIconsResponse iconBuilder(HandlingIcons icon) {
        HandlingIconsResponse result = new HandlingIconsResponse();
        result.setIcon(icon.getIcon());
        result.setTooltip(icon.getTooltip());
        return result;
    }
}
