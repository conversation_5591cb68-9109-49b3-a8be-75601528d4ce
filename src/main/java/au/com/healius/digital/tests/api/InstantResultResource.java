package au.com.healius.digital.tests.api;

import au.com.healius.digital.tests.generated.api.InstantResultsApi;
import au.com.healius.digital.tests.generated.model.InstantResultsResponse;
import au.com.healius.digital.tests.generated.model.ModelAPIResponse;
import au.com.healius.digital.tests.mapper.InstantResultToResponseMapper;
import au.com.healius.digital.tests.service.InstantResultService;
import au.com.healius.digital.trm.dto.model.RestError;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequiredArgsConstructor
@RequestMapping("/v1/instantResults")
public class InstantResultResource implements InstantResultsApi {

    private final InstantResultService instantResultsService;
    private final InstantResultToResponseMapper mapper;


    @Operation(summary = "List all instant resultss", description = "Return all object of instant results", security = {
        @SecurityRequirement(name = "bearerAuth")}, tags = {"instantResults"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "instantResults successfully returned", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = ModelAPIResponse.class)))),

        @ApiResponse(responseCode = "500", description = "Server error.", content = @Content(mediaType = "application/json", schema = @Schema(implementation = RestError.class)))})
    @GetMapping(
        produces = {"application/json"})
    public ResponseEntity<List<InstantResultsResponse>> listInstantResults() {
        return ResponseEntity.ok(instantResultsService.listInstantResults()
            .stream()
            .map(mapper::convert)
            .toList());
    }

    @Operation(summary = "Get instant results by id", description = "Return a instantResult", security = {
        @SecurityRequirement(name = "bearerAuth")}, tags = {"instantResults"})
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "instant results successfully returned", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ModelAPIResponse.class))),

        @ApiResponse(responseCode = "500", description = "Server error.", content = @Content(mediaType = "application/json", schema = @Schema(implementation = RestError.class)))})
    @GetMapping(value = "/{id}",
        produces = {"application/json"})
    public ResponseEntity<InstantResultsResponse> getInstantResult(@PathVariable String id) {
        return ResponseEntity.ok(mapper.convert(instantResultsService.getById(id)));
    }

}
