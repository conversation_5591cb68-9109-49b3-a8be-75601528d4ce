package au.com.healius.digital.tests.service;

import au.com.healius.digital.trm.domain.ContainerType;
import au.com.healius.digital.trm.repository.ContainerTypeRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class ContainerTypeService {

    private final ContainerTypeRepository containerTypeRepository;

    public List<ContainerType> list() {
        return containerTypeRepository.findAll();
    }
    public List<ContainerType> listActive() {
        return containerTypeRepository.findByActive(true);
    }

    public ContainerType get(String id) {
        return containerTypeRepository.findById(id).orElseThrow();
    }

    public boolean isInUse(String containerTypeId) {
        return containerTypeRepository.isInUse(containerTypeId);
    }
}
