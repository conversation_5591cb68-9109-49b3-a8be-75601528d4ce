package au.com.healius.digital.tests.service;

import au.com.healius.digital.trm.domain.CompanyOrderable;
import au.com.healius.digital.trm.repository.CompanyOrderableRepository;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyOrderableService {
    private final CompanyOrderableRepository companyOrderableRepository;


    public List<CompanyOrderable> findByOrderableWithChildren(String orderableId) {
        return companyOrderableRepository.getByOrderableWithChildren(orderableId);
    }

    public Optional<CompanyOrderable> findById(String id) {
        return companyOrderableRepository.findById(id);
    }
}
