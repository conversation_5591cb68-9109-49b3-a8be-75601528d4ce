package au.com.healius.digital.tests.config;

import org.neo4j.driver.AuthTokens;
import org.neo4j.driver.Driver;
import org.neo4j.driver.GraphDatabase;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.neo4j.Neo4jProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.neo4j.core.DatabaseSelectionProvider;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.data.neo4j.core.mapping.Neo4jMappingContext;
import org.springframework.data.neo4j.core.transaction.Neo4jTransactionManager;
import org.springframework.data.neo4j.repository.config.EnableNeo4jRepositories;

@Configuration
@EnableNeo4jRepositories(basePackages = {
        "au.com.healius.digital.trm.repository"},
        transactionManagerRef = Neo4jDataDbConfig.TRANSACTION_MANAGER)
public class Neo4jDataDbConfig {

    public static final String TRANSACTION_MANAGER = "neo4jDomainTxManager";
    @Value("${healius.neo4j.datadb.database}")
    private String database;

    @Bean
    @ConfigurationProperties("healius.neo4j.datadb.properties")
    public Neo4jProperties getNeo4jProperties() {
        return new Neo4jProperties();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public Neo4jTransactionManager neo4jTransactionManager() {
        return Neo4jTransactionManager.
                with(neo4jDomainDBDriver()).
                withDatabaseSelectionProvider(databaseDomainSelectionProvider()).
                build();
    }

    @Bean
    public Neo4jMappingContext neo4jMappingContext() {
        return new Neo4jMappingContext();
    }

    @Bean
    public Neo4jTemplate neo4jTemplate() {
        return new Neo4jTemplate(neo4jClient());
    }

    @Bean
    public Neo4jClient neo4jClient() {
        return Neo4jClient.with(neo4jDomainDBDriver()).
                withDatabaseSelectionProvider(databaseDomainSelectionProvider()).
                build();
    }

    @Bean
    public Driver neo4jDomainDBDriver() {
        return GraphDatabase.driver(getNeo4jProperties().getUri(),
                AuthTokens.basic(
                        getNeo4jProperties().getAuthentication().getUsername(),
                        getNeo4jProperties().getAuthentication().getPassword()));
    }

    @Bean
    public DatabaseSelectionProvider databaseDomainSelectionProvider() {
        return DatabaseSelectionProvider.createStaticDatabaseSelectionProvider(database);
    }
}
