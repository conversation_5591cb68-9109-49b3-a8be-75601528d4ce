package au.com.healius.digital.tests.mapper;

import au.com.healius.digital.tests.generated.model.CollectionFrequency;
import au.com.healius.digital.tests.generated.model.CompanyOrderableResponse;
import au.com.healius.digital.tests.generated.model.ContainerRule;
import au.com.healius.digital.trm.domain.CompanyOrderable;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CompanyOrderableToResponseMapper implements Converter<CompanyOrderable, CompanyOrderableResponse> {

    private final ContainerCompanyToResponseMapper mapper;
    @Override
    public CompanyOrderableResponse convert(CompanyOrderable source) {
        CompanyOrderableResponse target = new CompanyOrderableResponse();
        target.setCompany(new CompanyToResponseMapper().convert(source.getCompany()));
        target.setId(source.getId());
        target.setTrm(source.getTrm());
        target.setMbs(source.getMbs());
        target.setCode(source.getCode());
        target.setName(source.getName());
        target.setTier(source.isTier());
        target.setAllowPriceOverride(source.isAllowPriceOverride());
        target.setMinPriceOverride(BigDecimal.valueOf(Optional.ofNullable(source.getMinPriceOverride()).orElse(0L)));
        target.setPatientSelfClaim(Optional.of(source.isPatientSelfClaim()).orElse(false));
        target.setExemptionTest(Optional.of(source.isExemptionTest()).orElse(false));
        target.setPrice(BigDecimal.valueOf(Optional.ofNullable(source.getPrice()).orElse(0L)));
        target.setSpecimenRequired(Optional.of(source.getSpecimenRequired()).orElse(1));
        if( source.getCollectionFrequency() != null) {
            target.setCollectionFrequency(CollectionFrequency.valueOf(source.getCollectionFrequency().name()));
        }
        target.setModifiedBy(source.getModifiedBy());
        target.setEnabled(source.isEnabled());
        target.setContainers(source.getContainers().stream().sorted().map(mapper::convert).toList());
        target.setBuSortDestination(source.getBuSortDestination());
        target.setClinicalNotesSpecificText(source.getClinicalNotesSpecificText());
        target.setGeneralClinicalNotes(source.getGeneralClinicalNotes());
        target.setSendAwayTest(source.getSendAwayTest());
        target.setSendAwayTestDestination(source.getSendAwayTestDestination());
        target.setRecordSiteOfCollection(source.getRecordSiteOfCollection());
        target.setMustBeFasting(source.getMustBeFasting());
        target.setCollectionSOP(source.getCollectionSOP());
        target.setCollectionSOPLink(source.getCollectionSOPLink());
        target.setQuestionnaire(source.getQuestionnaire());
        target.setQuestionnaireLink(source.getQuestionnaireLink());
        target.setPatientPreparation1(source.getPatientPreparation1());
        target.setPatientPreparation1Link(source.getPatientPreparation1Link());
        target.setPatientPreparation2(source.getPatientPreparation2());
        target.setPatientPreparation2Link(source.getPatientPreparation2Link());
        target.setHandlingAndSpecialInstructions(source.getHandlingAndSpecialInstructions());
        if (source.getContainerRule() != null) {
            target.setContainerRule(ContainerRule.valueOf(source.getContainerRule().name()));
        }
        if (source.getCreatedDate() != null) {
            target.setCreatedDate(source.getCreatedDate().toOffsetDateTime());
        }
        target.setCreatedBy(source.getCreatedBy());
        if (source.getModifiedDate() != null) {
            target.setModifiedDate(source.getModifiedDate().toOffsetDateTime());
        }
        return target;
    }
}
