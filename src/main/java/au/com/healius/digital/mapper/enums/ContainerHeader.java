package au.com.healius.digital.mapper.enums;

import lombok.Getter;

@Getter
public enum ContainerHeader {

    Active(0),
    PathologyBU(1),
    ContainerType(2),
    Name(3),
    ColourDescription(4),
    Specification(5),
    SummaryLabel(6),
    FillColourCode(7),
    TextColourCode(8),
    BorderColourCode(9),
    Description(10),
    CollectedSpecimenSequence(11),
    CountOfLabels(12),
    ImagePosition(13),
    CollectedSpecimenCategory(14),
    CommonACCContainers(15);

    private final int position;

    ContainerHeader(int position) {
        this.position = position;
    }
}