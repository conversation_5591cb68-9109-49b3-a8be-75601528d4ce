package au.com.healius.digital;

import au.com.healius.digital.common.annotations.ConditionalOnElastic;
import co.elastic.apm.attach.ElasticApmAttacher;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.neo4j.Neo4jDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.neo4j.Neo4jReactiveDataAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.neo4j.Neo4jAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.HashMap;
import java.util.Map;

@Log4j2
@SpringBootApplication(exclude = {Neo4jDataAutoConfiguration.class, Neo4jReactiveDataAutoConfiguration.class, Neo4jAutoConfiguration.class, DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableScheduling
@OpenAPIDefinition
public class Application {


    @Value("${elastic.service_name}")
    private String appName;
    @Value("${elastic.secret_token}")
    private String elasticToken;
    @Value("${elastic.server_url}")
    private String elasticUrl;
    @Value("${elastic.application_packages}")
    private String elasticPackage;

    /**
     * Main method, used to run the application.
     *
     * @param args the command line arguments.
     */
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Bean
    public String getVersionHolder() {
        return Application.class.getPackage().getImplementationVersion() != null ? Application.class.getPackage().getImplementationVersion().substring(0, 8) : "Unknown";
    }

    @Bean
    @ConditionalOnElastic
    ApplicationRunner applicationRunner() {
        Map<String, String> elastic = new HashMap<>();
        elastic.put("application_packages", elasticPackage);
        elastic.put("server_url", elasticUrl);
        elastic.put("secret_token", elasticToken);
        elastic.put("service_name", appName);

        ElasticApmAttacher.attach(elastic);
        return args -> {
        };
    }

}
