package au.com.healius.digital.collectorportal.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * Data Transfer Object representing an AI-processed orderable item view.
 * This class contains information about an orderable item that has been processed
 * by AI, including its mapping status and associated free text.
 */
@Builder
@Getter
@ToString
public class OrderableAIView {

    /** Unique identifier for the orderable item */
    private String id;
    
    /** Healius identifier for the orderable item */
    private String healiusId;

    /** Flag indicating if the orderable item has been mapped */
    private boolean mapped;
    
    /** Free text associated with the orderable item */
    private String freeText;

    /** List of mapped views for the orderable item */
    private List<OrderableAIMappedView> mappedList;
}
