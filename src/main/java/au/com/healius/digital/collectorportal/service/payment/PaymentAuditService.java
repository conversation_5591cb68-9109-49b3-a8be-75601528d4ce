package au.com.healius.digital.collectorportal.service.payment;

import au.com.healius.digital.collectorportal.domain.payment.PaymentEvent;
import au.com.healius.digital.collectorportal.domain.payment.PaymentEventRepository;
import au.com.healius.digital.librarycollections.domain.enumeration.PaymentMethod;
import com.squareup.square.models.Payment;
import com.squareup.square.models.TerminalCheckout;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

import static au.com.healius.digital.librarycollections.domain.enumeration.PaymentMethod.SMS;
import static au.com.healius.digital.librarycollections.domain.enumeration.PaymentMethod.TERMINAL;

/**
 * Service responsible for auditing payment-related events.
 * This service records various payment events such as terminal checkouts,
 * payments, and payment links for tracking and auditing purposes.
 *
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class PaymentAuditService {
    private final PaymentEventRepository paymentEventRepository;

    /**
     * Event listener for payment events.
     * Saves the payment event to the repository and links it to the associated order.
     *
     * @param event The payment event to be processed
     */
    @EventListener
    public void onPaymentEvent(PaymentEvent event) {
        log.info(event);
        try {
            event = paymentEventRepository.save(event);
            paymentEventRepository.linkEvent(event.getOrderId(), event.getId());
        } catch (Exception ex) {
            log.error("Error while saving payment event", ex);
        }
    }

    /**
     * Audits a terminal checkout event.
     * Creates and saves a payment event with terminal checkout details.
     *
     * @param orderId The ID of the order associated with the checkout
     * @param event The type of event being audited
     * @param checkout The terminal checkout details
     */
    public void auditTerminalCheckout(String orderId, String event, TerminalCheckout checkout) {
        try {
            onPaymentEvent(PaymentEvent.builder()
                    .paymentMethod(TERMINAL)
                    .orderId(orderId)
                    .square(Map.of(
                            "type", event,
                            "id", checkout.getId(),
                            "referenceId", checkout.getReferenceId(),
                            "status", checkout.getStatus(),
                            "locationId", checkout.getLocationId()))
                    .build());
        } catch (Exception ex) {
            log.error("Error while auditing terminal checkout", ex);
        }
    }

    /**
     * Audits a payment event.
     * Creates and saves a payment event with payment details.
     *
     * @param orderId The ID of the order associated with the payment
     * @param method The payment method used
     * @param event The type of event being audited
     * @param payment The payment details
     */
    public void auditPayment(String orderId, PaymentMethod method, String event, Payment payment) {
        try {
            onPaymentEvent(PaymentEvent.builder()
                    .paymentMethod(method)
                    .orderId(orderId)
                    .square(Map.of(
                            "type", event,
                            "id", payment.getId(),
                            "orderId", payment.getOrderId(),
                            "locationId", payment.getLocationId(),
                            "receiptNumber", payment.getReceiptNumber(),
                            "status", payment.getStatus()))
                    .build());
        } catch (Exception ex) {
            log.error("Error while auditing payment", ex);
        }
    }

    /**
     * Audits a payment link event.
     * Creates and saves a payment event with payment link details.
     *
     * @param orderId The ID of the order associated with the payment link
     * @param event The type of event being audited
     * @param linkId The ID of the payment link
     * @param squareOrderId The Square order ID associated with the payment link
     */
    public void auditLink(String orderId, String event, String linkId, String squareOrderId) {
        try {
            onPaymentEvent(PaymentEvent.builder()
                    .paymentMethod(SMS)
                    .orderId(orderId)
                    .square(Map.of(
                            "type", event,
                            "linkId", linkId,
                            "squareOrderId", Optional.ofNullable(squareOrderId).orElse("")))
                    .build());
        } catch (Exception ex) {
            log.error("Error while auditing link", ex);
        }
    }
}
