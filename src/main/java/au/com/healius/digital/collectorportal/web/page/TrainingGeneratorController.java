package au.com.healius.digital.collectorportal.web.page;

import au.com.healius.digital.collectorportal.dto.MinimalReferralDTO;
import au.com.healius.digital.collectorportal.mocking.repository.Neo4jBookingRepository;
import au.com.healius.digital.collectorportal.mocking.service.MockBookingService;
import au.com.healius.digital.collectorportal.mocking.service.MockReferralService;
import au.com.healius.digital.collectorportal.mocking.service.MockTrainingBookingService;
import au.com.healius.digital.collectorportal.mocking.service.MockTrainingReferralService;
import au.com.healius.digital.collectorportal.session.CollectorSession;
import au.com.healius.digital.pulsar.dto.CommonReferral;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.apache.pulsar.client.api.Schema;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Random;

/**
 * Controller class responsible for handling referral generation and submission in the Collector Portal.
 * This controller provides endpoints for generating both single and batch referrals, and manages
 * the communication with Pulsar message queue for referral processing.
 *
 * <AUTHOR> Digital
 * @version 1.0
 */
@Log4j2
@Controller
@RequestMapping("/referralGeneratorTraining")
@RequiredArgsConstructor
public class TrainingGeneratorController {

    private final MockTrainingReferralService mockReferralService;
    private final MockTrainingBookingService mockBookingService;
    private final PulsarClient pulsarClient;
    private final CollectorSession collectorSession;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.findAndRegisterModules();
    }

    @Value("${healius.pulsar.producer.svc-referral-hub1}")
    private String referralTopicHub1;
    @Value("${healius.pulsar.producer.svc-referral-hub2}")
    private String referralTopicHub2;

    /**
     * Displays the referral form page to the user.
     * Initializes a new empty referral form model for user input.
     *
     * @param model The Spring MVC model to add attributes to
     * @return The view name for the referral generator form
     */
    @GetMapping("/form")
    public String showReferralForm(Model model) {
        model.addAttribute("referralForm", new MinimalReferralDTO());
        return "referralGenerator/generatorTraining";
    }

    /**
     * Processes a single referral submission from the form.
     * Generates a referral and its corresponding booking, then sends the referral to the Pulsar queue.
     *
     * @param referralForm The form data containing patient information
     * @param bindingResult The result of the form validation
     * @param model The Spring MVC model to add attributes to
     * @return Redirect to the dashboard page
     */
    @PostMapping("/submit")
    public String submitReferral(@Valid @ModelAttribute("referralForm") MinimalReferralDTO referralForm, BindingResult bindingResult, Model model) {
        if (bindingResult.hasErrors()) {
            return "referralGenerator/generatorTraining";
        }

        try {
            // Generate a CommonReferral using the mock service
            CommonReferral referral1 = mockReferralService.createMockReferral(1);
            referral1.setMobilePhone(referralForm.getMobilePhone());
            referral1.setNataCode(collectorSession.getCompany().getNataCode());
            CommonReferral referral2 = mockReferralService.createMockReferral(2);
            referral2.setMobilePhone(referralForm.getMobilePhone());
            referral2.setNataCode(collectorSession.getCompany().getNataCode());
            CommonReferral referral3 = mockReferralService.createMockReferral(3);
            referral3.setMobilePhone(referralForm.getMobilePhone());
            referral3.setNataCode(collectorSession.getCompany().getNataCode());

            // Create corresponding booking
            mockBookingService.createMockGTTBooking(referral1);
            
            // Send referral to Pulsar queue
            sendToPulsarQueue(referral1);
            sendToPulsarQueue(referral2);
            sendToPulsarQueue(referral3);

        } catch (PulsarClientException e) {
            log.error("Pulsar error processing referral from form", e);
            model.addAttribute("errorMessage", "Error communicating with message service. Please try again later.");
        } catch (Exception e) {
            log.error("Error processing referral from form", e);
            model.addAttribute("errorMessage", "An error occurred. Please try again later.");
        }

        return "redirect:/dashboard";
    }

    /**
     * Sends a referral to the Pulsar message queue for processing.
     * Converts the referral to JSON format and publishes it to the configured Pulsar topic.
     *
     * @param commonReferral The CommonReferral object to be sent to the queue
     * @throws Exception If an error occurs during the Pulsar communication
     */
    private void sendToPulsarQueue(CommonReferral commonReferral) throws Exception {
        sendCommonReferralToPulsarQueue(referralTopicHub1, commonReferral);
        sendCommonReferralToPulsarQueue(referralTopicHub2, commonReferral);
    }

    private void sendCommonReferralToPulsarQueue(
            String topic,
            CommonReferral commonReferral
    ) throws Exception {
        try (Producer<byte[]> producer = pulsarClient
                .newProducer(Schema.BYTES)
                .topic(topic)
                .create()
        ) {
            // Convert the referral to JSON using the configured ObjectMapper
            byte[] jsonBytes = objectMapper.writeValueAsBytes(commonReferral);
            // Send to the Pulsar topic
            producer
                    .newMessage()
                    .key(commonReferral.getHealiusId())
                    .value(jsonBytes)
                    .send();
            log.info(
                    "Successfully sent referral with ID {} to Pulsar topic: {}",
                    commonReferral.getHealiusId(),
                    topic
            );
        }
    }
}