/*
 * SVC Booking API
 * Healius Booking api
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package au.com.healius.digital.collectorportal.mocking.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.time.LocalDate;
import java.util.Objects;
import java.util.UUID;

/**
 * SearchBookingsFilters
 */
@JsonPropertyOrder({
  SearchBookingsFilters.JSON_PROPERTY_LOCATION_ID,
  SearchBookingsFilters.JSON_PROPERTY_INCLUDE_CANCELLED,
  SearchBookingsFilters.JSON_PROPERTY_DATE_ON,
  SearchBookingsFilters.JSON_PROPERTY_DATE_BEFORE,
  SearchBookingsFilters.JSON_PROPERTY_DATE_AFTER,
  SearchBookingsFilters.JSON_PROPERTY_REMINDER_SENT,
  SearchBookingsFilters.JSON_PROPERTY_UPDATED_BEFORE
})
@JsonTypeName("searchBookingsFilters")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-19T20:15:18.693725+10:00[Australia/Sydney]", comments = "Generator version: 7.10.0")
public class SearchBookingsFilters {
  public static final String JSON_PROPERTY_LOCATION_ID = "locationId";
  @jakarta.annotation.Nullable
  private UUID locationId;

  public static final String JSON_PROPERTY_INCLUDE_CANCELLED = "includeCancelled";
  @jakarta.annotation.Nullable
  private Boolean includeCancelled = false;

  public static final String JSON_PROPERTY_DATE_ON = "dateOn";
  @jakarta.annotation.Nullable
  private LocalDate dateOn;

  public static final String JSON_PROPERTY_DATE_BEFORE = "dateBefore";
  @jakarta.annotation.Nullable
  private LocalDate dateBefore;

  public static final String JSON_PROPERTY_DATE_AFTER = "dateAfter";
  @jakarta.annotation.Nullable
  private LocalDate dateAfter;

  public static final String JSON_PROPERTY_REMINDER_SENT = "reminderSent";
  @jakarta.annotation.Nullable
  private Boolean reminderSent;

  public static final String JSON_PROPERTY_UPDATED_BEFORE = "updatedBefore";
  @jakarta.annotation.Nullable
  private LocalDate updatedBefore;

  public SearchBookingsFilters() {
  }

  public SearchBookingsFilters locationId(@jakarta.annotation.Nullable UUID locationId) {
    
    this.locationId = locationId;
    return this;
  }

  /**
   * locations db primary id of location this booking is at
   * @return locationId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOCATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public UUID getLocationId() {
    return locationId;
  }


  @JsonProperty(JSON_PROPERTY_LOCATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLocationId(@jakarta.annotation.Nullable UUID locationId) {
    this.locationId = locationId;
  }

  public SearchBookingsFilters includeCancelled(@jakarta.annotation.Nullable Boolean includeCancelled) {
    
    this.includeCancelled = includeCancelled;
    return this;
  }

  /**
   * return cancelled orders as well as not cancelled
   * @return includeCancelled
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INCLUDE_CANCELLED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIncludeCancelled() {
    return includeCancelled;
  }


  @JsonProperty(JSON_PROPERTY_INCLUDE_CANCELLED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIncludeCancelled(@jakarta.annotation.Nullable Boolean includeCancelled) {
    this.includeCancelled = includeCancelled;
  }

  public SearchBookingsFilters dateOn(@jakarta.annotation.Nullable LocalDate dateOn) {
    
    this.dateOn = dateOn;
    return this;
  }

  /**
   * bookings on a particular day
   * @return dateOn
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_ON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDateOn() {
    return dateOn;
  }


  @JsonProperty(JSON_PROPERTY_DATE_ON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateOn(@jakarta.annotation.Nullable LocalDate dateOn) {
    this.dateOn = dateOn;
  }

  public SearchBookingsFilters dateBefore(@jakarta.annotation.Nullable LocalDate dateBefore) {
    
    this.dateBefore = dateBefore;
    return this;
  }

  /**
   * bookings before this date
   * @return dateBefore
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_BEFORE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDateBefore() {
    return dateBefore;
  }


  @JsonProperty(JSON_PROPERTY_DATE_BEFORE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateBefore(@jakarta.annotation.Nullable LocalDate dateBefore) {
    this.dateBefore = dateBefore;
  }

  public SearchBookingsFilters dateAfter(@jakarta.annotation.Nullable LocalDate dateAfter) {
    
    this.dateAfter = dateAfter;
    return this;
  }

  /**
   * bookings after this date
   * @return dateAfter
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_AFTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDateAfter() {
    return dateAfter;
  }


  @JsonProperty(JSON_PROPERTY_DATE_AFTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateAfter(@jakarta.annotation.Nullable LocalDate dateAfter) {
    this.dateAfter = dateAfter;
  }

  public SearchBookingsFilters reminderSent(@jakarta.annotation.Nullable Boolean reminderSent) {
    
    this.reminderSent = reminderSent;
    return this;
  }

  /**
   * has a reminder been sent
   * @return reminderSent
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMINDER_SENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReminderSent() {
    return reminderSent;
  }


  @JsonProperty(JSON_PROPERTY_REMINDER_SENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReminderSent(@jakarta.annotation.Nullable Boolean reminderSent) {
    this.reminderSent = reminderSent;
  }

  public SearchBookingsFilters updatedBefore(@jakarta.annotation.Nullable LocalDate updatedBefore) {
    
    this.updatedBefore = updatedBefore;
    return this;
  }

  /**
   * booking record was last updated before this date, nothing has changed since then (e.g. reminderSent)
   * @return updatedBefore
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UPDATED_BEFORE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getUpdatedBefore() {
    return updatedBefore;
  }


  @JsonProperty(JSON_PROPERTY_UPDATED_BEFORE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUpdatedBefore(@jakarta.annotation.Nullable LocalDate updatedBefore) {
    this.updatedBefore = updatedBefore;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SearchBookingsFilters searchBookingsFilters = (SearchBookingsFilters) o;
    return Objects.equals(this.locationId, searchBookingsFilters.locationId) &&
        Objects.equals(this.includeCancelled, searchBookingsFilters.includeCancelled) &&
        Objects.equals(this.dateOn, searchBookingsFilters.dateOn) &&
        Objects.equals(this.dateBefore, searchBookingsFilters.dateBefore) &&
        Objects.equals(this.dateAfter, searchBookingsFilters.dateAfter) &&
        Objects.equals(this.reminderSent, searchBookingsFilters.reminderSent) &&
        Objects.equals(this.updatedBefore, searchBookingsFilters.updatedBefore);
  }

  @Override
  public int hashCode() {
    return Objects.hash(locationId, includeCancelled, dateOn, dateBefore, dateAfter, reminderSent, updatedBefore);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SearchBookingsFilters {\n");
    sb.append("    locationId: ").append(toIndentedString(locationId)).append("\n");
    sb.append("    includeCancelled: ").append(toIndentedString(includeCancelled)).append("\n");
    sb.append("    dateOn: ").append(toIndentedString(dateOn)).append("\n");
    sb.append("    dateBefore: ").append(toIndentedString(dateBefore)).append("\n");
    sb.append("    dateAfter: ").append(toIndentedString(dateAfter)).append("\n");
    sb.append("    reminderSent: ").append(toIndentedString(reminderSent)).append("\n");
    sb.append("    updatedBefore: ").append(toIndentedString(updatedBefore)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  public static class Builder {

    private SearchBookingsFilters instance;

    public Builder() {
      this(new SearchBookingsFilters());
    }

    protected Builder(SearchBookingsFilters instance) {
      this.instance = instance;
    }

    public Builder locationId(UUID locationId) {
      this.instance.locationId = locationId;
      return this;
    }
    public Builder includeCancelled(Boolean includeCancelled) {
      this.instance.includeCancelled = includeCancelled;
      return this;
    }
    public Builder dateOn(LocalDate dateOn) {
      this.instance.dateOn = dateOn;
      return this;
    }
    public Builder dateBefore(LocalDate dateBefore) {
      this.instance.dateBefore = dateBefore;
      return this;
    }
    public Builder dateAfter(LocalDate dateAfter) {
      this.instance.dateAfter = dateAfter;
      return this;
    }
    public Builder reminderSent(Boolean reminderSent) {
      this.instance.reminderSent = reminderSent;
      return this;
    }
    public Builder updatedBefore(LocalDate updatedBefore) {
      this.instance.updatedBefore = updatedBefore;
      return this;
    }


    /**
    * returns a built SearchBookingsFilters instance.
    *
    * The builder is not reusable.
    */
    public SearchBookingsFilters build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field.
  */
  public static Builder builder() {
    return new Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public Builder toBuilder() {
    return new Builder()
      .locationId(getLocationId())
      .includeCancelled(getIncludeCancelled())
      .dateOn(getDateOn())
      .dateBefore(getDateBefore())
      .dateAfter(getDateAfter())
      .reminderSent(getReminderSent())
      .updatedBefore(getUpdatedBefore());
  }


}

