package au.com.healius.digital.collectorportal.config;

import org.neo4j.driver.AuthTokens;
import org.neo4j.driver.Driver;
import org.neo4j.driver.GraphDatabase;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.neo4j.Neo4jProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.neo4j.core.DatabaseSelectionProvider;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.data.neo4j.core.mapping.Neo4jMappingContext;
import org.springframework.data.neo4j.core.transaction.Neo4jTransactionManager;
import org.springframework.data.neo4j.repository.config.EnableNeo4jRepositories;

/**
 * Configuration class for Neo4j data access and repository setup.
 * This class configures the primary Neo4j database connection, transaction management,
 * and repository support for the application.
 */
@Configuration
@EnableNeo4jRepositories(
        basePackages = {
                "au.com.healius.digital.collectorportal.domain",
                "au.com.healius.digital.librarycollections.repository",
                "au.com.healius.digital.trm.repository",
        },
        transactionManagerRef = Neo4jDataConfig.TRANSACTION_MANAGER,
        neo4jTemplateRef = Neo4jDataConfig.TEMPLATE_REF
)
public class Neo4jDataConfig {

    /** The name of the Neo4j transaction manager bean */
    static final String TRANSACTION_MANAGER = "neo4jDataTxManager";
    
    /** The name of the Neo4j template bean */
    static final String TEMPLATE_REF = "neo4jDataTemplate";

    @Value("${healius.neo4j.data.database}")
    private String database;

    /**
     * Creates and configures Neo4j properties from application properties.
     * 
     * @return Configured Neo4jProperties instance
     */
    @Bean
    @ConfigurationProperties("healius.neo4j.data.properties")
    public Neo4jProperties neo4jDataProperties() {
        return new Neo4jProperties();
    }

    /**
     * Creates the primary Neo4j transaction manager.
     * 
     * @return Configured Neo4jTransactionManager instance
     */
    @Primary
    @Bean(name = TRANSACTION_MANAGER)
    public Neo4jTransactionManager neo4jTransactionManager() {
        return Neo4jTransactionManager
                .with(neo4jDataDBDriver())
                .withDatabaseSelectionProvider(databaseDataSelectionProvider())
                .build();
    }

    /**
     * Creates the primary Neo4j mapping context for entity mapping.
     * 
     * @return Neo4jMappingContext instance
     */
    @Primary
    @Bean
    public Neo4jMappingContext neo4jMappingContext() {
        return new Neo4jMappingContext();
    }

    /**
     * Creates the primary Neo4j template for database operations.
     * 
     * @return Configured Neo4jTemplate instance
     */
    @Primary
    @Bean(name = TEMPLATE_REF)
    public Neo4jTemplate neo4jTemplate() {
        return new Neo4jTemplate(
            neo4jClient(),
            neo4jMappingContext(),
            neo4jTransactionManager()
        );
    }

    /**
     * Creates the primary Neo4j client for database operations.
     * 
     * @return Configured Neo4jClient instance
     */
    @Primary
    @Bean
    public Neo4jClient neo4jClient() {
        return Neo4jClient.with(neo4jDataDBDriver())
                .withDatabaseSelectionProvider(databaseDataSelectionProvider())
                .build();
    }

    /**
     * Creates the Neo4j driver instance for database connectivity.
     * 
     * @return Configured Neo4j Driver instance
     */
    @Primary
    @Bean
    public Driver neo4jDataDBDriver() {
        return GraphDatabase.driver(
                neo4jDataProperties().getUri(),
                AuthTokens.basic(
                        neo4jDataProperties().getAuthentication().getUsername(),
                        neo4jDataProperties().getAuthentication().getPassword()));
    }

    /**
     * Creates a database selection provider for Neo4j operations.
     * 
     * @return DatabaseSelectionProvider instance configured with the specified database
     */
    @Bean
    public DatabaseSelectionProvider databaseDataSelectionProvider() {
        return DatabaseSelectionProvider.createStaticDatabaseSelectionProvider(database);
    }
}
