package au.com.healius.digital.collectorportal.config;

import au.com.healius.digital.collectorportal.component.CancelPaymentInterceptor;
import com.fasterxml.jackson.databind.cfg.CoercionAction;
import com.fasterxml.jackson.databind.cfg.CoercionInputShape;
import com.fasterxml.jackson.databind.type.LogicalType;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.Collections;

/**
 * MVC (Model-View-Controller) configuration class.
 * Configures web-related settings including interceptors, CORS, and JSON serialization.
 */
@Configuration
public class MVCConfiguration implements WebMvcConfigurer {

    private ObjectFactory<CancelPaymentInterceptor> cancelPaymentInterceptorFactory;

    @Autowired
    public void setCancelPaymentInterceptor(ObjectFactory<CancelPaymentInterceptor> cancelPaymentInterceptorFactory) {
        this.cancelPaymentInterceptorFactory = cancelPaymentInterceptorFactory;
    }

    /**
     * Adds interceptors to the application.
     * Currently configures the payment cancellation interceptor for specific paths.
     * 
     * @param registry The InterceptorRegistry to configure
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(cancelPaymentInterceptorFactory.getObject())
                .addPathPatterns(
                        "/collection/ref/*/collecting");
    }

    /**
     * Configures CORS (Cross-Origin Resource Sharing) settings.
     * Allows requests from any origin with specified methods and headers.
     * 
     * @return Configured CorsFilter instance
     */
    @Bean
    public CorsFilter corsFilter() {
        final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        final CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.setAllowedOrigins(Collections.singletonList("*"));
        config.setAllowedHeaders(Arrays.asList("Origin", "Content-Type", "Accept"));
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "OPTIONS", "DELETE", "PATCH"));
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }

    /**
     * Customizes Jackson JSON serialization behavior.
     * Configures how empty strings should be handled for enum types.
     * 
     * @return Configured Jackson2ObjectMapperBuilderCustomizer
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jsonCustomizer() {
        return builder -> builder.postConfigurer(objectMapper -> objectMapper.coercionConfigFor(LogicalType.Enum)
                .setCoercion(CoercionInputShape.EmptyString, CoercionAction.AsNull));
    }

}
