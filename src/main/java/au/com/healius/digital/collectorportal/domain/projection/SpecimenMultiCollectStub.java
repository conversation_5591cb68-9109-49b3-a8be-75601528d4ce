package au.com.healius.digital.collectorportal.domain.projection;

import au.com.healius.digital.librarycollections.domain.enumeration.CollectionSampleStatus;

/**
 * Interface representing a simplified view of a multi-collect specimen in the collector portal.
 * Provides access to multi-collect specimen information including custom pricing and collection status.
 * 
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
public interface SpecimenMultiCollectStub {
    /**
     * Gets the unique identifier of the multi-collect specimen.
     *
     * @return The multi-collect specimen ID
     */
    String getId();

    /**
     * Gets the custom price set for the multi-collect specimen.
     *
     * @return The custom price amount
     */
    long getCustomPrice();

    /**
     * Checks if the custom price was set by the collector.
     *
     * @return true if set by collector, false otherwise
     */
    boolean isCustomPriceSetByCollector();

    /**
     * Gets the collection sample status of the multi-collect specimen.
     *
     * @return The collection sample status
     */
    CollectionSampleStatus getCollectionSampleStatus();

    /**
     * Checks if the multi-collect specimen was manually added.
     *
     * @return true if manually added, false otherwise
     */
    boolean isManuallyAdded();
}
