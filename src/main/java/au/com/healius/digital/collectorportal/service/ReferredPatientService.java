package au.com.healius.digital.collectorportal.service;

import au.com.healius.digital.collectorportal.dto.OrderSearchDTO;
import au.com.healius.digital.collectorportal.dto.PatientSearch;
import au.com.healius.digital.collectorportal.identity.api.model.LookupByDemographicsApiResponse;
import au.com.healius.digital.collectorportal.identity.api.model.ProfileLookupResponseItem;
import au.com.healius.digital.collectorportal.web.api.SearchAPI;
import au.com.healius.digital.librarycollections.domain.Order;
import au.com.healius.digital.librarycollections.domain.enumeration.PathologyOrderWorkflow;
import au.com.healius.digital.librarycollections.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReferredPatientService {

    final OrderRepository orderRepository;
    final SearchAPI searchAPI;

    public boolean existsByHealiusId(String healiusId) {
        try {
            return orderRepository.existsByHealiusId(healiusId);
        } catch (Exception e) {
            log.error("Error checking if healiusId exists: {}", healiusId, e);
            return false;
        }
    }

    public List<ProfileLookupResponseItem> findPatients(PatientSearch search) {
        try {
            if (search == null) {
                log.warn("Patient search criteria is null");
                return Collections.emptyList();
            }

            LookupByDemographicsApiResponse response = searchAPI.searchIdentity(
                    PatientSearch.builder()
                            .givenName(search.getGivenName())
                            .familyName(search.getFamilyName())
                            .mobileNumber(search.getMobileNumber())
                            .medicareNo(search.getMedicareNo())
                            .dateOfBirth(search.getDateOfBirth())
                            .build());

            return Optional.ofNullable(response)
                    .map(LookupByDemographicsApiResponse::getPayload)
                    .map(payload -> payload.getProfileLookupResponseItems())
                    .orElse(Collections.emptyList());

        } catch (Exception e) {
            log.error("Error searching for patients with criteria: {}", search, e);
            return Collections.emptyList();
        }
    }

    public List<Order> findEreferralOrders(PatientSearch search) {
        try {
            if (search == null) {
                log.warn("Patient search criteria is null");
                return Collections.emptyList();
            }

            OrderSearchDTO searchDTO = OrderSearchDTO.builder()
                    .familyName(search.getFamilyName())
                    .givenName(search.getGivenName())
                    .mobilePhone(search.getMobileNumber())
                    .medicareNo(search.getMedicareNo())
                    .dateOfBirth(search.getDateOfBirth())
                    .workflow(PathologyOrderWorkflow.E_ORDER)
                    .build();

            return Optional.ofNullable(searchAPI.searchOrder(searchDTO, PageRequest.of(0, 10)))
                    .map(orders -> orders.stream().toList())
                    .orElse(Collections.emptyList());

        } catch (Exception e) {
            log.error("Error searching for eReferral orders with criteria: {}", search, e);
            return Collections.emptyList();
        }
    }
}
