package au.com.healius.digital.collectorportal.service.paymentcalculator;

import au.com.healius.digital.collectorportal.dto.VisitCostDTO;
import au.com.healius.digital.librarycollections.domain.Visit;

/**
 * Interface defining the contract for calculating visit costs based on different payment schemes.
 * Implementations of this interface handle specific payment calculation logic for different types of visits
 * such as Medicare, DVA, Private, Overseas, and Bulk Bill visits.
 */
public interface PaymentCalculator {
    /**
     * Calculates the total cost for a visit based on various parameters.
     *
     * @param visit The visit for which to calculate costs
     * @param rebatableCount Number of rebatable items
     * @param criteriaExceptionCount Number of items with criteria exceptions
     * @param exceptionCount Number of items with general exceptions
     * @param nonRebatableCost Cost of non-rebatable items
     * @param criteriaCost Cost of items with criteria exceptions
     * @param exceptionCost Cost of items with general exceptions
     * @param unknownCompanyCost Cost of items with unknown company
     * @return VisitCostDTO containing the calculated costs
     */
    VisitCostDTO getVisitCost(Visit visit,
                              int rebatableCount,
                              int criteriaExceptionCount,
                              int exceptionCount,
                              long nonRebatableCost,
                              long criteriaCost,
                              long exceptionCost,
                              long unknownCompanyCost);
}
