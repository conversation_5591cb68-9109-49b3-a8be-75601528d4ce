package au.com.healius.digital.collectorportal.converter;

import au.com.healius.digital.collectorportal.dto.InstantResultsDetails;
import au.com.healius.digital.librarycollections.domain.SpecimenInstantResult;
import au.com.healius.digital.trm.repository.CompanyInstantResultRepository;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

/**
 * Converter class that transforms a {@link SpecimenInstantResult} domain object into an {@link InstantResultsDetails} DTO.
 * This converter handles the mapping of instant result details from a specimen, including measurement values
 * and associated company instant result information.
 *
 * <AUTHOR> Digital
 * @since 1.0
 */
@RequiredArgsConstructor
@Component
public class InstantResultsDetailsConverter implements Converter<SpecimenInstantResult, InstantResultsDetails> {

  private final CompanyInstantResultRepository companyInstantResultRepository;
  private final CompanyInstantResultConverter companyInstantResultConverter;

  /**
   * Converts a {@link SpecimenInstantResult} domain object to an {@link InstantResultsDetails} DTO.
   * This method maps the instant result details including:
   * - Result identification (ID and instant result ID)
   * - Measurement details (name, value, type, measurement)
   * - Value ranges and display settings (min/max values, decimals, display type)
   * - Error information
   * - Associated orderable ID
   * - Company instant result details (converted using CompanyInstantResultConverter)
   *
   * @param source the source SpecimenInstantResult object to convert
   * @return an InstantResultsDetails DTO containing the converted data
   * @throws NullPointerException if the source parameter is null
   * @throws RuntimeException if the associated company instant result cannot be found
   */
  @Override
  @NotNull
  public InstantResultsDetails convert(SpecimenInstantResult source) {

    return InstantResultsDetails.builder()
        .id(source.getId())
        .instantResultId(source.getInstantResultId())
        .name(source.getName())
        .value(source.getValue())
        .type(source.getType())
        .measurement(source.getMeasurement())
        .minValue(source.getMinValue())
        .maxValue(source.getMaxValue())
        .decimals(source.getDecimals())
        .displayType(source.getDisplayType())
        .errorMessage(source.getErrorMessage())
        .orderableId(source.getOrderableIds().get(0))
        .companyInstantResult(companyInstantResultConverter.convert(companyInstantResultRepository.findByIdDepth1(source.getCompanyInstantResultId()).orElseThrow()))
        .build();
  }
}

