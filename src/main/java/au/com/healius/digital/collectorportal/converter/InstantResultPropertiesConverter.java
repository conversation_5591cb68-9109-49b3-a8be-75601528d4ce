package au.com.healius.digital.collectorportal.converter;

import au.com.healius.digital.trm.domain.CompanyInstantResultProperties;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

/**
 * Converter class that transforms a {@link CompanyInstantResultProperties} domain object into another {@link CompanyInstantResultProperties} object.
 * This converter handles the mapping of instant result property data, including its value and label.
 *
 * <AUTHOR> Digital
 * @since 1.0
 */
@RequiredArgsConstructor
@Component
public class InstantResultPropertiesConverter implements Converter<CompanyInstantResultProperties, CompanyInstantResultProperties> {

    /**
     * Converts a {@link CompanyInstantResultProperties} domain object to another {@link CompanyInstantResultProperties} object.
     * This method maps the instant result property details including:
     * - Property ID
     * - Property value
     * - Property label
     *
     * @param source the source CompanyInstantResultProperties object to convert
     * @return a new CompanyInstantResultProperties object containing the converted data
     * @throws NullPointerException if the source parameter is null
     */
    @Override
    @NotNull
    public CompanyInstantResultProperties convert(CompanyInstantResultProperties source) {
        CompanyInstantResultProperties companyInstantResultProperties = new CompanyInstantResultProperties();
        companyInstantResultProperties.setId(source.getId());
        companyInstantResultProperties.setValue(source.getValue());
        companyInstantResultProperties.setLabel(source.getLabel());
        return companyInstantResultProperties;
    }
}

