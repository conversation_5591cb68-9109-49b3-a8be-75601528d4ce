package au.com.healius.digital.collectorportal.service;

import au.com.healius.digital.collectorportal.healiusidentifier.QueryIdentifierApi;
import au.com.healius.digital.collectorportal.healiusidentifier.invoker.ApiClient;
import au.com.healius.digital.collectorportal.healiusidentifier.invoker.auth.HttpBearerAuth;
import au.com.healius.digital.collectorportal.healiusidentifier.model.GetUniqueIDResponse;
import au.com.healius.digital.collectorportal.healiusidentifier.model.GetUniqueIDResponsePayload;
import com.azure.core.credential.TokenRequestContext;
import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.util.Random;

@RequiredArgsConstructor
@Service
@Log4j2
public class HealiusIdentifierService {
    @Value("${healius.digital.identifier.api.base-url}")
    private final String baseUrl;
    @Value("${spring.cloud.azure.active-directory.profile.tenant-id}")
    private final String tenantId;
    @Value("${spring.cloud.azure.active-directory.credential.client-id}")
    private final String clientId;
    @Value("${spring.cloud.azure.active-directory.credential.client-secret}")
    private final String clientSecret;
    @Value("${spring.cloud.azure.active-directory.authorization-clients.identifier.scopes[0]}")
    private final String scope;
    @Value("${spring.profiles.active:}")
    private final String activeProfile;


    private String generateEntraToken() {
        ClientSecretCredential credential = new ClientSecretCredentialBuilder()
                .tenantId(tenantId)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .build();
        TokenRequestContext context = new TokenRequestContext().addScopes(scope);
        return credential.getToken(context).block().getToken();
    }

    public ApiClient getApiClient() {
        ApiClient apiClient = new ApiClient();
        apiClient.setBasePath(baseUrl);
        apiClient.addDefaultHeader("Authorization", "Bearer " + generateEntraToken());
        return apiClient;
    }

    /**
     * Generates a random 8-digit string
     * @return random 8-digit string
     */
    private String generateRandomEightDigit() {
        if (!"local".equalsIgnoreCase(activeProfile)) {
            throw new IllegalStateException("Random generation is only available in local profile");
        }
        Random random = new Random();
        return String.format("%08d", random.nextInt(100000000));
    }

    /**
     * Gets a unique eight-digit identifier
     * @return Mono containing the unique ID response or random string if service is down (local profile only)
     */
    public Mono<GetUniqueIDResponse> getEightDigitIdentifier() {
        QueryIdentifierApi queryIdentifierApi = new QueryIdentifierApi(getApiClient());
        return queryIdentifierApi.eightDigits()
            .onErrorResume(WebClientResponseException.class, e -> {
                log.error("Error getting eight-digit identifier: {}", e.getMessage());
                return "local".equalsIgnoreCase(activeProfile) ?
                    createResponseWithRandomId(generateRandomEightDigit()) : 
                    Mono.empty();
            })
            .onErrorResume(Exception.class, e -> {
                log.error("Unexpected error getting eight-digit identifier: {}", e.getMessage());
                return "local".equalsIgnoreCase(activeProfile) ?
                    createResponseWithRandomId(generateRandomEightDigit()) : 
                    Mono.empty();
            });
    }

    /**
     * Creates a response with a random ID
     * @param randomId the random ID to use
     * @return Mono containing the response with the random ID
     */
    private Mono<GetUniqueIDResponse> createResponseWithRandomId(String randomId) {
        GetUniqueIDResponse response = new GetUniqueIDResponse();
        response.setPayload(new GetUniqueIDResponsePayload());
        response.getPayload().setId(randomId);
        return Mono.just(response);
    }
} 