package au.com.healius.digital.collectorportal.domain.projection;

import au.com.healius.digital.fhir.neo4j.files.domain.enumeration.FhirOrderStatus;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Interface representing a simplified view of a FHIR order audit in the collector portal.
 * Provides access to audit information including collection timestamps, warnings, and order status.
 * 
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
public interface UpdateFhirOrderAuditStub {
    /**
     * Gets the unique identifier of the FHIR order audit.
     *
     * @return The audit ID
     */
    String getId();

    /**
     * Gets the date and time when collection started.
     * Currently, this is when the collector hits the print button.
     *
     * @return The collection start date and time
     */
    ZonedDateTime getCollectingStartDateTime();

    /**
     * Gets the date and time when collection was completed.
     *
     * @return The collection completion date and time
     */
    ZonedDateTime getCollectingFinishedDateTime();

    /**
     * Gets the list of warnings associated with the FHIR order.
     *
     * @return List of warning messages
     */
    List<String> getWarnings();

    /**
     * Gets the current FHIR order status.
     *
     * @return The FHIR order status
     */
    FhirOrderStatus getFhirOrderStatus();
}
