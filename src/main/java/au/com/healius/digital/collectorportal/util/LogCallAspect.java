package au.com.healius.digital.collectorportal.util;

import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.Level;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.function.IntPredicate;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.joining;

/**
 * Aspect for logging method calls annotated with {@link LogCall}.
 * This aspect provides method call logging functionality with configurable log levels
 * and the ability to include/exclude parameters and return values from the log output.
 * 
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
@Log4j2
@Aspect
@Component
@ConditionalOnProperty("healius.digital.log.call")
public class LogCallAspect {
    @Value("${healius.digital.log.call}")
    private Level logLevel;

    /**
     * Intercepts method calls annotated with {@link LogCall} and logs the method invocation details.
     * The logging includes:
     * - Class name and method name
     * - Method parameters (excluding those marked with {@link LogCall.Exclude})
     * - Return value (if {@link LogCall#includeReturn()} is true)
     *
     * @param joinPoint The join point representing the method call
     * @return The result of the method call
     * @throws Throwable If the method call throws an exception
     */
    @Around("@annotation(au.com.healius.digital.collectorportal.util.LogCall)")
    public Object logBefore(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methodName = signature.getMethod().getName();
        if (log.isEnabled(logLevel)) {
            Object[] args = joinPoint.getArgs();
            Class<?>[] paramTypes = ((CodeSignature) joinPoint.getSignature()).getParameterTypes();
            String[] paramNames = signature.getParameterNames();
            Annotation[][] annotations = joinPoint.getTarget().getClass().getMethod(methodName, paramTypes).getParameterAnnotations();

            IntPredicate isIncluded = i -> Arrays.stream(annotations[i])
                    .noneMatch(annotation -> annotation.annotationType() == LogCall.Exclude.class);

            String parameters = IntStream.range(0, paramNames.length)
                    .filter(isIncluded)
                    .mapToObj(i -> String.format("%s = %s", paramNames[i], args[i]))
                    .collect(joining(", "));

            log.atLevel(logLevel)
                    .log("LogCall: {}.{} with parameters: {}", className, methodName, parameters);

        }
        Object returnValue = joinPoint.proceed();
        Method method = joinPoint.getTarget().getClass()
                .getMethod(methodName, signature.getMethod().getParameterTypes());

        LogCall logCall = method.getAnnotation(LogCall.class);

        if (logCall.includeReturn() && log.isEnabled(logLevel)) {
            log.atLevel(logLevel)
                    .log("LogCall: {}.{} returned: {}", className, methodName, returnValue);
        }
        return returnValue;
    }
}
