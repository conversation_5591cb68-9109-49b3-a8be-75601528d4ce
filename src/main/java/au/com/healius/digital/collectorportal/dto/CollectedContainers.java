package au.com.healius.digital.collectorportal.dto;

import lombok.Data;

import java.util.List;

/**
 * Data Transfer Object representing a collection of specimen containers.
 * This class contains information about both known and unknown containers
 * that have been collected during a visit.
 */
@Data
public class CollectedContainers {
    /** List of known containers that have been collected */
    private List<ContainerTypeCollectedDTO> containerTypes;
    
    /** Free text description of any unknown containers collected */
    private String unknownContainers;
}
