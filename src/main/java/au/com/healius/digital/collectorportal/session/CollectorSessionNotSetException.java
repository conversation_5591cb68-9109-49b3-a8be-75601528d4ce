package au.com.healius.digital.collectorportal.session;

/**
 * Exception thrown when an operation requiring a valid collector session is attempted
 * without a properly initialized session. This typically occurs when a user tries to
 * access protected resources without being properly authenticated.
 *
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
public class CollectorSessionNotSetException extends RuntimeException {
    /**
     * Constructs a new CollectorSessionNotSetException with a default message
     * indicating that the collector session is not set.
     */
    public CollectorSessionNotSetException() {
        super("Collector Session not set");
    }
}