package au.com.healius.digital.collectorportal.service.payment;

import au.com.healius.digital.collectorportal.service.SquareService;
import au.com.healius.digital.librarycollections.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.ZoneId;

/**
 * Service responsible for cleaning up incomplete payments in the system.
 * This service runs on a daily schedule to identify and clean up any payments
 * that were not properly completed.
 *
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class PaymentCleanupService {
    private final OrderRepository orderRepository;
    private final SquareService squareService;

    /**
     * Scheduled task that runs daily at midnight to clean up incomplete payments.
     * For each incomplete payment found, it logs the cleanup action and attempts
     * to clean up the payment using the Square service.
     */
    @Scheduled(cron = "0 0 0 * * *")
    public void cleanup() {
        orderRepository.findIncompletePaymentHealiusIds().forEach(healiusId -> {
            log.info("Cleanup incomplete payment for {}", healiusId);
            squareService.cleanupPayment(healiusId, ZoneId.systemDefault());
        });
    }
}
