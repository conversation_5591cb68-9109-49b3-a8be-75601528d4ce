package au.com.healius.digital.collectorportal.dto;

import au.com.healius.digital.librarycollections.domain.enumeration.CommercialPaymentType;
import au.com.healius.digital.librarycollections.domain.enumeration.PaymentMethod;
import au.com.healius.digital.librarycollections.domain.enumeration.PaymentProvider;
import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * DTO representing payment information for an order.
 * This class contains details about the payment transaction, including payment provider,
 * method, and various identifiers for tracking the payment.
 */
@Builder
@Data
public class OrderPaymentStub {
    /** Date and time when the payment was made */
    private ZonedDateTime paymentDate;
    
    /** Provider used for the payment (e.g., Square, BPoint) */
    private PaymentProvider paymentProvider;
    
    /** Method of payment (e.g., credit card, cash) */
    private PaymentMethod paymentMethod;
    
    /** Type of commercial payment */
    private CommercialPaymentType commercialPaymentType;
    
    /** Receipt number for the payment */
    private long receiptNumber;
    
    /** Amount of the payment */
    private long paymentAmount;
    
    /** Indicates if the payment was successfully taken */
    private boolean paymentTaken;
    
    /** Square checkout identifier */
    private String squareCheckoutId;
    
    /** Square receipt number */
    private String squareReceiptNumber;
    
    /** Square order identifier */
    private String squareOrderId;
    
    /** Square payment identifier */
    private String squarePaymentId;
    
    /** BPoint receipt number */
    private String bPointReceiptNo;
    
    /** Amount paid through BPoint */
    private String bPointAmountPaid;
    
    /** Name of the Square terminal used */
    private String squareTerminalName;
}
