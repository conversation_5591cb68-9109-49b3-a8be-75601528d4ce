package au.com.healius.digital.collectorportal.dto;

import au.com.healius.digital.librarycollections.service.dto.ReferrerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for adding a new referrer to the system.
 * This class extends the base ReferrerDTO and adds functionality
 * to handle unknown referrers.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddReferrerDTO extends ReferrerDTO {
    /** Flag indicating if the referrer is unknown */
    private boolean unknownReferrer = false;
}
