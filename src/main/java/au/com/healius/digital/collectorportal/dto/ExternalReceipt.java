package au.com.healius.digital.collectorportal.dto;

import groovy.transform.builder.Builder;
import lombok.Data;

/**
 * Data Transfer Object representing an external payment receipt.
 * Contains basic receipt information for external payment systems.
 */
@Data
@Builder
public class ExternalReceipt {
    /** Receipt number from the external payment system */
    private String receiptNumber;
    
    /** Amount recorded on the receipt */
    private String receiptAmount;
}
