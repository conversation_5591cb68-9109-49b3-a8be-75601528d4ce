package au.com.healius.digital.collectorportal.service;

import au.com.healius.digital.collectorportal.domain.projection.UpdateFhirOrderAuditStub;
import au.com.healius.digital.fhir.neo4j.files.domain.FhirOrderAudit;
import au.com.healius.digital.fhir.neo4j.files.domain.enumeration.FhirOrderStatus;
import au.com.healius.digital.fhir.neo4j.files.repository.FhirOrderAuditRepository;
import au.com.healius.digital.pulsar.dto.FhirTaskGroupStatusUpdateMessage;
import au.com.healius.digital.pulsar.dto.enumeration.FhirTaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Service class responsible for handling FHIR-related operations including order status updates
 * and audit tracking. This service manages the communication between the collector portal
 * and the FHIR system, ensuring proper status updates and audit trail maintenance.
 */
@Log4j2
@RequiredArgsConstructor
@Service
public class FhirService {

    @Autowired
    @Qualifier("neo4jFilesTemplate")
    private final Neo4jTemplate neo4jFilesTemplate;
    private final PulsarService pulsarService;
    private final FhirOrderAuditRepository fhirOrderAuditRepository;

    /**
     * Marks the start of a collection process for a given data node and flow type.
     * Updates the FHIR order status, sends task status updates, and maintains audit records.
     *
     * @param dataNodeId The unique identifier of the data node
     * @param flow The type of flow (e.g., "paper" or other flow types)
     */
    public void collectionStarted(String dataNodeId, String flow) {
        FhirOrderAudit audit = new FhirOrderAudit();
        audit.setCollectingStartDateTime(ZonedDateTime.now());
        audit.setFhirOrderStatus(FhirOrderStatus.COLLECTION_STARTED);
        List<String> warnings = new ArrayList<>();
        try {
            taskStatusUpdate(dataNodeId, flow);
        } catch (Exception e) {
            log.warn(String.format("(dataNodeId: %s) error sending pulsar message to update fhir status: %s", dataNodeId, e));
            warnings.add(String.format("error updating task status fhir-side: %s", e.getMessage()));
        }

        if (Objects.equals(flow, "paper")) {
            warnings.add("this order is paper, it will never get collection finished status");
        }

        audit.setWarnings(warnings);

        try {
            updateAudit(dataNodeId, audit);
        } catch (Exception e) {
            log.warn("(dataNodeId: {}) error updating audit: {}", dataNodeId, e);
        }
    }

    /**
     * Sends a task status update message via Pulsar to update the FHIR task status.
     *
     * @param dataNodeId The unique identifier of the data node
     * @param flow The type of flow being processed
     * @throws PulsarClientException If there is an error sending the Pulsar message
     */
    private void taskStatusUpdate(String dataNodeId, String flow) throws PulsarClientException {
        FhirTaskGroupStatusUpdateMessage message = new FhirTaskGroupStatusUpdateMessage();
        message.setFhirReferralId(dataNodeId);
        message.setReason(String.format("order has started %s flow", flow));
        message.setStatus(FhirTaskStatus.IN_PROGRESS);
        pulsarService.sendTaskStatusUpdate(message);
    }

    /**
     * Updates the audit record for a given data node with new audit information.
     * Merges existing audit data with new updates while preserving historical information.
     *
     * @param nodeId The unique identifier of the data node
     * @param auditUpdate The new audit information to be merged with existing data
     */
    private void updateAudit(String nodeId, FhirOrderAudit auditUpdate) {
        FhirOrderAudit existingAudit = fhirOrderAuditRepository.findFhirOrderAuditByFhirTaskDataId(nodeId);
        assert existingAudit != null;

        if (auditUpdate.getCollectingStartDateTime() != null) {
            existingAudit.setCollectingStartDateTime(auditUpdate.getCollectingStartDateTime());
        }

        if (auditUpdate.getCollectingFinishedDateTime() != null) {
            existingAudit.setCollectingFinishedDateTime(auditUpdate.getCollectingFinishedDateTime());
        }

        if (auditUpdate.getFhirOrderStatus() != null) {
            existingAudit.setFhirOrderStatus(auditUpdate.getFhirOrderStatus());
        }

        if (auditUpdate.getWarnings() != null && !auditUpdate.getWarnings().isEmpty()) {
            List<String> newWarnings = auditUpdate.getWarnings();
            List<String> existingWarnings = existingAudit.getWarnings();
            existingWarnings.addAll(newWarnings);
            existingAudit.setWarnings(existingWarnings);
        }

        neo4jFilesTemplate.saveAs(existingAudit, UpdateFhirOrderAuditStub.class);
    }
}