package au.com.healius.digital.collectorportal.service;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.source.ByteArrayOutputStream;
import com.itextpdf.kernel.pdf.PdfWriter;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * Service class responsible for generating PDF documents from HTML content.
 * This service uses the iText library to convert HTML to PDF format with proper font handling.
 */
@Service
@Log4j2
public class DocumentGenerator {

    /**
     * Converts HTML content to a PDF document.
     * The conversion process includes proper font handling and error logging.
     *
     * @param processedHtml The HTML content to be converted to PDF
     * @return A byte array containing the generated PDF document
     * @throws Exception if there is an error during PDF generation
     */
    public byte[] htmlToPdf(String processedHtml) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        try (PdfWriter pdfwriter = new PdfWriter(byteArrayOutputStream)) {
            DefaultFontProvider defaultFont = new DefaultFontProvider(false, true, false);

            ConverterProperties converterProperties = new ConverterProperties();
            converterProperties.setFontProvider(defaultFont);

            HtmlConverter.convertToPdf(processedHtml, pdfwriter, converterProperties);
        } catch (Exception ex) {
            log.error("Error generating pdf", ex);
        }

        return byteArrayOutputStream.toByteArray();
    }
}