package au.com.healius.digital.collectorportal.dto;

import com.google.common.collect.ComparisonChain;
import lombok.Data;

import java.io.Serializable;

@Data
public class ContainerCollectedDTO implements Serializable, Comparable<ContainerCollectedDTO>{
    private String id;
    private String name;
    private String summaryLabel;
    private String containerId;
    private String textColourCode;
    private String fillColourCode;
    private String borderColourCode;
    private Integer sequence;
    private Integer count;

    @Override
    public int compareTo(ContainerCollectedDTO o) {
        return ComparisonChain.start()
                .compare(sequence, o.getSequence())
                .result();
    }
} 