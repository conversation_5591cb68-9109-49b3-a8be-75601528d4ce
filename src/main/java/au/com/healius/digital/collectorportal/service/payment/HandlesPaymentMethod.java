package au.com.healius.digital.collectorportal.service.payment;

import au.com.healius.digital.librarycollections.domain.enumeration.PaymentMethod;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation used to mark methods that handle specific payment methods.
 * This annotation is used to associate payment processing methods with specific payment method types.
 * 
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HandlesPaymentMethod {
    /**
     * Specifies the payment method that the annotated method handles.
     *
     * @return The payment method enum value
     */
    PaymentMethod value();
}
