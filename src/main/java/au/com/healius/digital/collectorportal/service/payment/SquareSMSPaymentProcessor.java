package au.com.healius.digital.collectorportal.service.payment;

import au.com.healius.digital.collectorportal.dto.CCSummary;
import au.com.healius.digital.collectorportal.dto.SMSTemplate;
import au.com.healius.digital.collectorportal.service.PulsarService;
import au.com.healius.digital.collectorportal.service.SquareSMSParser;
import au.com.healius.digital.collectorportal.service.SquareUpService;
import au.com.healius.digital.collectorportal.service.paymentcalculator.PaymentException;
import au.com.healius.digital.collectorportal.util.LogCall;
import au.com.healius.digital.librarycollections.domain.Order;
import au.com.healius.digital.librarycollections.domain.SquarePayment;
import au.com.healius.digital.librarycollections.domain.enumeration.PaymentProvider;
import au.com.healius.digital.librarycollections.domain.enumeration.PaymentState;
import com.squareup.square.exceptions.ApiException;
import com.squareup.square.models.Payment;
import com.squareup.square.models.PaymentLink;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

import static au.com.healius.digital.librarycollections.domain.enumeration.PaymentMethod.SMS;
import static au.com.healius.digital.librarycollections.domain.enumeration.PaymentProvider.SQUARE;

/**
 * Implementation of PaymentProcessor for handling Square SMS payments.
 * This processor manages the lifecycle of payments made through SMS payment links,
 * including starting, finalizing, and canceling payments.
 *
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.0
 */
@Component
@RequiredArgsConstructor
@With
@Log4j2
public class SquareSMSPaymentProcessor implements PaymentProcessor {
    private final SquareUpService squareService;
    private final PulsarService pulsarService;
    private final PaymentAuditService paymentAuditService;

    private final String mobileNumber;

    /**
     * Retrieves the current status of a Square SMS payment.
     *
     * @param squarePayment The Square payment object to check status for
     * @return The current status of the payment (CANCELED, IN_PROGRESS, or COMPLETED)
     * @throws PaymentException If there is an error retrieving the payment status
     */
    @Override
    @HandlesPaymentMethod(SMS)
    @LogCall(includeReturn = true)
    public String getStatus(SquarePayment squarePayment) throws PaymentException {
        try {
            String orderId = squarePayment.getSquareOrderId();
            log.debug("Link Status for {}", orderId);
            if (orderId == null) {
                return null;
            }

            String status = squareService.getPaymentLinkStatus(orderId);
            if (status.equals("OPEN")) {
                Payment payment = squareService.getPaymentLinkComplete(squarePayment.getSquareOrderId());
                if (payment != null && "COMPLETED".equals(payment.getStatus())) {
                    return STATUS_COMPLETED;
                }
            } else if (status.equals("CANCELED")) {
                return STATUS_CANCELED;
            }
            return STATUS_IN_PROGRESS;
        } catch (ApiException | IOException ex) {
            throw new PaymentException("Error getting payment link status", ex);
        }
    }

    /**
     * Gets the payment provider associated with this processor.
     *
     * @return SQUARE as the payment provider
     */
    @Override
    public PaymentProvider getPaymentProvider() {
        return SQUARE;
    }

    /**
     * Initiates a new payment process through SMS payment link.
     * Creates a payment link and sends it via SMS to the specified mobile number.
     *
     * @param cc The credit card summary details
     * @param order The order associated with the payment
     * @param squarePayment The Square payment object to initialize
     * @param deviceId The ID of the device (not used in SMS payments)
     * @return The initialized Square payment object with payment link details
     * @throws PaymentException If there is an error starting the payment or sending the SMS
     */
    @Override
    @LogCall
    public SquarePayment startPayment(CCSummary cc, Order order, SquarePayment squarePayment, String deviceId) throws PaymentException {
        String company = order.getCompany().getShortName();
        PaymentLink link = squareService.sendPaymentLink(
                squarePayment.getPaymentAmount(),
                UUID.randomUUID().toString(),
                company,
                squarePayment.getReceiptNumber());

        paymentAuditService.auditLink(order.getId(), "PaymentLink", link.getId(), link.getOrderId());

        squarePayment.setPaymentMethod(SMS);
        squarePayment.setSquareCheckoutId(link.getId());
        squarePayment.setSquareOrderId(link.getOrderId());

        String msg = SquareSMSParser.generateSMS(link.getUrl(), company);
        try {
            pulsarService.sendSquare(SMSTemplate.builder()
                    .businessUnit(order.getCompany().getCode())
                    .mobile(mobileNumber)
                    .msg(msg)
                    .build());
        } catch (IOException ex) {
            throw new PaymentException("Error sending payment link to pulsar", ex);
        }

        return squarePayment;
    }

    /**
     * Completes a Square SMS payment process.
     *
     * @param squarePayment The Square payment object to finalize
     * @return The finalized Square payment object with payment details
     * @throws PaymentException If there is an error finalizing the payment
     */
    @Override
    @HandlesPaymentMethod(SMS)
    @LogCall
    public SquarePayment finalisePayment(SquarePayment squarePayment) throws PaymentException {
        try {
            String status = squareService.getPaymentLinkStatus(squarePayment.getSquareOrderId());
            if (status.equals("OPEN")) {
                Payment payment = squareService.getPaymentLinkComplete(squarePayment.getSquareOrderId());
                if (payment != null && "COMPLETED".equals(payment.getStatus())) {

                    paymentAuditService.auditPayment(squarePayment.getId(), SMS, "PaymentLinkComplete", payment);

                    squarePayment.setSquarePaymentId(payment.getId());
                    squarePayment.setSquareReceiptNumber(payment.getReceiptNumber());
                }
            }
        } catch (ApiException | IOException ex) {
            throw new PaymentException("Error completing link payment", ex);
        }
        return squarePayment;
    }

    /**
     * Cancels a Square SMS payment process.
     *
     * @param orderId The ID of the order associated with the payment
     * @param squareId The Square payment ID
     * @throws PaymentException If there is an error canceling the payment
     */
    @Override
    @LogCall
    public void cancelPayment(String orderId, String squareId) throws PaymentException {
        try {
            if (squareId != null) {
                String squareOrderId = squareService.getPaymentLinkCancel(squareId);
                paymentAuditService.auditLink(orderId, "PaymentLinkCancel", squareId, squareOrderId);
            }
        } catch (ApiException | IOException ex) {
            throw new PaymentException("Error cancelling link", ex);
        }
    }

    /**
     * Finalizes the cancellation of a Square SMS payment.
     *
     * @param squarePayment The Square payment object to finalize cancellation for
     * @return The Square payment object with updated cancellation status
     */
    @Override
    @HandlesPaymentMethod(SMS)
    @LogCall
    public SquarePayment finaliseCancel(SquarePayment squarePayment) {
        squarePayment.setPaymentState(PaymentState.CANCELED);
        return squarePayment;
    }

}
