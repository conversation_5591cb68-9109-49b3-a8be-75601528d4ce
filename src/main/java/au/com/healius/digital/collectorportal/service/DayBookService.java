package au.com.healius.digital.collectorportal.service;

import au.com.healius.digital.collectorportal.converter.ContainerCollectedListToCollectedContainersConverter;
import au.com.healius.digital.collectorportal.dto.CollectedContainers;
import au.com.healius.digital.collectorportal.dto.DayBookItem;
import au.com.healius.digital.collectorportal.dto.LabNumberView;
import au.com.healius.digital.collectorportal.dto.VisitLabNumbers;
import au.com.healius.digital.librarycollections.domain.ContainerCollected;
import au.com.healius.digital.librarycollections.domain.LabNumberAudit;
import au.com.healius.digital.librarycollections.domain.Order;
import au.com.healius.digital.librarycollections.domain.ReferredPatient;
import au.com.healius.digital.librarycollections.domain.Visit;
import au.com.healius.digital.librarycollections.domain.enumeration.LabNumberAuditType;
import au.com.healius.digital.librarycollections.repository.ContainerCollectedRepository;
import au.com.healius.digital.librarycollections.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.convert.ConversionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service class responsible for managing day book operations in the collector portal.
 * A day book is a record of all collections and visits for a specific day.
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class DayBookService {
    private final CollectionVisitService collectionVisitService;
    private final OrderRepository orderRepository;
    private final ConversionService conversionService;
    private final ContainerCollectedRepository containerCollectedRepository;
    private final ContainerCollectedListToCollectedContainersConverter containerCollectedListToCollectedContainersConverter;

    /**
     * Retrieves day book items for a specific account and day.
     *
     * @param accCombinedId The combined account identifier
     * @param day The specific day to retrieve day book items for
     * @return A list of day book items sorted by their natural order
     */
    @Transactional(readOnly = true)
    public List<DayBookItem> getDayBook(String accCombinedId, ZonedDateTime day) {

        log.info("Find day book items");
        ZonedDateTime start = day
                .withHour(0)
                .withMinute(0)
                .withSecond(0);

        ZonedDateTime end = start
                .plusDays(1)
                .minusMinutes(1);

        return orderRepository.findOrdersForDayBook(accCombinedId, start, end).stream()
                .flatMap(this::toDayBookItems)
                .sorted()
                .toList();
    }

    @Transactional(readOnly = true)
    public List<DayBookItem> getDayBookByPatientHealiusId(String accCombinedId, String patientHealiusId) {

        log.info("Find day book items for patient {}", patientHealiusId);

        return orderRepository.findOrdersForDayBookByPatientHeliusId(accCombinedId, patientHealiusId).stream()
                .flatMap(this::toDayBookItems)
                .sorted()
                .toList();
    }

    /**
     * Converts an Order into a stream of DayBookItems.
     * Each visit in the order is mapped to a separate day book item.
     *
     * @param order The order to convert
     * @return A stream of day book items
     */
    private Stream<DayBookItem> toDayBookItems(Order order) {
        return order.getVisits().stream()
                .map(visit -> toDayBookItem(order, visit));
    }

    /**
     * Converts an Order and its associated Visit into a DayBookItem.
     * This method populates all relevant information including patient details,
     * lab numbers, payment information, and collection status.
     *
     * @param order The order containing the visit
     * @param visit The specific visit to convert
     * @return A populated DayBookItem
     */
    private DayBookItem toDayBookItem(Order order, Visit visit) {
        Optional<LabNumberView> labNumber = conversionService.convert(visit, VisitLabNumbers.class)
                .getPrimaryLabNumber();
        DayBookItem dayBookItem = new DayBookItem();
        dayBookItem.setId(order.getId());
        dayBookItem.setLabNumber(labNumber.map(LabNumberView::getLabNumber).orElse(null));
        dayBookItem.setLabNumberPlusYear(labNumber.map(LabNumberView::getLabNumberPlusYear).orElse(null));
        dayBookItem.setHealiusId(order.getHealiusId());
        dayBookItem.setPathologyOrderWorkflow(order.getPathologyOrderWorkflow());
        dayBookItem.setVisit(visit);

        dayBookItem.setRequestType(order.getRequestType());

        dayBookItem.setSquarePayment(visit.getSquarePayment());
        dayBookItem.setBPointPayments(Collections.singletonList(visit.getBPointPayment()));

        List<ContainerCollected> domainContainers = containerCollectedRepository.findByVisitId(visit.getId());

        // Use the new converter
        CollectedContainers collectedContainers = containerCollectedListToCollectedContainersConverter.convert(
                new ContainerCollectedListToCollectedContainersConverter.Input(
                        domainContainers,
                        visit.getUnknownContainers()
                )
        );

        dayBookItem.setCollected(collectedContainers);

        String freehand = visit.getFreeText();
        if (StringUtils.hasText(freehand)) {
            dayBookItem.setFreehand(freehand);
        }

        dayBookItem.setCollectedTests(Optional.ofNullable(collectionVisitService.getCollectedString(visit))
                .orElse("Please refer to paper referral for tests"));
        dayBookItem.setToFollow(Optional.ofNullable(collectionVisitService.getToFollowString(visit)).orElse(""));
        dayBookItem.setUnableToCollect(Optional.ofNullable(collectionVisitService.getUnableToCollect(visit)).orElse(""));
        dayBookItem.setDeclinedToPay(Optional.ofNullable(collectionVisitService.getDeclineToPayString(visit)).orElse(""));
        dayBookItem.setDeclinedToCollect(Optional.ofNullable(collectionVisitService.getDeclineToCollectString(visit)).orElse(""));
        dayBookItem.setTestUnavailable(Optional.ofNullable(collectionVisitService.getNotAvailableString(visit)).orElse(""));

        Optional<ReferredPatient> patient = Optional.ofNullable(order.getReferredPatient());
        Optional<ReferredPatient> referredPatient = Optional.ofNullable(order.getReferredPatient());

        dayBookItem.setFamilyName(patient
                .map(ReferredPatient::getFamilyName)
                .orElseGet(() -> referredPatient
                        .map(ReferredPatient::getFamilyName)
                        .orElse("")));

        Optional<String> givenName = patient.isPresent()
                ? patient.map(ReferredPatient::getGivenName)
                : referredPatient.map(ReferredPatient::getGivenName);

        dayBookItem.setGivenName(givenName
                .orElse("-"));

        dayBookItem.setDateOfBirth(patient.map(ReferredPatient::getDateOfBirth).orElse(null));
        dayBookItem.setReferrer(order.getReferrer());
        dayBookItem.setCopyTo(order.getCopyTo());
        dayBookItem.setAuditLoggedInString(getAuditEmployeeNames(labNumber, LabNumberAuditType.LOGGED_IN));
        dayBookItem.setAuditPdbCollectorString(getAuditEmployeeNames(labNumber, LabNumberAuditType.PDB_COLLECTOR));
        dayBookItem.setAuditSubmittedString(getAuditEmployeeNames(labNumber, LabNumberAuditType.SUBMITTED));
        return dayBookItem;
    }

    private String getAuditEmployeeNames(Optional<LabNumberView> labNumber, LabNumberAuditType auditType) {
        return labNumber.map(labNumberView -> labNumberView.getLabNumberAudits()
                .stream()
                .filter(audit -> Objects.isNull(audit))
                .filter(audit -> audit.getAuditType().equals(auditType))
                .map(LabNumberAudit::getEmployeeName)
                .collect(Collectors.joining(", ")))
                .orElse("");
    }

}
