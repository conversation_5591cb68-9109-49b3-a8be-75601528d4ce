package au.com.healius.digital.collectorportal.dto;

import au.com.healius.digital.collectorportal.domain.projection.OrderStub;
import au.com.healius.digital.librarycollections.domain.Visit;

import java.util.Optional;

/**
 * Data transfer object representing the result of processing a visit.
 * This record encapsulates the order information and related visits, including
 * the first visit, current visit, and an optional follow-up visit.
 *
 * @param order The order stub containing basic order information
 * @param firstVisit The first visit associated with the order
 * @param visit The current visit being processed
 * @param followVisit An optional follow-up visit, if applicable
 */
public record ProcessVisitResult(OrderStub order, Visit firstVisit, Visit visit, Optional<Visit> followVisit, String healiusProfileId) {
}
