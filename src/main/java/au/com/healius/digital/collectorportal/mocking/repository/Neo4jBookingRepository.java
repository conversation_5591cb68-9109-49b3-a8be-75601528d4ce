package au.com.healius.digital.collectorportal.mocking.repository;

import au.com.healius.digital.collectorportal.mocking.entity.Neo4jBooking;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface Neo4jBookingRepository extends Neo4jRepository<Neo4jBooking, String> {
    Optional<Neo4jBooking> findByBookingNumber(String bookingNumber);
    
    @Query("MATCH (b:MockBooking)-[rel]-(a:MockAttachment) WHERE b.locationId = $locationId AND b.date = $date RETURN distinct b,a,rel")
    List<Neo4jBooking> findByLocationIdAndDate(UUID locationId, LocalDate date);
    
    @Query("MATCH (b:MockBooking) WHERE b.date = $date RETURN b")
    List<Neo4jBooking> findByDate(LocalDate date);
    
    @Query("MATCH (b:MockBooking) WHERE b.date >= $startDate AND b.date <= $endDate RETURN b")
    List<Neo4jBooking> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    @Query("MATCH (b:MockBooking) WHERE b.locationId = $locationId RETURN b")
    List<Neo4jBooking> findByLocationId(UUID locationId);
    
    @Query("MATCH (b:MockBooking) WHERE b.reminderSent = $reminderSent RETURN b")
    List<Neo4jBooking> findByReminderSent(Boolean reminderSent);
    
    @Query("MATCH (b:MockBooking) WHERE b.cancelled = $cancelled RETURN b")
    List<Neo4jBooking> findByCancelled(Boolean cancelled);
    
    @Query("MATCH (b:MockBooking) WHERE b.completed = $completed RETURN b")
    List<Neo4jBooking> findByCompleted(Boolean completed);
    
    List<Neo4jBooking> findByPatientEmail(String patientEmail);
    
    @Query("MATCH (b:MockBooking)-[:HAS_ATTACHMENT]->(a:MockAttachment) WHERE b.id = $bookingId RETURN b")
    Optional<Neo4jBooking> findByIdWithAttachments(String bookingId);
} 