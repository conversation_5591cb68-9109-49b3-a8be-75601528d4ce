package au.com.healius.digital.collectorportal.mocking.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Getter
@Setter
@NoArgsConstructor
@Node("MockAttachment")
public class Neo4jAttachment {
    @Id
    private String id;
    private String fileName;
    private String fileType;
    private String uploadedBy;
    private byte[] fileData;

} 