/*
 * SVC Booking API
 * Healius Booking api
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package au.com.healius.digital.collectorportal.mocking.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * V1RestBooking
 */
@JsonPropertyOrder({
  V1RestBooking.JSON_PROPERTY_ID,
  V1RestBooking.JSON_PROPERTY_BOOKING_NUMBER,
  V1RestBooking.JSON_PROPERTY_HEALIUS_ID,
  V1RestBooking.JSON_PROPERTY_EREFERRAL_HEALIUS_ID,
  V1RestBooking.JSON_PROPERTY_LOCATION_ID,
  V1RestBooking.JSON_PROPERTY_CREATED,
  V1RestBooking.JSON_PROPERTY_UPDATE,
  V1RestBooking.JSON_PROPERTY_TEST_ID,
  V1RestBooking.JSON_PROPERTY_CUSTOMER_TEST_NAME,
  V1RestBooking.JSON_PROPERTY_CREATED_BY,
  V1RestBooking.JSON_PROPERTY_DATE,
  V1RestBooking.JSON_PROPERTY_FROM_TIME,
  V1RestBooking.JSON_PROPERTY_TO_TIME,
  V1RestBooking.JSON_PROPERTY_RESERVATION_EXPIRY,
  V1RestBooking.JSON_PROPERTY_REMINDER_SENT,
  V1RestBooking.JSON_PROPERTY_ADDITIONAL_CONTACT,
  V1RestBooking.JSON_PROPERTY_PATIENT_GIVEN_NAME,
  V1RestBooking.JSON_PROPERTY_PATIENT_FAMILY_NAME,
  V1RestBooking.JSON_PROPERTY_PATIENT_EMAIL,
  V1RestBooking.JSON_PROPERTY_PATIENT_PHONE,
  V1RestBooking.JSON_PROPERTY_CONTACT_GIVEN_NAME,
  V1RestBooking.JSON_PROPERTY_CONTACT_FAMILY_NAME,
  V1RestBooking.JSON_PROPERTY_CONTACT_EMAIL,
  V1RestBooking.JSON_PROPERTY_CONTACT_PHONE,
  V1RestBooking.JSON_PROPERTY_NOTIFICATION_PREFERENCE,
  V1RestBooking.JSON_PROPERTY_PATIENT_S_M_S_NOTIFY,
  V1RestBooking.JSON_PROPERTY_PATIENT_EMAIL_NOTIFY,
  V1RestBooking.JSON_PROPERTY_CONTACT_S_M_S_NOTIFY,
  V1RestBooking.JSON_PROPERTY_CONTACT_EMAIL_NOTIFY,
  V1RestBooking.JSON_PROPERTY_COMMENT,
  V1RestBooking.JSON_PROPERTY_CANCELLED,
  V1RestBooking.JSON_PROPERTY_CANCEL_REASON,
  V1RestBooking.JSON_PROPERTY_DATE_CANCELLED,
  V1RestBooking.JSON_PROPERTY_COMPLETED,
  V1RestBooking.JSON_PROPERTY_NOT_COMPLETED_REASON,
  V1RestBooking.JSON_PROPERTY_ATTACHMENTS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-19T20:15:18.693725+10:00[Australia/Sydney]", comments = "Generator version: 7.10.0")
public class V1RestBooking {
  public static final String JSON_PROPERTY_ID = "id";
  @jakarta.annotation.Nullable
  private String id;

  public static final String JSON_PROPERTY_BOOKING_NUMBER = "bookingNumber";
  @jakarta.annotation.Nullable
  private String bookingNumber;

  public static final String JSON_PROPERTY_HEALIUS_ID = "healiusId";
  @jakarta.annotation.Nullable
  private String healiusId;

  public static final String JSON_PROPERTY_EREFERRAL_HEALIUS_ID = "ereferralHealiusId";
  @jakarta.annotation.Nullable
  private String ereferralHealiusId;

  public static final String JSON_PROPERTY_LOCATION_ID = "locationId";
  @jakarta.annotation.Nullable
  private UUID locationId;

  public static final String JSON_PROPERTY_CREATED = "created";
  @jakarta.annotation.Nullable
  private OffsetDateTime created;

  public static final String JSON_PROPERTY_UPDATE = "update";
  @jakarta.annotation.Nullable
  private OffsetDateTime update;

  public static final String JSON_PROPERTY_TEST_ID = "testId";
  @jakarta.annotation.Nullable
  private UUID testId;

  public static final String JSON_PROPERTY_CUSTOMER_TEST_NAME = "customerTestName";
  @jakarta.annotation.Nullable
  private String customerTestName;

  public static final String JSON_PROPERTY_CREATED_BY = "createdBy";
  @jakarta.annotation.Nullable
  private String createdBy;

  public static final String JSON_PROPERTY_DATE = "date";
  @jakarta.annotation.Nullable
  private LocalDate date;

  public static final String JSON_PROPERTY_FROM_TIME = "fromTime";
  @jakarta.annotation.Nullable
  private String fromTime;

  public static final String JSON_PROPERTY_TO_TIME = "toTime";
  @jakarta.annotation.Nullable
  private String toTime;

  public static final String JSON_PROPERTY_RESERVATION_EXPIRY = "reservationExpiry";
  @jakarta.annotation.Nullable
  private Long reservationExpiry;

  public static final String JSON_PROPERTY_REMINDER_SENT = "reminderSent";
  @jakarta.annotation.Nullable
  private Boolean reminderSent;

  public static final String JSON_PROPERTY_ADDITIONAL_CONTACT = "additionalContact";
  @jakarta.annotation.Nullable
  private Boolean additionalContact;

  public static final String JSON_PROPERTY_PATIENT_GIVEN_NAME = "patientGivenName";
  @jakarta.annotation.Nullable
  private String patientGivenName;

  public static final String JSON_PROPERTY_PATIENT_FAMILY_NAME = "patientFamilyName";
  @jakarta.annotation.Nullable
  private String patientFamilyName;

  public static final String JSON_PROPERTY_PATIENT_EMAIL = "patientEmail";
  @jakarta.annotation.Nullable
  private String patientEmail;

  public static final String JSON_PROPERTY_PATIENT_PHONE = "patientPhone";
  @jakarta.annotation.Nullable
  private String patientPhone;

  public static final String JSON_PROPERTY_CONTACT_GIVEN_NAME = "contactGivenName";
  @jakarta.annotation.Nullable
  private String contactGivenName;

  public static final String JSON_PROPERTY_CONTACT_FAMILY_NAME = "contactFamilyName";
  @jakarta.annotation.Nullable
  private String contactFamilyName;

  public static final String JSON_PROPERTY_CONTACT_EMAIL = "contactEmail";
  @jakarta.annotation.Nullable
  private String contactEmail;

  public static final String JSON_PROPERTY_CONTACT_PHONE = "contactPhone";
  @jakarta.annotation.Nullable
  private String contactPhone;

  public static final String JSON_PROPERTY_NOTIFICATION_PREFERENCE = "NotificationPreference";
  @jakarta.annotation.Nullable
  private RestNotificationPreference notificationPreference;

  public static final String JSON_PROPERTY_PATIENT_S_M_S_NOTIFY = "patientSMSNotify";
  @jakarta.annotation.Nullable
  private Boolean patientSMSNotify;

  public static final String JSON_PROPERTY_PATIENT_EMAIL_NOTIFY = "patientEmailNotify";
  @jakarta.annotation.Nullable
  private Boolean patientEmailNotify;

  public static final String JSON_PROPERTY_CONTACT_S_M_S_NOTIFY = "contactSMSNotify";
  @jakarta.annotation.Nullable
  private Boolean contactSMSNotify;

  public static final String JSON_PROPERTY_CONTACT_EMAIL_NOTIFY = "contactEmailNotify";
  @jakarta.annotation.Nullable
  private Boolean contactEmailNotify;

  public static final String JSON_PROPERTY_COMMENT = "comment";
  @jakarta.annotation.Nullable
  private String comment;

  public static final String JSON_PROPERTY_CANCELLED = "cancelled";
  @jakarta.annotation.Nullable
  private Boolean cancelled;

  public static final String JSON_PROPERTY_CANCEL_REASON = "cancelReason";
  @jakarta.annotation.Nullable
  private V1RestCancelReason cancelReason;

  public static final String JSON_PROPERTY_DATE_CANCELLED = "dateCancelled";
  @jakarta.annotation.Nullable
  private OffsetDateTime dateCancelled;

  public static final String JSON_PROPERTY_COMPLETED = "completed";
  @jakarta.annotation.Nullable
  private Boolean completed;

  public static final String JSON_PROPERTY_NOT_COMPLETED_REASON = "notCompletedReason";
  @jakarta.annotation.Nullable
  private String notCompletedReason;

  public static final String JSON_PROPERTY_ATTACHMENTS = "attachments";
  @jakarta.annotation.Nullable
  private List<RestAttachmentInfo> attachments = new ArrayList<>();

  public V1RestBooking() {
  }

  public V1RestBooking id(@jakarta.annotation.Nullable String id) {
    
    this.id = id;
    return this;
  }

  /**
   * booking UUID
   * @return id
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setId(@jakarta.annotation.Nullable String id) {
    this.id = id;
  }

  public V1RestBooking bookingNumber(@jakarta.annotation.Nullable String bookingNumber) {
    
    this.bookingNumber = bookingNumber;
    return this;
  }

  /**
   * booking number
   * @return bookingNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BOOKING_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBookingNumber() {
    return bookingNumber;
  }


  @JsonProperty(JSON_PROPERTY_BOOKING_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBookingNumber(@jakarta.annotation.Nullable String bookingNumber) {
    this.bookingNumber = bookingNumber;
  }

  public V1RestBooking healiusId(@jakarta.annotation.Nullable String healiusId) {
    
    this.healiusId = healiusId;
    return this;
  }

  /**
   * Get healiusId
   * @return healiusId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HEALIUS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHealiusId() {
    return healiusId;
  }


  @JsonProperty(JSON_PROPERTY_HEALIUS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHealiusId(@jakarta.annotation.Nullable String healiusId) {
    this.healiusId = healiusId;
  }

  public V1RestBooking ereferralHealiusId(@jakarta.annotation.Nullable String ereferralHealiusId) {
    
    this.ereferralHealiusId = ereferralHealiusId;
    return this;
  }

  /**
   * Get ereferralHealiusId
   * @return ereferralHealiusId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EREFERRAL_HEALIUS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEreferralHealiusId() {
    return ereferralHealiusId;
  }


  @JsonProperty(JSON_PROPERTY_EREFERRAL_HEALIUS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEreferralHealiusId(@jakarta.annotation.Nullable String ereferralHealiusId) {
    this.ereferralHealiusId = ereferralHealiusId;
  }

  public V1RestBooking locationId(@jakarta.annotation.Nullable UUID locationId) {
    
    this.locationId = locationId;
    return this;
  }

  /**
   * this is actual location ccid, as in the string e.g \&quot;KVSP\&quot;, not a uuid, little confusing
   * @return locationId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOCATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public UUID getLocationId() {
    return locationId;
  }


  @JsonProperty(JSON_PROPERTY_LOCATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLocationId(@jakarta.annotation.Nullable UUID locationId) {
    this.locationId = locationId;
  }

  public V1RestBooking created(@jakarta.annotation.Nullable OffsetDateTime created) {
    
    this.created = created;
    return this;
  }

  /**
   * booking created time
   * @return created
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OffsetDateTime getCreated() {
    return created;
  }


  @JsonProperty(JSON_PROPERTY_CREATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreated(@jakarta.annotation.Nullable OffsetDateTime created) {
    this.created = created;
  }

  public V1RestBooking update(@jakarta.annotation.Nullable OffsetDateTime update) {
    
    this.update = update;
    return this;
  }

  /**
   * booking updated time
   * @return update
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UPDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OffsetDateTime getUpdate() {
    return update;
  }


  @JsonProperty(JSON_PROPERTY_UPDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUpdate(@jakarta.annotation.Nullable OffsetDateTime update) {
    this.update = update;
  }

  public V1RestBooking testId(@jakarta.annotation.Nullable UUID testId) {
    
    this.testId = testId;
    return this;
  }

  /**
   * foreign key of test in findus database if selected from there
   * @return testId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TEST_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public UUID getTestId() {
    return testId;
  }


  @JsonProperty(JSON_PROPERTY_TEST_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTestId(@jakarta.annotation.Nullable UUID testId) {
    this.testId = testId;
  }

  public V1RestBooking customerTestName(@jakarta.annotation.Nullable String customerTestName) {
    
    this.customerTestName = customerTestName;
    return this;
  }

  /**
   * either the custom text for test name or the friendly name for the test from findus
   * @return customerTestName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER_TEST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCustomerTestName() {
    return customerTestName;
  }


  @JsonProperty(JSON_PROPERTY_CUSTOMER_TEST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCustomerTestName(@jakarta.annotation.Nullable String customerTestName) {
    this.customerTestName = customerTestName;
  }

  public V1RestBooking createdBy(@jakarta.annotation.Nullable String createdBy) {
    
    this.createdBy = createdBy;
    return this;
  }

  /**
   * name of the booking creator
   * @return createdBy
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCreatedBy() {
    return createdBy;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedBy(@jakarta.annotation.Nullable String createdBy) {
    this.createdBy = createdBy;
  }

  public V1RestBooking date(@jakarta.annotation.Nullable LocalDate date) {
    
    this.date = date;
    return this;
  }

  /**
   * booking date
   * @return date
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDate() {
    return date;
  }


  @JsonProperty(JSON_PROPERTY_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDate(@jakarta.annotation.Nullable LocalDate date) {
    this.date = date;
  }

  public V1RestBooking fromTime(@jakarta.annotation.Nullable String fromTime) {
    
    this.fromTime = fromTime;
    return this;
  }

  /**
   * booking start time
   * @return fromTime
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FROM_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFromTime() {
    return fromTime;
  }


  @JsonProperty(JSON_PROPERTY_FROM_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFromTime(@jakarta.annotation.Nullable String fromTime) {
    this.fromTime = fromTime;
  }

  public V1RestBooking toTime(@jakarta.annotation.Nullable String toTime) {
    
    this.toTime = toTime;
    return this;
  }

  /**
   * booking end time
   * @return toTime
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TO_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getToTime() {
    return toTime;
  }


  @JsonProperty(JSON_PROPERTY_TO_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setToTime(@jakarta.annotation.Nullable String toTime) {
    this.toTime = toTime;
  }

  public V1RestBooking reservationExpiry(@jakarta.annotation.Nullable Long reservationExpiry) {
    
    this.reservationExpiry = reservationExpiry;
    return this;
  }

  /**
   * unix epoch time _seconds_ for when reservation will expire
   * minimum: 1
   * @return reservationExpiry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESERVATION_EXPIRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getReservationExpiry() {
    return reservationExpiry;
  }


  @JsonProperty(JSON_PROPERTY_RESERVATION_EXPIRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReservationExpiry(@jakarta.annotation.Nullable Long reservationExpiry) {
    this.reservationExpiry = reservationExpiry;
  }

  public V1RestBooking reminderSent(@jakarta.annotation.Nullable Boolean reminderSent) {
    
    this.reminderSent = reminderSent;
    return this;
  }

  /**
   * if a reminder has sent
   * @return reminderSent
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMINDER_SENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReminderSent() {
    return reminderSent;
  }


  @JsonProperty(JSON_PROPERTY_REMINDER_SENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReminderSent(@jakarta.annotation.Nullable Boolean reminderSent) {
    this.reminderSent = reminderSent;
  }

  public V1RestBooking additionalContact(@jakarta.annotation.Nullable Boolean additionalContact) {
    
    this.additionalContact = additionalContact;
    return this;
  }

  /**
   * do we have an additional contact
   * @return additionalContact
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDITIONAL_CONTACT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAdditionalContact() {
    return additionalContact;
  }


  @JsonProperty(JSON_PROPERTY_ADDITIONAL_CONTACT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAdditionalContact(@jakarta.annotation.Nullable Boolean additionalContact) {
    this.additionalContact = additionalContact;
  }

  public V1RestBooking patientGivenName(@jakarta.annotation.Nullable String patientGivenName) {
    
    this.patientGivenName = patientGivenName;
    return this;
  }

  /**
   * patient given name
   * @return patientGivenName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PATIENT_GIVEN_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPatientGivenName() {
    return patientGivenName;
  }


  @JsonProperty(JSON_PROPERTY_PATIENT_GIVEN_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPatientGivenName(@jakarta.annotation.Nullable String patientGivenName) {
    this.patientGivenName = patientGivenName;
  }

  public V1RestBooking patientFamilyName(@jakarta.annotation.Nullable String patientFamilyName) {
    
    this.patientFamilyName = patientFamilyName;
    return this;
  }

  /**
   * patient family name
   * @return patientFamilyName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PATIENT_FAMILY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPatientFamilyName() {
    return patientFamilyName;
  }


  @JsonProperty(JSON_PROPERTY_PATIENT_FAMILY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPatientFamilyName(@jakarta.annotation.Nullable String patientFamilyName) {
    this.patientFamilyName = patientFamilyName;
  }

  public V1RestBooking patientEmail(@jakarta.annotation.Nullable String patientEmail) {
    
    this.patientEmail = patientEmail;
    return this;
  }

  /**
   * patient email
   * @return patientEmail
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PATIENT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPatientEmail() {
    return patientEmail;
  }


  @JsonProperty(JSON_PROPERTY_PATIENT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPatientEmail(@jakarta.annotation.Nullable String patientEmail) {
    this.patientEmail = patientEmail;
  }

  public V1RestBooking patientPhone(@jakarta.annotation.Nullable String patientPhone) {
    
    this.patientPhone = patientPhone;
    return this;
  }

  /**
   * patient phone
   * @return patientPhone
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PATIENT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPatientPhone() {
    return patientPhone;
  }


  @JsonProperty(JSON_PROPERTY_PATIENT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPatientPhone(@jakarta.annotation.Nullable String patientPhone) {
    this.patientPhone = patientPhone;
  }

  public V1RestBooking contactGivenName(@jakarta.annotation.Nullable String contactGivenName) {
    
    this.contactGivenName = contactGivenName;
    return this;
  }

  /**
   * contact given name
   * @return contactGivenName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_GIVEN_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContactGivenName() {
    return contactGivenName;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_GIVEN_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactGivenName(@jakarta.annotation.Nullable String contactGivenName) {
    this.contactGivenName = contactGivenName;
  }

  public V1RestBooking contactFamilyName(@jakarta.annotation.Nullable String contactFamilyName) {
    
    this.contactFamilyName = contactFamilyName;
    return this;
  }

  /**
   * contact family name
   * @return contactFamilyName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_FAMILY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContactFamilyName() {
    return contactFamilyName;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_FAMILY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactFamilyName(@jakarta.annotation.Nullable String contactFamilyName) {
    this.contactFamilyName = contactFamilyName;
  }

  public V1RestBooking contactEmail(@jakarta.annotation.Nullable String contactEmail) {
    
    this.contactEmail = contactEmail;
    return this;
  }

  /**
   * contact email
   * @return contactEmail
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContactEmail() {
    return contactEmail;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactEmail(@jakarta.annotation.Nullable String contactEmail) {
    this.contactEmail = contactEmail;
  }

  public V1RestBooking contactPhone(@jakarta.annotation.Nullable String contactPhone) {
    
    this.contactPhone = contactPhone;
    return this;
  }

  /**
   * contact phone
   * @return contactPhone
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContactPhone() {
    return contactPhone;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactPhone(@jakarta.annotation.Nullable String contactPhone) {
    this.contactPhone = contactPhone;
  }

  public V1RestBooking notificationPreference(@jakarta.annotation.Nullable RestNotificationPreference notificationPreference) {
    
    this.notificationPreference = notificationPreference;
    return this;
  }

  /**
   * Get notificationPreference
   * @return notificationPreference
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NOTIFICATION_PREFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public RestNotificationPreference getNotificationPreference() {
    return notificationPreference;
  }


  @JsonProperty(JSON_PROPERTY_NOTIFICATION_PREFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNotificationPreference(@jakarta.annotation.Nullable RestNotificationPreference notificationPreference) {
    this.notificationPreference = notificationPreference;
  }

  public V1RestBooking patientSMSNotify(@jakarta.annotation.Nullable Boolean patientSMSNotify) {
    
    this.patientSMSNotify = patientSMSNotify;
    return this;
  }

  /**
   * if patient need SMS notify
   * @return patientSMSNotify
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PATIENT_S_M_S_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPatientSMSNotify() {
    return patientSMSNotify;
  }


  @JsonProperty(JSON_PROPERTY_PATIENT_S_M_S_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPatientSMSNotify(@jakarta.annotation.Nullable Boolean patientSMSNotify) {
    this.patientSMSNotify = patientSMSNotify;
  }

  public V1RestBooking patientEmailNotify(@jakarta.annotation.Nullable Boolean patientEmailNotify) {
    
    this.patientEmailNotify = patientEmailNotify;
    return this;
  }

  /**
   * if patient need email notify
   * @return patientEmailNotify
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PATIENT_EMAIL_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPatientEmailNotify() {
    return patientEmailNotify;
  }


  @JsonProperty(JSON_PROPERTY_PATIENT_EMAIL_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPatientEmailNotify(@jakarta.annotation.Nullable Boolean patientEmailNotify) {
    this.patientEmailNotify = patientEmailNotify;
  }

  public V1RestBooking contactSMSNotify(@jakarta.annotation.Nullable Boolean contactSMSNotify) {
    
    this.contactSMSNotify = contactSMSNotify;
    return this;
  }

  /**
   * if contact need SMS notify
   * @return contactSMSNotify
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_S_M_S_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getContactSMSNotify() {
    return contactSMSNotify;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_S_M_S_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactSMSNotify(@jakarta.annotation.Nullable Boolean contactSMSNotify) {
    this.contactSMSNotify = contactSMSNotify;
  }

  public V1RestBooking contactEmailNotify(@jakarta.annotation.Nullable Boolean contactEmailNotify) {
    
    this.contactEmailNotify = contactEmailNotify;
    return this;
  }

  /**
   * if contact need email notify
   * @return contactEmailNotify
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_EMAIL_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getContactEmailNotify() {
    return contactEmailNotify;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_EMAIL_NOTIFY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactEmailNotify(@jakarta.annotation.Nullable Boolean contactEmailNotify) {
    this.contactEmailNotify = contactEmailNotify;
  }

  public V1RestBooking comment(@jakarta.annotation.Nullable String comment) {
    
    this.comment = comment;
    return this;
  }

  /**
   * additional information
   * @return comment
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getComment() {
    return comment;
  }


  @JsonProperty(JSON_PROPERTY_COMMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setComment(@jakarta.annotation.Nullable String comment) {
    this.comment = comment;
  }

  public V1RestBooking cancelled(@jakarta.annotation.Nullable Boolean cancelled) {
    
    this.cancelled = cancelled;
    return this;
  }

  /**
   * if booking is cancelled
   * @return cancelled
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CANCELLED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCancelled() {
    return cancelled;
  }


  @JsonProperty(JSON_PROPERTY_CANCELLED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCancelled(@jakarta.annotation.Nullable Boolean cancelled) {
    this.cancelled = cancelled;
  }

  public V1RestBooking cancelReason(@jakarta.annotation.Nullable V1RestCancelReason cancelReason) {
    
    this.cancelReason = cancelReason;
    return this;
  }

  /**
   * Get cancelReason
   * @return cancelReason
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CANCEL_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V1RestCancelReason getCancelReason() {
    return cancelReason;
  }


  @JsonProperty(JSON_PROPERTY_CANCEL_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCancelReason(@jakarta.annotation.Nullable V1RestCancelReason cancelReason) {
    this.cancelReason = cancelReason;
  }

  public V1RestBooking dateCancelled(@jakarta.annotation.Nullable OffsetDateTime dateCancelled) {
    
    this.dateCancelled = dateCancelled;
    return this;
  }

  /**
   * booking cancelled time
   * @return dateCancelled
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_CANCELLED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OffsetDateTime getDateCancelled() {
    return dateCancelled;
  }


  @JsonProperty(JSON_PROPERTY_DATE_CANCELLED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateCancelled(@jakarta.annotation.Nullable OffsetDateTime dateCancelled) {
    this.dateCancelled = dateCancelled;
  }

  public V1RestBooking completed(@jakarta.annotation.Nullable Boolean completed) {
    
    this.completed = completed;
    return this;
  }

  /**
   * if booking completed
   * @return completed
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPLETED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCompleted() {
    return completed;
  }


  @JsonProperty(JSON_PROPERTY_COMPLETED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompleted(@jakarta.annotation.Nullable Boolean completed) {
    this.completed = completed;
  }

  public V1RestBooking notCompletedReason(@jakarta.annotation.Nullable String notCompletedReason) {
    
    this.notCompletedReason = notCompletedReason;
    return this;
  }

  /**
   * Get notCompletedReason
   * @return notCompletedReason
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NOT_COMPLETED_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNotCompletedReason() {
    return notCompletedReason;
  }


  @JsonProperty(JSON_PROPERTY_NOT_COMPLETED_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNotCompletedReason(@jakarta.annotation.Nullable String notCompletedReason) {
    this.notCompletedReason = notCompletedReason;
  }

  public V1RestBooking attachments(@jakarta.annotation.Nullable List<RestAttachmentInfo> attachments) {
    
    this.attachments = attachments;
    return this;
  }

  public V1RestBooking addAttachmentsItem(RestAttachmentInfo attachmentsItem) {
    if (this.attachments == null) {
      this.attachments = new ArrayList<>();
    }
    this.attachments.add(attachmentsItem);
    return this;
  }

  /**
   * Get attachments
   * @return attachments
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ATTACHMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<RestAttachmentInfo> getAttachments() {
    return attachments;
  }


  @JsonProperty(JSON_PROPERTY_ATTACHMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAttachments(@jakarta.annotation.Nullable List<RestAttachmentInfo> attachments) {
    this.attachments = attachments;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    V1RestBooking v1RestBooking = (V1RestBooking) o;
    return Objects.equals(this.id, v1RestBooking.id) &&
        Objects.equals(this.bookingNumber, v1RestBooking.bookingNumber) &&
        Objects.equals(this.healiusId, v1RestBooking.healiusId) &&
        Objects.equals(this.ereferralHealiusId, v1RestBooking.ereferralHealiusId) &&
        Objects.equals(this.locationId, v1RestBooking.locationId) &&
        Objects.equals(this.created, v1RestBooking.created) &&
        Objects.equals(this.update, v1RestBooking.update) &&
        Objects.equals(this.testId, v1RestBooking.testId) &&
        Objects.equals(this.customerTestName, v1RestBooking.customerTestName) &&
        Objects.equals(this.createdBy, v1RestBooking.createdBy) &&
        Objects.equals(this.date, v1RestBooking.date) &&
        Objects.equals(this.fromTime, v1RestBooking.fromTime) &&
        Objects.equals(this.toTime, v1RestBooking.toTime) &&
        Objects.equals(this.reservationExpiry, v1RestBooking.reservationExpiry) &&
        Objects.equals(this.reminderSent, v1RestBooking.reminderSent) &&
        Objects.equals(this.additionalContact, v1RestBooking.additionalContact) &&
        Objects.equals(this.patientGivenName, v1RestBooking.patientGivenName) &&
        Objects.equals(this.patientFamilyName, v1RestBooking.patientFamilyName) &&
        Objects.equals(this.patientEmail, v1RestBooking.patientEmail) &&
        Objects.equals(this.patientPhone, v1RestBooking.patientPhone) &&
        Objects.equals(this.contactGivenName, v1RestBooking.contactGivenName) &&
        Objects.equals(this.contactFamilyName, v1RestBooking.contactFamilyName) &&
        Objects.equals(this.contactEmail, v1RestBooking.contactEmail) &&
        Objects.equals(this.contactPhone, v1RestBooking.contactPhone) &&
        Objects.equals(this.notificationPreference, v1RestBooking.notificationPreference) &&
        Objects.equals(this.patientSMSNotify, v1RestBooking.patientSMSNotify) &&
        Objects.equals(this.patientEmailNotify, v1RestBooking.patientEmailNotify) &&
        Objects.equals(this.contactSMSNotify, v1RestBooking.contactSMSNotify) &&
        Objects.equals(this.contactEmailNotify, v1RestBooking.contactEmailNotify) &&
        Objects.equals(this.comment, v1RestBooking.comment) &&
        Objects.equals(this.cancelled, v1RestBooking.cancelled) &&
        Objects.equals(this.cancelReason, v1RestBooking.cancelReason) &&
        Objects.equals(this.dateCancelled, v1RestBooking.dateCancelled) &&
        Objects.equals(this.completed, v1RestBooking.completed) &&
        Objects.equals(this.notCompletedReason, v1RestBooking.notCompletedReason) &&
        Objects.equals(this.attachments, v1RestBooking.attachments);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, bookingNumber, healiusId, ereferralHealiusId, locationId, created, update, testId, customerTestName, createdBy, date, fromTime, toTime, reservationExpiry, reminderSent, additionalContact, patientGivenName, patientFamilyName, patientEmail, patientPhone, contactGivenName, contactFamilyName, contactEmail, contactPhone, notificationPreference, patientSMSNotify, patientEmailNotify, contactSMSNotify, contactEmailNotify, comment, cancelled, cancelReason, dateCancelled, completed, notCompletedReason, attachments);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class V1RestBooking {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    bookingNumber: ").append(toIndentedString(bookingNumber)).append("\n");
    sb.append("    healiusId: ").append(toIndentedString(healiusId)).append("\n");
    sb.append("    ereferralHealiusId: ").append(toIndentedString(ereferralHealiusId)).append("\n");
    sb.append("    locationId: ").append(toIndentedString(locationId)).append("\n");
    sb.append("    created: ").append(toIndentedString(created)).append("\n");
    sb.append("    update: ").append(toIndentedString(update)).append("\n");
    sb.append("    testId: ").append(toIndentedString(testId)).append("\n");
    sb.append("    customerTestName: ").append(toIndentedString(customerTestName)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    date: ").append(toIndentedString(date)).append("\n");
    sb.append("    fromTime: ").append(toIndentedString(fromTime)).append("\n");
    sb.append("    toTime: ").append(toIndentedString(toTime)).append("\n");
    sb.append("    reservationExpiry: ").append(toIndentedString(reservationExpiry)).append("\n");
    sb.append("    reminderSent: ").append(toIndentedString(reminderSent)).append("\n");
    sb.append("    additionalContact: ").append(toIndentedString(additionalContact)).append("\n");
    sb.append("    patientGivenName: ").append(toIndentedString(patientGivenName)).append("\n");
    sb.append("    patientFamilyName: ").append(toIndentedString(patientFamilyName)).append("\n");
    sb.append("    patientEmail: ").append(toIndentedString(patientEmail)).append("\n");
    sb.append("    patientPhone: ").append(toIndentedString(patientPhone)).append("\n");
    sb.append("    contactGivenName: ").append(toIndentedString(contactGivenName)).append("\n");
    sb.append("    contactFamilyName: ").append(toIndentedString(contactFamilyName)).append("\n");
    sb.append("    contactEmail: ").append(toIndentedString(contactEmail)).append("\n");
    sb.append("    contactPhone: ").append(toIndentedString(contactPhone)).append("\n");
    sb.append("    notificationPreference: ").append(toIndentedString(notificationPreference)).append("\n");
    sb.append("    patientSMSNotify: ").append(toIndentedString(patientSMSNotify)).append("\n");
    sb.append("    patientEmailNotify: ").append(toIndentedString(patientEmailNotify)).append("\n");
    sb.append("    contactSMSNotify: ").append(toIndentedString(contactSMSNotify)).append("\n");
    sb.append("    contactEmailNotify: ").append(toIndentedString(contactEmailNotify)).append("\n");
    sb.append("    comment: ").append(toIndentedString(comment)).append("\n");
    sb.append("    cancelled: ").append(toIndentedString(cancelled)).append("\n");
    sb.append("    cancelReason: ").append(toIndentedString(cancelReason)).append("\n");
    sb.append("    dateCancelled: ").append(toIndentedString(dateCancelled)).append("\n");
    sb.append("    completed: ").append(toIndentedString(completed)).append("\n");
    sb.append("    notCompletedReason: ").append(toIndentedString(notCompletedReason)).append("\n");
    sb.append("    attachments: ").append(toIndentedString(attachments)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  public static class Builder {

    private V1RestBooking instance;

    public Builder() {
      this(new V1RestBooking());
    }

    protected Builder(V1RestBooking instance) {
      this.instance = instance;
    }

    public Builder id(String id) {
      this.instance.id = id;
      return this;
    }
    public Builder bookingNumber(String bookingNumber) {
      this.instance.bookingNumber = bookingNumber;
      return this;
    }
    public Builder healiusId(String healiusId) {
      this.instance.healiusId = healiusId;
      return this;
    }
    public Builder ereferralHealiusId(String ereferralHealiusId) {
      this.instance.ereferralHealiusId = ereferralHealiusId;
      return this;
    }
    public Builder locationId(UUID locationId) {
      this.instance.locationId = locationId;
      return this;
    }
    public Builder created(OffsetDateTime created) {
      this.instance.created = created;
      return this;
    }
    public Builder update(OffsetDateTime update) {
      this.instance.update = update;
      return this;
    }
    public Builder testId(UUID testId) {
      this.instance.testId = testId;
      return this;
    }
    public Builder customerTestName(String customerTestName) {
      this.instance.customerTestName = customerTestName;
      return this;
    }
    public Builder createdBy(String createdBy) {
      this.instance.createdBy = createdBy;
      return this;
    }
    public Builder date(LocalDate date) {
      this.instance.date = date;
      return this;
    }
    public Builder fromTime(String fromTime) {
      this.instance.fromTime = fromTime;
      return this;
    }
    public Builder toTime(String toTime) {
      this.instance.toTime = toTime;
      return this;
    }
    public Builder reservationExpiry(Long reservationExpiry) {
      this.instance.reservationExpiry = reservationExpiry;
      return this;
    }
    public Builder reminderSent(Boolean reminderSent) {
      this.instance.reminderSent = reminderSent;
      return this;
    }
    public Builder additionalContact(Boolean additionalContact) {
      this.instance.additionalContact = additionalContact;
      return this;
    }
    public Builder patientGivenName(String patientGivenName) {
      this.instance.patientGivenName = patientGivenName;
      return this;
    }
    public Builder patientFamilyName(String patientFamilyName) {
      this.instance.patientFamilyName = patientFamilyName;
      return this;
    }
    public Builder patientEmail(String patientEmail) {
      this.instance.patientEmail = patientEmail;
      return this;
    }
    public Builder patientPhone(String patientPhone) {
      this.instance.patientPhone = patientPhone;
      return this;
    }
    public Builder contactGivenName(String contactGivenName) {
      this.instance.contactGivenName = contactGivenName;
      return this;
    }
    public Builder contactFamilyName(String contactFamilyName) {
      this.instance.contactFamilyName = contactFamilyName;
      return this;
    }
    public Builder contactEmail(String contactEmail) {
      this.instance.contactEmail = contactEmail;
      return this;
    }
    public Builder contactPhone(String contactPhone) {
      this.instance.contactPhone = contactPhone;
      return this;
    }
    public Builder notificationPreference(RestNotificationPreference notificationPreference) {
      this.instance.notificationPreference = notificationPreference;
      return this;
    }
    public Builder patientSMSNotify(Boolean patientSMSNotify) {
      this.instance.patientSMSNotify = patientSMSNotify;
      return this;
    }
    public Builder patientEmailNotify(Boolean patientEmailNotify) {
      this.instance.patientEmailNotify = patientEmailNotify;
      return this;
    }
    public Builder contactSMSNotify(Boolean contactSMSNotify) {
      this.instance.contactSMSNotify = contactSMSNotify;
      return this;
    }
    public Builder contactEmailNotify(Boolean contactEmailNotify) {
      this.instance.contactEmailNotify = contactEmailNotify;
      return this;
    }
    public Builder comment(String comment) {
      this.instance.comment = comment;
      return this;
    }
    public Builder cancelled(Boolean cancelled) {
      this.instance.cancelled = cancelled;
      return this;
    }
    public Builder cancelReason(V1RestCancelReason cancelReason) {
      this.instance.cancelReason = cancelReason;
      return this;
    }
    public Builder dateCancelled(OffsetDateTime dateCancelled) {
      this.instance.dateCancelled = dateCancelled;
      return this;
    }
    public Builder completed(Boolean completed) {
      this.instance.completed = completed;
      return this;
    }
    public Builder notCompletedReason(String notCompletedReason) {
      this.instance.notCompletedReason = notCompletedReason;
      return this;
    }
    public Builder attachments(List<RestAttachmentInfo> attachments) {
      this.instance.attachments = attachments;
      return this;
    }


    /**
    * returns a built V1RestBooking instance.
    *
    * The builder is not reusable.
    */
    public V1RestBooking build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field.
  */
  public static Builder builder() {
    return new Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public Builder toBuilder() {
    return new Builder()
      .id(getId())
      .bookingNumber(getBookingNumber())
      .healiusId(getHealiusId())
      .ereferralHealiusId(getEreferralHealiusId())
      .locationId(getLocationId())
      .created(getCreated())
      .update(getUpdate())
      .testId(getTestId())
      .customerTestName(getCustomerTestName())
      .createdBy(getCreatedBy())
      .date(getDate())
      .fromTime(getFromTime())
      .toTime(getToTime())
      .reservationExpiry(getReservationExpiry())
      .reminderSent(getReminderSent())
      .additionalContact(getAdditionalContact())
      .patientGivenName(getPatientGivenName())
      .patientFamilyName(getPatientFamilyName())
      .patientEmail(getPatientEmail())
      .patientPhone(getPatientPhone())
      .contactGivenName(getContactGivenName())
      .contactFamilyName(getContactFamilyName())
      .contactEmail(getContactEmail())
      .contactPhone(getContactPhone())
      .notificationPreference(getNotificationPreference())
      .patientSMSNotify(getPatientSMSNotify())
      .patientEmailNotify(getPatientEmailNotify())
      .contactSMSNotify(getContactSMSNotify())
      .contactEmailNotify(getContactEmailNotify())
      .comment(getComment())
      .cancelled(getCancelled())
      .cancelReason(getCancelReason())
      .dateCancelled(getDateCancelled())
      .completed(getCompleted())
      .notCompletedReason(getNotCompletedReason())
      .attachments(getAttachments());
  }


}

