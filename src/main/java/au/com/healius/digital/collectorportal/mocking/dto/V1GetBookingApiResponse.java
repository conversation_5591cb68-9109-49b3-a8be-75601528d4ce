/*
 * SVC Booking API
 * Healius Booking api
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package au.com.healius.digital.collectorportal.mocking.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * V1GetBookingApiResponse
 */
@JsonPropertyOrder({
  V1GetBookingApiResponse.JSON_PROPERTY_CODE,
  V1GetBookingApiResponse.JSON_PROPERTY_RESPONSE_MESSAGE,
  V1GetBookingApiResponse.JSON_PROPERTY_ERRORS,
  V1GetBookingApiResponse.JSON_PROPERTY_PAGE,
  V1GetBookingApiResponse.JSON_PROPERTY_PAYLOAD
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-19T20:15:18.693725+10:00[Australia/Sydney]", comments = "Generator version: 7.10.0")
public class V1GetBookingApiResponse {
  public static final String JSON_PROPERTY_CODE = "code";
  @jakarta.annotation.Nonnull
  private String code;

  public static final String JSON_PROPERTY_RESPONSE_MESSAGE = "responseMessage";
  @jakarta.annotation.Nonnull
  private String responseMessage;

  public static final String JSON_PROPERTY_ERRORS = "errors";
  @jakarta.annotation.Nonnull
  private List<Error> errors = new ArrayList<>();

  public static final String JSON_PROPERTY_PAGE = "page";
  @jakarta.annotation.Nullable
  private Integer page;

  public static final String JSON_PROPERTY_PAYLOAD = "payload";
  @jakarta.annotation.Nullable
  private V1RestBooking payload;

  public V1GetBookingApiResponse() {
  }

  public V1GetBookingApiResponse code(@jakarta.annotation.Nonnull String code) {
    
    this.code = code;
    return this;
  }

  /**
   * Get code
   * @return code
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCode() {
    return code;
  }


  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCode(@jakarta.annotation.Nonnull String code) {
    this.code = code;
  }

  public V1GetBookingApiResponse responseMessage(@jakarta.annotation.Nonnull String responseMessage) {
    
    this.responseMessage = responseMessage;
    return this;
  }

  /**
   * Get responseMessage
   * @return responseMessage
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RESPONSE_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getResponseMessage() {
    return responseMessage;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setResponseMessage(@jakarta.annotation.Nonnull String responseMessage) {
    this.responseMessage = responseMessage;
  }

  public V1GetBookingApiResponse errors(@jakarta.annotation.Nonnull List<Error> errors) {
    
    this.errors = errors;
    return this;
  }

  public V1GetBookingApiResponse addErrorsItem(Error errorsItem) {
    if (this.errors == null) {
      this.errors = new ArrayList<>();
    }
    this.errors.add(errorsItem);
    return this;
  }

  /**
   * Get errors
   * @return errors
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ERRORS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<Error> getErrors() {
    return errors;
  }


  @JsonProperty(JSON_PROPERTY_ERRORS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setErrors(@jakarta.annotation.Nonnull List<Error> errors) {
    this.errors = errors;
  }

  public V1GetBookingApiResponse page(@jakarta.annotation.Nullable Integer page) {
    
    this.page = page;
    return this;
  }

  /**
   * Get page
   * @return page
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPage() {
    return page;
  }


  @JsonProperty(JSON_PROPERTY_PAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPage(@jakarta.annotation.Nullable Integer page) {
    this.page = page;
  }

  public V1GetBookingApiResponse payload(@jakarta.annotation.Nullable V1RestBooking payload) {
    
    this.payload = payload;
    return this;
  }

  /**
   * Get payload
   * @return payload
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYLOAD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V1RestBooking getPayload() {
    return payload;
  }


  @JsonProperty(JSON_PROPERTY_PAYLOAD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPayload(@jakarta.annotation.Nullable V1RestBooking payload) {
    this.payload = payload;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    V1GetBookingApiResponse v1GetBookingApiResponse = (V1GetBookingApiResponse) o;
    return Objects.equals(this.code, v1GetBookingApiResponse.code) &&
        Objects.equals(this.responseMessage, v1GetBookingApiResponse.responseMessage) &&
        Objects.equals(this.errors, v1GetBookingApiResponse.errors) &&
        Objects.equals(this.page, v1GetBookingApiResponse.page) &&
        Objects.equals(this.payload, v1GetBookingApiResponse.payload);
  }

  @Override
  public int hashCode() {
    return Objects.hash(code, responseMessage, errors, page, payload);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class V1GetBookingApiResponse {\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    responseMessage: ").append(toIndentedString(responseMessage)).append("\n");
    sb.append("    errors: ").append(toIndentedString(errors)).append("\n");
    sb.append("    page: ").append(toIndentedString(page)).append("\n");
    sb.append("    payload: ").append(toIndentedString(payload)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  public static class Builder {

    private V1GetBookingApiResponse instance;

    public Builder() {
      this(new V1GetBookingApiResponse());
    }

    protected Builder(V1GetBookingApiResponse instance) {
      this.instance = instance;
    }

    public Builder code(String code) {
      this.instance.code = code;
      return this;
    }
    public Builder responseMessage(String responseMessage) {
      this.instance.responseMessage = responseMessage;
      return this;
    }
    public Builder errors(List<Error> errors) {
      this.instance.errors = errors;
      return this;
    }
    public Builder page(Integer page) {
      this.instance.page = page;
      return this;
    }
    public Builder payload(V1RestBooking payload) {
      this.instance.payload = payload;
      return this;
    }


    /**
    * returns a built V1GetBookingApiResponse instance.
    *
    * The builder is not reusable.
    */
    public V1GetBookingApiResponse build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field.
  */
  public static Builder builder() {
    return new Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public Builder toBuilder() {
    return new Builder()
      .code(getCode())
      .responseMessage(getResponseMessage())
      .errors(getErrors())
      .page(getPage())
      .payload(getPayload());
  }


}

