package au.com.healius.digital.collectorportal.service;


import static au.com.healius.digital.librarycollections.domain.enumeration.CollectionSampleStatus.COLLECTED;
import static au.com.healius.digital.librarycollections.domain.enumeration.CollectionSampleStatus.TO_FOLLOW;
import static au.com.healius.digital.librarycollections.domain.enumeration.PathologyVisitState.ABANDONED;
import static au.com.healius.digital.librarycollections.domain.enumeration.PathologyVisitState.COLLECTION_IN_PROGRESS;
import static au.com.healius.digital.librarycollections.domain.enumeration.PathologyVisitState.COLLECTION_SUBMITTED;
import static au.com.healius.digital.librarycollections.domain.enumeration.PathologyVisitState.PRINTED;
import static au.com.healius.digital.librarycollections.domain.enumeration.PathologyVisitState.READY_TO_COLLECT;
import static au.com.healius.digital.librarycollections.domain.enumeration.RequestPriorityStatus.ROUTINE;
import static au.com.healius.digital.librarycollections.domain.enumeration.RequestPriorityStatus.URGENT;
import static java.util.Comparator.naturalOrder;

import au.com.healius.digital.collectorportal.domain.projection.OrderDetailsStub;
import au.com.healius.digital.collectorportal.domain.projection.OrderStub;
import au.com.healius.digital.collectorportal.domain.projection.SpecimenMultiCollectStub;
import au.com.healius.digital.collectorportal.domain.projection.SpecimenStub;
import au.com.healius.digital.collectorportal.domain.projection.VisitStatusStub;
import au.com.healius.digital.collectorportal.domain.projection.VisitStub;
import au.com.healius.digital.collectorportal.dto.AddReferrerDTO;
import au.com.healius.digital.collectorportal.dto.BillingForm;
import au.com.healius.digital.collectorportal.dto.CCSummary;
import au.com.healius.digital.collectorportal.dto.EditPatientDTO;
import au.com.healius.digital.collectorportal.dto.ExternalReceipt;
import au.com.healius.digital.collectorportal.dto.MedicareValidationResponseDTO;
import au.com.healius.digital.collectorportal.dto.OrderPatientReferrerStub;
import au.com.healius.digital.collectorportal.dto.ProcessVisitResult;
import au.com.healius.digital.collectorportal.dto.VisitForm;
import au.com.healius.digital.collectorportal.identity.api.model.CreateProfileRequestBody;
import au.com.healius.digital.collectorportal.identity.api.model.EditProfileRequestBody;
import au.com.healius.digital.collectorportal.identity.api.model.ReadProfileResponseApiResponse;
import au.com.healius.digital.collectorportal.identity.api.model.ReadProfileResponseBody;
import au.com.healius.digital.collectorportal.mocking.dto.V1RestBooking;
import au.com.healius.digital.collectorportal.service.paymentcalculator.PaymentException;
import au.com.healius.digital.collectorportal.session.CollectorSession;
import au.com.healius.digital.collectorportal.util.LogCall;
import au.com.healius.digital.librarycollections.domain.BPointPayment;
import au.com.healius.digital.librarycollections.domain.ClinicalDetails;
import au.com.healius.digital.librarycollections.domain.Fasting;
import au.com.healius.digital.librarycollections.domain.MedicareValidation;
import au.com.healius.digital.librarycollections.domain.Order;
import au.com.healius.digital.librarycollections.domain.ReferredPatient;
import au.com.healius.digital.librarycollections.domain.Referrer;
import au.com.healius.digital.librarycollections.domain.Specimen;
import au.com.healius.digital.librarycollections.domain.SpecimenMultiCollect;
import au.com.healius.digital.librarycollections.domain.UnknownReferrer;
import au.com.healius.digital.librarycollections.domain.Visit;
import au.com.healius.digital.librarycollections.domain.enumeration.BillingType;
import au.com.healius.digital.librarycollections.domain.enumeration.CollectionSampleStatus;
import au.com.healius.digital.librarycollections.domain.enumeration.CommercialPaymentType;
import au.com.healius.digital.librarycollections.domain.enumeration.PathologyOrderWorkflow;
import au.com.healius.digital.librarycollections.domain.enumeration.PathologyVisitState;
import au.com.healius.digital.librarycollections.domain.enumeration.RequestPriorityStatus;
import au.com.healius.digital.librarycollections.domain.projection.CreateOrderStub;
import au.com.healius.digital.librarycollections.repository.OrderRepository;
import au.com.healius.digital.librarycollections.repository.ReferredPatientRepository;
import au.com.healius.digital.librarycollections.repository.SpecimenMultiCollectRepository;
import au.com.healius.digital.librarycollections.repository.SpecimenRepository;
import au.com.healius.digital.librarycollections.repository.UnknownReferrerRepository;
import au.com.healius.digital.librarycollections.repository.VisitRepository;
import au.com.healius.digital.librarycollections.service.dto.ReferrerDTO;
import au.com.healius.digital.trm.domain.Orderable;
import au.com.healius.digital.trm.repository.OrderableRepository;
import io.github.resilience4j.retry.annotation.Retry;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.data.util.Predicates;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service class responsible for managing collection orders and their associated operations.
 * This service handles the creation, retrieval, and management of orders, visits, and related entities.
 * It provides functionality for linking orders to visits, managing payments, and handling order workflows.
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class CollectionOrderService {

    private final CollectionVisitService visitService;
    private final OrderableService orderableService;

    private final OrderRepository orderRepository;
    private final VisitRepository visitRepository;
    private final SpecimenRepository specimenRepository;
    private final SpecimenMultiCollectRepository specimenMultiCollectRepository;
    private final ReferredPatientRepository referredPatientRepository;
    private final UnknownReferrerRepository unknownReferrerRepository;

    private final Neo4jTemplate neo4jTemplate;

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private final CollectorSession collectorSession;
    private final MedicareValidationResponseService medicareValidationResponseService;
    private final InstantResultService instantResultService;
    private final OrderableRepository orderableRepository;

    private final CollectorSession session;
    private final IdentityService identityService;
    private final HealiusIdentifierService healiusIdentifierService;

    private final BookingService bookingService;

    /**
     * Retrieves an order by its Healius ID.
     *
     * @param healiusId The unique Healius identifier of the order
     * @return The order associated with the given Healius ID
     */
    @Transactional(readOnly = true)
    public Order getOrderByHealiusId(String healiusId) {
        return orderRepository.findByHealiusId(healiusId).orElseThrow();
    }

    /**
     * Links a Square payment to a visit.
     *
     * @param visitId The ID of the visit to link the payment to
     * @param squarePaymentId The ID of the Square payment to link
     */
    @Transactional
    public void linkSquareToVisit(String visitId, String squarePaymentId) {
        visitRepository.linkSquarePayment(visitId, squarePaymentId);
    }

    /**
     * Retrieves an order by its ID.
     *
     * @param id The unique identifier of the order
     * @return The order associated with the given ID
     */
    @Transactional(readOnly = true)
    public Order getOrderById(String id) {
        return orderRepository.findById(id).orElseThrow();
    }


    /**
     * Gets the first visit associated with an order, optionally filtered by a predicate.
     *
     * @param order The order to get the visit from
     * @param predicate Optional filter condition for the visit
     * @return The first matching visit
     */
    public Visit getFirstVisit(Order order, Optional<Predicate<Visit>> predicate) {
        return getFirstVisit(order,
                visit -> visit.getPathologyVisitState() != ABANDONED,
                predicate);
    }

    /**
     * Gets the first visit from a cancelled order.
     *
     * @param order The cancelled order
     * @return The first visit from the cancelled order
     */
    public Visit getFirstVisitFromCancelled(Order order) {
        return getFirstVisit(order,
                visit -> visit.getPathologyVisitState() == ABANDONED,
                Optional.empty());
    }

    private Visit getFirstVisit(Order order, Predicate<Visit> statePredicate, Optional<Predicate<Visit>> extraPredicate) {
        return order.getVisits()
                .stream()
                .filter(statePredicate)
                .filter(extraPredicate.orElseGet(Predicates::isTrue))
                .min(Comparator.comparing(Visit::getCreatedDate))
                .orElseThrow();
    }

    /**
     * Gets the most recent visit for an order, optionally filtered by a predicate.
     *
     * @param order The order to get the visit from
     * @param predicate Optional filter condition for the visit
     * @return An Optional containing the most recent matching visit
     */
    public Optional<Visit> getRecentVisit(Order order, Optional<Predicate<Visit>> predicate) {
        return order.getVisits().stream()
                .filter(predicate.orElseGet(Predicates::isTrue))
                .max(naturalOrder());
    }

    /**
     * Gets the most recent uncancelled visit for an order, optionally filtered by a predicate.
     *
     * @param order The order to get the visit from
     * @param predicate Optional filter condition for the visit
     * @return An Optional containing the most recent uncancelled matching visit
     */
    public Optional<Visit> getRecentUncancelledVisit(Order order, Optional<Predicate<Visit>> predicate) {
        return getRecentVisit(order, predicate
                .map(condition -> condition.and(visit -> visit.getPathologyVisitState() != ABANDONED)));
    }


    /**
     * Views an order's visit details.
     *
     * @param healiusId The Healius ID of the order
     * @param acc The collection center summary
     * @param zoneId The time zone ID
     * @return The order with visit details
     */
    public Order viewOrderVisit(String healiusId, CCSummary acc, ZoneId zoneId) {
        log.info("View order {}", healiusId);
        Order order = orderRepository.findByHealiusId(healiusId).orElseThrow();
        int orderableCount = order.getOrderableIds().size();
        
        // Check if there's exactly one visit and it's ready-to-collect with insufficient specimens
        if (order.getVisits().size() == 1) {
            Visit visit = order.getVisits().get(0);
            if (visit.getPathologyVisitState() == READY_TO_COLLECT && visit.getSpecimens().size() < orderableCount) {
                visitRepository.delete(visit);
                order = orderRepository.findByHealiusId(healiusId).orElseThrow();
            }
        }

        if (order.getVisits().isEmpty()){
            addNewVisit(acc, order, zoneId);
            order = orderRepository.findByHealiusId(healiusId).orElseThrow();
        }
        return order;
    }

    /**
     * Collects an order visit.
     *
     * @param healiusId The Healius ID of the order
     * @param acc The collection center summary
     * @param zoneId The time zone ID
     * @return The visit that was collected
     */
    public Visit collectOrderVisit(String healiusId, CCSummary acc, ZoneId zoneId) {
        log.info("Collect order {}", healiusId);
        Order order = orderRepository.findByHealiusId(healiusId).orElseThrow();

        boolean isToFollow = order.getVisits().stream()
            .anyMatch(i -> i.getPathologyVisitState() == COLLECTION_SUBMITTED);

        Visit visit = getRecentVisit(order, Optional.empty()).orElseThrow();
        if (visit.getPathologyVisitState() == COLLECTION_SUBMITTED) {
            return null;
        } else if (visit.getPathologyVisitState() == ABANDONED) {
            visit = copyCancelledVisit(order, visit, zoneId);
        }

        if (visit.getRequestPriorityStatus() == null) {
            orderRepository.linkOrderCompanyCode(order.getId(), acc.getBuCode());
            visit.setRequestPriorityStatus(order.getClinicalDetails() != null && order.getClinicalDetails().isUrgent() ? URGENT : ROUTINE);
            visit.setRequestPriority(order.getClinicalDetails() != null && order.getClinicalDetails().isUrgent() ? URGENT : ROUTINE);
            visit.setPriorityPhone(order.getClinicalDetails() != null ? order.getClinicalDetails().getUrgentPhone():"");
            visit.setPriorityFax(order.getClinicalDetails() != null ? order.getClinicalDetails().getUrgentFax():"");
            visit.setPriorityDate(order.getClinicalDetails() != null ? order.getClinicalDetails().getUrgentBy():"");
            neo4jTemplate.saveAs(visit, VisitStub.class);
        }

        if (visit.getPathologyVisitState() == READY_TO_COLLECT) {
            if (order.getPathologyOrderWorkflow() == PathologyOrderWorkflow.E_ORDER && !isToFollow) {

                instantResultService.addInstantResultViaOrder(
                        visit.getId(),
                        order.getOrderables(),
                        collectorSession.getCompany().getCode()
                );
                specimenRepository.updateSpecimenFromEOrder(visit.getId());
            }
        }

        visitService.setPathologyVisitState(acc.getCombinedId(), visit.getId(), COLLECTION_IN_PROGRESS, Optional.empty(), zoneId);

        return visitRepository.findById(visit.getId()).orElseThrow();
    }

    private Visit copyCancelledVisit(Order order, Visit cancelledVisit, ZoneId zoneId) {
        Visit copy = visitService.copyCancelledVisit(cancelledVisit, zoneId);
        orderRepository.linkOrderToVisit(order.getId(), copy.getId());
        orderableService.copySpecimens(cancelledVisit, Predicates.isTrue(), copy);

        return copy;
    }

    @Transactional
    @LogCall
    public BPointPayment bPointPayment(String healiusId,
                                       CommercialPaymentType commercialPaymentType,
                                       String bPointReceiptNo,
                                       String bPointAmountPaid) {
        Order order = orderRepository.findByHealiusId(healiusId).orElseThrow();

        Visit visit = getRecentUncancelledVisit(order, Optional.empty()).orElseThrow();

        BPointPayment bPointPayment = new BPointPayment();
        bPointPayment.setBPointReceiptNo(bPointReceiptNo);
        bPointPayment.setBPointAmountPaid(bPointAmountPaid);
        bPointPayment = neo4jTemplate.saveAs(bPointPayment, BPointPayment.class);

        orderRepository.setCommercialPaymentType(order.getId(), commercialPaymentType);
        visitRepository.linkBPPaymentToVisit(visit.getId(), bPointPayment.getId());
        return bPointPayment;
    }

    @Transactional
    @LogCall
    public CommercialPaymentType commercialPaymentType(String healiusId,
                                       CommercialPaymentType commercialPaymentType) {
        Order order = orderRepository.findByHealiusId(healiusId).orElseThrow();

        orderRepository.setCommercialPaymentType(order.getId(), commercialPaymentType);
        return commercialPaymentType;
    }

    @Transactional
    @Retry(name = "specimenStatus")
    public boolean updateSpecimenStatus(String visitId, String specimenId, CollectionSampleStatus collectionSampleStatus) {
        log.info("Update specimen status {} to {}", specimenId, collectionSampleStatus);

        Specimen specimen = specimenRepository.findById(specimenId).orElseThrow();
        if (specimen.getCollectionSampleStatus() == CollectionSampleStatus.DECLINE_TO_PAY) {
            specimen.setIsIFCAgreed(null);
        }
        if (collectionSampleStatus == CollectionSampleStatus.DECLINE_TO_PAY) {
            specimen.setIsIFCAgreed(false);
        }

        specimen.setCollectionSampleStatus(collectionSampleStatus);
        neo4jTemplate.saveAs(specimen, SpecimenStub.class);
        if (collectionSampleStatus != COLLECTED) {
            instantResultService.removeOrderableFromSpecimenInstantResult(visitId, specimen.getOrderableId());
        } else {
            instantResultService.addInstantResultViaPicker(visitId, List.of(specimen.getOrderableId()), collectorSession.getCompany().getCode());
        }
        return true;
    }

    @Transactional
    @Retry(name = "specimenIFCStatus")
    public boolean updateIFCStatus(String visitId, String specimenId, Boolean agreedStatus) {
        log.info("Update specimen IFC status {} to {}", specimenId, agreedStatus);

        Specimen specimen = specimenRepository.findById(specimenId).orElseThrow();
        specimen.setIsIFCAgreed(agreedStatus);

        SpecimenStub specimenReturned = neo4jTemplate.saveAs(specimen, SpecimenStub.class);
        log.info("Returned SpecimenStub {}", specimenReturned);
        return true;
    }

    @Transactional
    @Retry(name = "instantResultsStatus")
    public boolean updateInstantResultsStatus(String visitId, String instantResultId, String value) {
        log.info("Update instant results status {} to {}", instantResultId, value);

        instantResultService.setInstantResultValue(instantResultId, value);
        return true;
    }

    @Transactional
    @Retry(name = "specimenExternalReceipt")
    public void addSpecimenExternalReceipt(String specimenId, ExternalReceipt externalReceipt) {
        log.info("Update specimen external receipt {} to {}", specimenId, externalReceipt);

        BPointPayment paymentDetails = new BPointPayment();
        paymentDetails.setBPointAmountPaid(externalReceipt.getReceiptAmount());
        paymentDetails.setBPointReceiptNo(externalReceipt.getReceiptNumber());

        neo4jTemplate.saveAs(paymentDetails, BPointPayment.class);

        specimenRepository.linkBPointPayment(specimenId, paymentDetails.getId());
    }

    @Transactional
    @Retry(name = "specimenStatus")
    public boolean updateSpecimenMultiCollectStatus(String visitId, String specimenMasterId, CollectionSampleStatus collectionSampleStatus) {
        log.info("Update specimen multi collect status {} to {}", specimenMasterId, collectionSampleStatus);

        SpecimenMultiCollect specimen = specimenMultiCollectRepository.findById(specimenMasterId).orElseThrow();
        specimen.setCollectionSampleStatus(collectionSampleStatus);
        neo4jTemplate.saveAs(specimen, SpecimenMultiCollectStub.class);

        if (collectionSampleStatus != COLLECTED) {
            instantResultService.removeOrderableFromSpecimenInstantResult(visitId, specimen.getOrderableId());
        } else {
            instantResultService.addInstantResultViaPicker(visitId, List.of(specimen.getOrderableId()), collectorSession.getCompany().getCode());
        }
        return true;
    }

    /**
     * Creates a new order with the specified parameters.
     *
     * @param companyId The ID of the company
     * @param workflow The workflow workflow of the order
     * @param referredPatient The patient being referred
     * @param requestedDate The requested date for the order
     * @param zoneId The time zone ID
     * @return A stub containing the created order details
     */
    @Transactional
    public CreateOrderStub createOrder(String companyId,
                                       PathologyOrderWorkflow workflow,
                                       ReferredPatient referredPatient,
                                       String requestedDate,
                                       ZoneId zoneId) {
        log.info("createOrder {} {} {}", companyId, workflow, referredPatient);
        ZonedDateTime now = ZonedDateTime.now(zoneId);

        Order order = new Order();
        order.setPathologyOrderWorkflow(workflow);
        order.setHealiusId(healiusIdentifierService.getEightDigitIdentifier().block().getPayload().getId());
        order.setBillingConcession(false);
        order.setCreatedDate(now);
        order.setRequestedDate(atStartOfDay(requestedDate, zoneId));
        order.setClinicalDetails(new ClinicalDetails());

        order.setReferredPatient(referredPatient);

        if (workflow == PathologyOrderWorkflow.COMMERCIAL) {
            order.setBillingType(BillingType.COMMERCIAL);
        }

        if(order.getBillingType() == null && referredPatient.getMedicareNo() != null) {
            order.setBillingType(BillingType.BULK_BILLED);
        }

        Visit visit = new Visit();
        visit.setPathologyVisitState(PathologyVisitState.READY_TO_COLLECT);
        visit.setCreatedDate(now);
        visit.setModifiedDate(now);
        visit.setRequestPriorityStatus(ROUTINE);
        order.getVisits().add(visit);

        log.info("Saving order {}", order);

        CreateOrderStub createdOrder = neo4jTemplate.saveAs(order, CreateOrderStub.class);

        log.info("Saved {}", createdOrder);

        orderRepository.linkOrderCompany(createdOrder.getId(), companyId);

        log.info("Linked");

        return createdOrder;
    }


    private void addNewVisit(CCSummary cc, Order order, ZoneId zoneId) {
        log.info("addNewVisit {}", order);
        String freeText = order.getFreeText().stream().map(au.com.healius.digital.librarycollections.domain.FreeText::getTest).collect(Collectors.joining(", "));

        Visit visit = visitService.createVisit(cc, freeText, Optional.ofNullable(order.getDoesNotConsentToUploadEHealth()).orElse(false), zoneId);
        visit = neo4jTemplate.saveAs(visit, Visit.class);
        orderRepository.linkOrderToVisit(order.getId(), visit.getId());
        List<Orderable> orderables = order.getOrderableIds().stream().map(i-> orderableRepository.findById(i).orElseThrow()).toList();
        orderableService.addSpecimensViaOrder(
                visit.getId(),
                orderables,
                collectorSession.getCompany().getCode()
        );
    }

    private ZonedDateTime atStartOfDay(String dateString, ZoneId zoneId) {
        LocalDate date = LocalDate.parse(dateString, dateTimeFormatter);
        return date.atStartOfDay(zoneId);
    }

    /**
     * Updates the referred patient information for an order.
     *
     * @param orderId The ID of the order
     * @param update The updated patient information
     * @return The updated referred patient
     */
    @Transactional
    public ReferredPatient updateReferredPatient(String orderId, EditPatientDTO update) {
        log.info("setReferredPatient {} {}", orderId, update);
        ReferredPatient referredPatient = referredPatientRepository.findByOrderId(orderId).orElseThrow();
        BeanUtils.copyProperties(update, referredPatient, "id","healiusId");
        referredPatient.setFamilyName(update.getFamilyName().trim());
        referredPatient.setFamilyNameSearch(referredPatient.getFamilyName().toUpperCase());
        if (update.getGivenName() == null) {
            referredPatient.setGivenName(null);
            referredPatient.setGivenNameSearch(null);
            referredPatient.setMiddleNames(null);
        } else {
            referredPatient.setGivenName(update.getGivenName().trim());
            referredPatient.setGivenNameSearch(referredPatient.getGivenName().toUpperCase());
            referredPatient.setMiddleNames(update.getMiddleNames().trim());
        }
        referredPatient.setDateOfBirth(LocalDate.parse(update.getDateOfBirth(), dateTimeFormatter));

        referredPatient.setCountry(update.getCountry());

        referredPatient.setAutocompleteStreetAddress(update.getAutocompleteStreetAddress());
        referredPatient.setAutocompletePlaceId(update.getAutocompletePlaceId());
        referredPatient.setStreetAddress(update.getStreetAddress());
        referredPatient.setStreetAddress2(update.getStreetAddress2());
        referredPatient.setCity(update.getCity());
        referredPatient.setState(update.getState());
        referredPatient.setPostCode(update.getPostCode());

        referredPatient.setDVANo(update.getDvaNo());
        referredPatient.setDvaCardType(update.getDvaCardType());

        return referredPatientRepository.save(referredPatient);
    }

    /**
     * Updates Medicare OS validation patient details.
     *
     * @param orderId The ID of the order
     * @param update The updated patient information
     * @return The updated referred patient
     */
    @Transactional
    public ReferredPatient updateMedicareOSValidationPatientDetails(String orderId, EditPatientDTO update) {
        ReferredPatient referredPatient = referredPatientRepository.findByOrderId(orderId).orElseThrow();

        referredPatient.setFamilyName(update.getFamilyName().trim());
        referredPatient.setFamilyNameSearch(update.getFamilyName().toUpperCase());
        if (update.getGivenName() == null) {
            referredPatient.setGivenName(null);
            referredPatient.setGivenNameSearch(null);
        } else {
            referredPatient.setGivenName(update.getGivenName().trim());
            referredPatient.setGivenNameSearch(update.getGivenName().toUpperCase());
        }
        referredPatient.setDateOfBirth(LocalDate.parse(update.getDateOfBirth(), dateTimeFormatter));
        referredPatient.setSex(update.getSex());

        return referredPatientRepository.save(referredPatient);
    }

    /**
     * Sets the referrer for an order.
     *
     * @param orderId The ID of the order
     * @param referrerId The ID of the referrer
     * @return The set referrer
     */
    @Transactional
    public Referrer setReferrer(String orderId, String referrerId) {
        log.info("setReferrer {} {}", orderId, referrerId);

        return orderRepository.linkOrderReferrer(orderId, referrerId).getReferrer();
    }

    /**
     * Sets the referrer for an order using detailed information.
     *
     * @param orderId The ID of the order
     * @param addReferrerDTO The referrer information to add
     */
    @Transactional
    public void setReferrer(String orderId, AddReferrerDTO addReferrerDTO) {
        log.info("setReferrer {} {}", orderId, addReferrerDTO);

        if(!addReferrerDTO.isUnknownReferrer() && addReferrerDTO.getId() != null){
            setReferrer(orderId, addReferrerDTO.getId());
        }else{
            log.info("set unknownReferer {} {}", orderId, addReferrerDTO);
            UnknownReferrer unknownReferrer = new UnknownReferrer();
            BeanUtils.copyProperties(addReferrerDTO, unknownReferrer,"id");
            unknownReferrer.setGivenName("Provider Not Found");
            UnknownReferrer savedUnknownReferrer = unknownReferrerRepository.save(unknownReferrer);
            orderRepository.linkOrderUnknownReferrer(orderId, savedUnknownReferrer.getId()).getReferrer();
        }
    }

    /**
     * Gets the unknown provider referrer.
     *
     * @return The unknown provider referrer
     */
//    public Referrer getUnknownProvider() {
//        return unknownReferrerRepository.findUnknownReferrer().orElseGet(() -> {
//            log.warn("Creating new UnknownProvider");
//            UnknownReferrer referrer = new UnknownReferrer();
//            referrer.setFamilyName("Provider Not Found");
//            return referrerRepository.save(referrer);
//        });
//    }

    /**
     * Removes the referrer from an order.
     *
     * @param orderId The ID of the order
     */
    @Transactional
    public void removeReferrer(String orderId) {
        orderRepository.unlinkOrderReferrer(orderId);
    }

    /**
     * Sets the copy-to referrers for an order.
     *
     * @param orderId The ID of the order
     * @param referrerIds The list of referrer IDs to copy to
     * @return The list of set copy-to referrers
     */
    @Transactional
    public List<Referrer> setCopyTo(String orderId, List<AddReferrerDTO> referrerIds) {
        log.info("addCopyTo {} {}",
            orderId,
            referrerIds
        );

        List<String> processedReferrerIds = new ArrayList<>(referrerIds.stream()
            .filter(i -> !i.isUnknownReferrer())
            .map(ReferrerDTO::getId)
            .toList());

        for (ReferrerDTO referrer : referrerIds.stream()
            .filter(AddReferrerDTO::isUnknownReferrer)
            .toList()) {
            UnknownReferrer referrers = new UnknownReferrer();
            referrers.setFamilyName(referrer.getGivenName() + " " + referrer.getFamilyName());
            referrers.setGivenName("Provider Not Found");
            unknownReferrerRepository.save(referrers);
            processedReferrerIds.add(referrers.getId());
        }

        return Optional.ofNullable(orderRepository.linkOrderCopyTo(orderId,
                processedReferrerIds))
            .map(Order::getCopyTo)
            .orElseGet(Collections::emptyList);
    }

    /**
     * Removes a copy-to referrer from an order.
     *
     * @param orderId The ID of the order
     * @param referrerId The ID of the referrer to remove
     * @return The updated list of copy-to referrers
     */
    @Transactional
    public List<Referrer> removeCopyToReferrer(String orderId, String referrerId) {
        return Optional.ofNullable(orderRepository
                        .unlinkOrderCopyTo(orderId, referrerId))
                .map(Order::getCopyTo)
                .orElseGet(Collections::emptyList);
    }

    /**
     * Updates the request priority for an order.
     *
     * @param orderId The ID of the order
     * @param requestPriority The new priority status
     * @param priorityPhone The priority phone number
     * @param priorityFax The priority fax number
     * @param priorityDate The priority date
     */
    @Transactional
    public void updateRequestPriority(String orderId,
                                      RequestPriorityStatus requestPriority,
                                      String priorityPhone,
                                      String priorityFax,
                                      String priorityDate) {
        log.debug("update priority {} {}", orderId, requestPriority);
        Order order = orderRepository.findById(orderId).orElseThrow();
        ClinicalDetails clinicalDetails = getClinicalDetails(order);
        clinicalDetails.setUrgentPhone(priorityPhone);
        clinicalDetails.setUrgentFax(priorityFax);
        clinicalDetails.setUrgentBy(priorityDate);
        neo4jTemplate.saveAs(order, OrderDetailsStub.class);

        Visit visit = getRecentUncancelledVisit(order, Optional.empty()).orElseThrow();
        visit.setRequestPriorityStatus(requestPriority);
        neo4jTemplate.saveAs(visit, VisitStub.class);
    }

    private ClinicalDetails getClinicalDetails(Order order) {
        if (order.getClinicalDetails() == null) {
            log.info("Attaching clinical details");
            order.setClinicalDetails(new ClinicalDetails());
        }
        return order.getClinicalDetails();
    }

    /**
     * Updates the billing information for an order.
     *
     * @param orderId The ID of the order
     * @param form The billing form containing updated information
     * @return The updated order
     */
    @Transactional
    public Order updateBilling(String orderId,
                               BillingForm form) {
        log.debug("update billing {}", orderId);
        Order order = orderRepository.findById(orderId).orElseThrow();

        order.setBillingType(form.getBillingType());
        order.getReferredPatient().setMedicareNo(form.getMedicareNumber());
        order.getReferredPatient().setMedicareIndex(form.getMedicareIndex());
        order.getReferredPatient().setDvaCardType(form.getDvaCardType());
        order.getReferredPatient().setDVANo(form.getDvaNo());
        order.getReferredPatient().setHealthFundNo(form.getHealthFundMembershipNumber());
        order.getReferredPatient().setHealthFundExpiry(form.getHealthFundExpiry());
        order.getReferredPatient().setHealthFundRefNo(form.getHealthFundRefNo());

        neo4jTemplate.saveAs(order, OrderPatientReferrerStub.class);

        Visit visit = getRecentUncancelledVisit(order, Optional.empty()).orElseThrow();
        visit.setBillingType(form.getBillingType());
        visit.setDvaCardType(form.getDvaCardType());
        visit.setHealthFundCard(form.getHealthFund());
        visit.setHealthFundMembershipNumber(form.getHealthFundMembershipNumber());
        visit.setMedicareCardSighted(form.isMedicareCardSighted());
        visit.setDvaCardSighted(form.isDvaCardSighted());
        visit.setPrivateHealthSighted(form.isPrivateHealthSighted());
        visit.setPrivatePatientDetailsMatch(form.isPrivatePatientDetailsMatch());
        visit.setPatientEpisodeInitiated(form.getPatientEpisodeInitiated());
        visit.setCoverManualVerification(form.isCoverManualVerification());
        visit.setCollectorManuallyVerified(form.isCollectorManualVerification());
        visit.setMedicareMBA(form.isMedicareMBA());

        MedicareValidationResponseDTO medicareValidationResponseDTO = form.getMedicareResponse();

        if (medicareValidationResponseDTO != null) {
            MedicareValidation medicareResponseBuilder = MedicareValidation.builder()
                    .code(medicareValidationResponseDTO.getCode())
                    .text(medicareValidationResponseDTO.getText())
                    .validationType(medicareValidationResponseDTO.getValidationType())
                    .correctedGivenName(medicareValidationResponseDTO.getCorrectedGivenName())
                    .correctedMedicareNumber(medicareValidationResponseDTO.getCorrectedCardNumber())
                    .correctedMedicareReferenceNumber(medicareValidationResponseDTO.getCorrectedCardRefNo())
                    .dateRequested(ZonedDateTime.now())
                    .build();

            medicareValidationResponseService.saveAndLinkToVisit(medicareResponseBuilder,
                    visit.getId(),
                    medicareValidationResponseDTO.getErrors());
        }


        neo4jTemplate.saveAs(visit, VisitStub.class);

        return order;
    }

    /**
     * Updates the referred patient's billing information.
     *
     * @param orderId The ID of the order
     * @param mapAddressAUBilling The mapped address for AU billing
     * @param apartmentInputBilling The apartment input for billing
     * @param autocompleteStreetAddress The autocompleted street address
     * @param autocompletePlaceId The autocompleted place ID
     * @param suburbInputBilling The suburb input for billing
     * @param stateInputBilling The state input for billing
     * @param postcodeInputBilling The postcode input for billing
     * @param emailAddressInputBilling The email address input for billing
     * @param mobilePhoneUIBilling The mobile phone input for billing
     * @param countryBilling The country for billing
     * @return The updated referred patient
     */
    @Transactional
    public ReferredPatient updateReferredPatient(String orderId,
                                               String mapAddressAUBilling,
                                               String apartmentInputBilling,
                                               String autocompleteStreetAddress,
                                               String autocompletePlaceId,
                                               String suburbInputBilling,
                                               String stateInputBilling,
                                               String postcodeInputBilling,
                                               String emailAddressInputBilling,
                                               String mobilePhoneUIBilling,
                                               String countryBilling) {
        log.debug("update patient {}", orderId);
        Order order = orderRepository.findById(orderId).orElseThrow();
        ReferredPatient referredPatient = Optional.ofNullable(order.getReferredPatient())
                .orElseGet(ReferredPatient::new);

        referredPatient.setStreetAddress(mapAddressAUBilling);
        referredPatient.setStreetAddress2(apartmentInputBilling);
        referredPatient.setAutocompleteStreetAddress(autocompleteStreetAddress);
        referredPatient.setAutocompletePlaceId(autocompletePlaceId);
        referredPatient.setCity(suburbInputBilling);
        referredPatient.setState(stateInputBilling);
        referredPatient.setPostCode(postcodeInputBilling);

        referredPatient.setHomeEmail(emailAddressInputBilling);
        referredPatient.setMobilePhone(mobilePhoneUIBilling);
        referredPatient.setCountry(countryBilling);

        return referredPatientRepository.save(referredPatient);
    }

    /**
     * Updates the requested date for an order.
     *
     * @param orderId The ID of the order
     * @param requestedDate The new requested date
     * @param zoneId The time zone ID
     * @return The updated requested date
     */
    @Transactional
    public ZonedDateTime updateRequestedDate(String orderId, String requestedDate, ZoneId zoneId) {
        log.debug("update requested date {}", orderId);
        ZonedDateTime requestedDateZoned = atStartOfDay(requestedDate, zoneId);
        Order order = orderRepository.findById(orderId).orElseThrow();
        order.setRequestedDate(requestedDateZoned);
        neo4jTemplate.saveAs(order, OrderStub.class);
        return requestedDateZoned;
    }

    /**
     * Submits an order visit.
     *
     * @param orderId The ID of the order
     * @param visitId The ID of the visit
     * @param ccSummary The collection center summary
     * @param collectorName The name of the collector
     * @param trainerName The name of the trainer
     * @param zoneId The time zone ID
     * @return The result of processing the visit
     * @throws PaymentException if there is an error with payment processing
     */
    @Transactional
    @LogCall
    public ProcessVisitResult submitOrderVisit(String orderId, String visitId, CCSummary ccSummary, String collectorName, String trainerName, ZoneId zoneId) throws PaymentException {
        Visit visit = visitService.submitVisit(visitId, ccSummary.getCombinedId(), collectorName, trainerName, zoneId);

        // Update booking status if there is an active booking
        String activeBookingId = collectorSession.getActiveBookingId();
        if (activeBookingId != null) {
            try {
                V1RestBooking booking = new V1RestBooking()
                    .id(activeBookingId)
                    .completed(true);
                bookingService.updateBookingAdmin(UUID.fromString(activeBookingId), booking)
                    .doOnSuccess(response -> log.info("Updated booking status to COMPLETED for booking ID: {}", activeBookingId))
                    .doOnError(error -> log.error("Failed to update booking status for booking ID: {}", activeBookingId, error))
                    .subscribe();
            } catch (Exception e) {
                log.error("Failed to update booking status for booking ID: {}", activeBookingId, e);
            }
        }

        Optional<Visit> visitToFollowOptional = visitService.createToFollowVisit(visit, ccSummary, zoneId)
                .map(toFollowVisit -> {
                    orderRepository.linkOrderToVisit(orderId, toFollowVisit.getId());
                    orderableService.copySpecimens(visit, specimen -> specimen.getCollectionSampleStatus() == TO_FOLLOW
                            || specimen.getCollectionSampleStatus() == null, toFollowVisit);
                    return toFollowVisit;
                });

        Order order = getOrderById(orderId);
        Visit firstVisit = getFirstVisit(order, Optional.empty());

        order.setSubmittedDate(visit.getModifiedDate());
        order.setSavedDate(null);

        if (order.getReferenceId() == null || Objects.equals(order.getReferenceId(), "")) {
            order.setReferenceId(order.getHealiusId());
        }


        if(order.getPathologyOrderWorkflow().equals(PathologyOrderWorkflow.PAPER)){
            if(order.getReferredPatient().getHealiusId() == null) {
                order.getReferredPatient().setHealiusId(createHealiusIdentityProfile(order.getReferredPatient()));
                referredPatientRepository.save(order.getReferredPatient());
            } else {
                String newFriendlyId = updateHealiusIdentityProfile(order.getReferredPatient());
                if (newFriendlyId != null && !newFriendlyId.equals(order.getReferredPatient().getHealiusId())) {
                    log.warn("Patient healius friendly id has been updated for patient: {}", order.getReferredPatient());
                    order.getReferredPatient().setHealiusId(newFriendlyId);
                    referredPatientRepository.save(order.getReferredPatient());
                }
            }
        }

        ClinicalDetails clinicalDetails = getClinicalDetails(order);
        clinicalDetails.setUrgent(false);

        log.info("save order {}", order.getHealiusId());
        OrderStub savedOrder = neo4jTemplate.saveAs(order, OrderStub.class);
        return new ProcessVisitResult(savedOrder, firstVisit, visit, visitToFollowOptional, savedOrder.getReferredPatient().getHealiusId());
    }

    /**
     * Updates an existing Healius identity profile with the latest patient information.
     */
    @LogCall
    private String updateHealiusIdentityProfile(ReferredPatient referredPatient) {
        if (referredPatient == null || referredPatient.getHealiusId() == null) {
            log.warn("Cannot update identity profile: patient or Healius ID is null");
            return null;
        }

        log.debug("Looking up profile by friendlyId: {}", referredPatient.getHealiusId());

        ReadProfileResponseApiResponse lookupResponse = identityService
                .lookupByFriendlyId(referredPatient.getHealiusId()).orElse(null);

        return Optional.ofNullable(lookupResponse)
                .map(ReadProfileResponseApiResponse::getPayload)
                .map(ReadProfileResponseBody::getFriendlyId)
                .filter(id -> !id.isEmpty())
                .map(s -> {
                    EditProfileRequestBody.Builder builder = EditProfileRequestBody.builder();

                    builder.firstName(referredPatient.getGivenName());
                    builder.middleNames(referredPatient.getMiddleNames());
                    builder.lastName(referredPatient.getFamilyName());

                    // birthSex and dateOfBirth are required and assumed non-null (from lookupResponse)
                    builder.birthSex(EditProfileRequestBody.BirthSexEnum.valueOf(lookupResponse.getPayload().getBirthSex().name()));
                    builder.dateOfBirth(lookupResponse.getPayload().getDateOfBirth());

                    if (referredPatient.getMobilePhone() != null) {
                        builder.mobilePhoneNumber(referredPatient.getMobilePhone());
                    }

                    if (referredPatient.getMedicareNo() != null) {
                        builder.medicareNumber(referredPatient.getMedicareNo());
                    }

                    if (referredPatient.getMedicareIndex() != null) {
                        builder.medicareIrn(referredPatient.getMedicareIndex());
                    }

                    if (referredPatient.getStreetAddress() != null) {
                        builder.streetAddress(referredPatient.getStreetAddress());
                    }

                    if (referredPatient.getStreetAddress2() != null) {
                        builder.streetAddress2(referredPatient.getStreetAddress2());
                    }

                    if (referredPatient.getCity() != null) {
                        builder.city(referredPatient.getCity());
                    }

                    if (referredPatient.getState() != null) {
                        builder.state(referredPatient.getState());
                    }

                    if (referredPatient.getPostCode() != null) {
                        builder.postCode(referredPatient.getPostCode());
                    }

                    if (referredPatient.getCountry() != null) {
                        builder.country(referredPatient.getCountry());
                    }

                    if (referredPatient.getAddressType() != null) {
                        builder.addressType(referredPatient.getAddressType());
                    }

                    if (referredPatient.getHomeEmail() != null) {
                        builder.email(referredPatient.getHomeEmail());
                    }

                    if (referredPatient.getBusinessPhone() != null) {
                        builder.workPhoneNumber(referredPatient.getBusinessPhone());
                    }

                    if (referredPatient.getRace() != null) {
                        builder.ethnicityOrRace(EditProfileRequestBody.EthnicityOrRaceEnum.valueOf(referredPatient.getRace().name()));
                    }

                    if (referredPatient.getHealthCareCard() != null) {
                        builder.healthCareConcessionCardNo(referredPatient.getHealthCareCard());
                    }

                    if (referredPatient.getDVANo() != null) {
                        builder.veteranCardNo(referredPatient.getDVANo());
                    }

                    if (referredPatient.getDvaCardType() != null) {
                        builder.veteranCardColor(EditProfileRequestBody.VeteranCardColorEnum.valueOf(referredPatient.getDvaCardType().name()));
                    }

                    EditProfileRequestBody requestBody = builder.build();


                    ReadProfileResponseApiResponse updateResponse = identityService
                            .editProfile(lookupResponse.getPayload().getProfileId(), requestBody).orElse(null);

                    return Optional.ofNullable(updateResponse)
                            .map(ReadProfileResponseApiResponse::getPayload)
                            .map(ReadProfileResponseBody::getFriendlyId)
                            .filter(id -> !id.isEmpty())
                            .orElse(null);
                })
                .orElse(null);
    }


    /**
     * Creates a Healius identity profile for a referred patient.
     *
     * @param referredPatient The referred patient information
     * @return The friendly ID of the created profile
     * @throws RuntimeException if profile creation fails or returns invalid response
     */
    private String createHealiusIdentityProfile(ReferredPatient referredPatient) {
        CreateProfileRequestBody createProfileRequestBody = identityService.referredPatientToCreateProfileRequestBody(referredPatient);
        ReadProfileResponseApiResponse readProfileResponseApiResponse = identityService.createProfile(createProfileRequestBody).orElse(null);

        return Optional.ofNullable(readProfileResponseApiResponse)
                .map(ReadProfileResponseApiResponse::getPayload)
                .map(payload -> {
                    log.debug("Profile response payload received: {}", payload);
                    return payload.getFriendlyId();
                })
                .filter(id -> !id.isEmpty())
                .orElseThrow(() -> new RuntimeException("Failed to get valid friendlyId from profile response"));
    }

    /**
     * Switches an order to the paper workflow.
     *
     * @param healiusId The Healius ID of the order
     * @param zoneId The time zone ID
     * @return The updated order
     */
    @Transactional
    public Order switchToPaperWorkflow(String healiusId, ZoneId zoneId) {
        Order order = orderRepository.findByHealiusId(healiusId).orElseThrow();
        order.setPrintedDate(ZonedDateTime.now(zoneId));

        Visit visit = getFirstVisit(order, Optional.empty());
        visit.setPathologyVisitState(PRINTED);
        neo4jTemplate.saveAs(visit, VisitStatusStub.class);
        neo4jTemplate.saveAs(order, OrderDetailsStub.class);
        return order;
    }

    /**
     * Gets the Healius ID for a new order.
     *
     * @return The generated Healius ID
     */
    public String getHealiusId() {
        boolean exists;
        String healiusId;
        do {
            healiusId = RandomStringUtils.randomAlphanumeric(6).toUpperCase();
            exists = orderRepository.existsByHealiusId(healiusId);
            if (exists) {
                log.warn("Retrying getting new Healius Id");
            }
        } while (exists);
        return healiusId;
    }

    /**
     * Gets the patient's email for receipt.
     *
     * @param orderId The ID of the order
     * @return The patient's email address
     */
    public String getPatientEmailForReceipt(String orderId){
        Order order = orderRepository.findById(orderId).orElseThrow();
        ReferredPatient referredPatient = Optional.ofNullable(order.getReferredPatient())
                .orElseGet(ReferredPatient::new);

        return  referredPatient.getHomeEmail();

    }

    /**
     * Finds all orders with AI request form within a date range.
     *
     * @param startOfDay The start of the day
     * @param endOfDay The end of the day
     * @return List of orders with AI request form
     */
    public List<Order> findAllOrdersWithAiRequestForm(ZonedDateTime startOfDay, ZonedDateTime endOfDay) {
        return orderRepository.findOrdersWithAiRequestForm(startOfDay, endOfDay);
    }

    /**
     * Updates the referral section for a visit.
     *
     * @param orderId The ID of the order
     * @param visitId The ID of the visit
     * @param visitForm The visit form containing updated information
     */
    @Transactional
    public void updateReferralSection(String orderId, String visitId, VisitForm visitForm) {
        updateFastingStatus(visitId,
            visitForm.getFasting(),
            visitForm.getFastingDate(),
            visitForm.getFastingTime());

      updateVisitRequestPriority(visitId,
            visitForm.getRequestPriority(),
            visitForm.getPriorityPhone(),
            visitForm.getPriorityFax(),
            visitForm.getPriorityDate());

    }


  private void updateVisitRequestPriority(String visitId, RequestPriorityStatus priority,
      String phone, String fax, String date) {
    Visit visit = visitRepository.findById(visitId)
        .orElseThrow();

    visit.setRequestPriorityStatus(priority);
    visit.setPriorityDate(date);
    visit.setPriorityPhone(phone);
    visit.setPriorityFax(fax);
    neo4jTemplate.saveAs(visit,
        VisitStub.class);
  }

  private void updateFastingStatus(String visitId, boolean fasting,
        String fastingDate,
        String fastingTime) {
        Visit visit = visitRepository.findById(visitId)
            .orElseThrow();

        if (!fasting) {
            if (visit.getFasting() != null) {
                visitRepository.deleteFasting(visit.getFasting()
                    .getId());
            }
            return;
        }

        LocalDate date = LocalDate.parse(fastingDate,
            dateTimeFormatter);

        String[] timeParts = fastingTime.split(":");
        int hour = Integer.parseInt(timeParts[0]);
        int minute = Integer.parseInt(timeParts[1]);
        int second = timeParts.length > 2 ? Integer.parseInt(timeParts[2]) : 0;

        ZonedDateTime combinedDateTime = date.atTime(hour,
                minute,
                second)
            .atZone(ZoneId.systemDefault());
        Fasting existingFasting = Optional.ofNullable(visit.getFasting())
            .orElse(Fasting.builder()
                .lastMeal(combinedDateTime)
                .build());

        existingFasting.setLastMeal(combinedDateTime);
        Fasting savedFasting = neo4jTemplate.save(existingFasting);

        if (visit.getFasting() == null) {
            visitRepository.linkFastingToVisit(visitId,
                savedFasting.getId());
        }
    }

    /**
     * Retrieves the existing clinical details associated with the given order ID,
     * copies the relevant properties from the provided {@code clinicalDetails} object,
     * updates the modification timestamp and user, and saves the changes.
     *
     * @param clinicalDetails The new clinical details data to apply.
     * @param orderId The ID of the order whose clinical details should be updated.
     * @throws java.util.NoSuchElementException if no order is found for the given {@code orderId}.
     */
    public void updateClinicalDetails(ClinicalDetails clinicalDetails, String orderId) {
        Order order = orderRepository.findById(orderId)
            .orElseThrow();
        ClinicalDetails existing = getClinicalDetails(order);
        BeanUtils.copyProperties(clinicalDetails,
            existing,
            "id",
            "modifiedDate",
            "createdDate",
            "modifiedBy",
            "createdBy");
        existing.setModifiedDate(ZonedDateTime.now());
        existing.setModifiedBy(session.getUserFullName());

        neo4jTemplate.saveAs(existing,
            ClinicalDetails.class);
    }

//    private void setRegisteredOrdersStatus(String healiusId, RegisteredOrderStatus status) {
//        List<RegisteredOrder> registeredOrders = registeredOrderRepository.findAllByOrder_HealiusId(healiusId);
//        registeredOrders.forEach(registeredOrder ->
//                registeredOrder.setStatus(status));
//        neo4jTemplate.saveAllAs(registeredOrders, RegisteredOrderStatusStub.class);
//    }
}
