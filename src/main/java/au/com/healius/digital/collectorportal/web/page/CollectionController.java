package au.com.healius.digital.collectorportal.web.page;

import au.com.healius.digital.collectorportal.converter.ContainerCollectedListToCollectedContainersConverter;
import au.com.healius.digital.collectorportal.dto.CCSummary;
import au.com.healius.digital.collectorportal.dto.CollectedContainers;
import au.com.healius.digital.collectorportal.dto.EditPatientDTO;
import au.com.healius.digital.collectorportal.dto.LabNumberView;
import au.com.healius.digital.collectorportal.dto.OrderSummary;
import au.com.healius.digital.collectorportal.dto.OrderView;
import au.com.healius.digital.collectorportal.dto.VisitEvent;
import au.com.healius.digital.collectorportal.dto.VisitView;
import au.com.healius.digital.collectorportal.service.CollectionOrderService;
import au.com.healius.digital.collectorportal.service.CollectionVisitService;
import au.com.healius.digital.collectorportal.service.DocumentGenerator;
import au.com.healius.digital.collectorportal.service.ThymeleafReportGenerator;
import au.com.healius.digital.collectorportal.session.CollectorSession;
import au.com.healius.digital.collectorportal.session.RequiresCollectorSession;
import au.com.healius.digital.librarycollections.domain.CancellationReason;
import au.com.healius.digital.librarycollections.domain.ClinicalDetails;
import au.com.healius.digital.librarycollections.domain.ContainerCollected;
import au.com.healius.digital.librarycollections.domain.FreeText;
import au.com.healius.digital.librarycollections.domain.LabNumber;
import au.com.healius.digital.librarycollections.domain.Order;
import au.com.healius.digital.librarycollections.domain.Specimen;
import au.com.healius.digital.librarycollections.domain.Visit;
import au.com.healius.digital.librarycollections.domain.enumeration.BillingType;
import au.com.healius.digital.librarycollections.domain.enumeration.CollectionSampleStatus;
import au.com.healius.digital.librarycollections.domain.enumeration.HealthFundCard;
import au.com.healius.digital.librarycollections.domain.enumeration.PatientEpisodeInitiated;
import au.com.healius.digital.librarycollections.domain.enumeration.PaymentState;
import au.com.healius.digital.librarycollections.domain.enumeration.PrintedDigitalOrder;
import au.com.healius.digital.librarycollections.domain.enumeration.RequestPriorityStatus;
import au.com.healius.digital.librarycollections.repository.CCRepository;
import au.com.healius.digital.librarycollections.repository.ContainerCollectedRepository;
import au.com.healius.digital.trm.domain.Company;
import au.com.healius.digital.trm.repository.CompanyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.ConversionService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.annotation.RegisteredOAuth2AuthorizedClient;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.text.DecimalFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static au.com.healius.digital.librarycollections.domain.enumeration.PathologyOrderWorkflow.E_ORDER;
import static au.com.healius.digital.librarycollections.domain.enumeration.PathologyVisitState.*;
import static au.com.healius.digital.librarycollections.domain.enumeration.RequestPriorityStatus.ROUTINE;
import static au.com.healius.digital.librarycollections.domain.enumeration.RequestPriorityStatus.URGENT;
import static java.util.Comparator.naturalOrder;

/**
 * Controller for handling collection-related operations in the Collector Portal.
 * Manages specimen collection, visit tracking, and order processing.
 * Requires collector authentication and authorization.
 */
@Log4j2
@RequiredArgsConstructor
@Controller
@PreAuthorize("hasAnyAuthority('APPROLE_Collector')")
@RequestMapping(value = "/collection")
public class CollectionController {
    public static final String REDIRECT_DASHBOARD = "redirect:/dashboard";
    private static final int MAX_LAB_NUMBERS = 5;

    private final CollectionOrderService collectionOrderService;
    private final CollectionVisitService collectionVisitService;

    private final CCRepository ccRepository;

    private final CollectorSession collectorSession;
    private final ConversionService conversionService;

    private final DocumentGenerator documentGenerator;
    private final ThymeleafReportGenerator thymeleafReportGenerator;

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private final CompanyRepository companyRepository;
    private final ContainerCollectedRepository containerCollectedRepository;
    private final ContainerCollectedListToCollectedContainersConverter containerCollectedListToCollectedContainersConverter;

    @Value(value = "${healius.digital.google-api.maps}")
    private String googleAPIMaps;

    /**
     * Retrieves the Google Maps API key from application properties.
     * @return The Google Maps API key as a String
     */
    @ModelAttribute(value = "googleAPIMaps")
    public String getGoogleKey() {
        return googleAPIMaps;
    }

    /**
     * Provides a list of cancellation reasons for collections.
     * @return List of CancellationReason objects
     */
    @ModelAttribute("cancellationReasons")
    public List<CancellationReason> cancellationReasons() {
        return collectionVisitService.getCollectionCancellationReasons();
    }

    /**
     * Provides available billing types for collections.
     * @return Array of BillingType enums
     */
    @ModelAttribute("billingTypes")
    public BillingType[] billingTypes() {
        return BillingType.values();
    }

    /**
     * Provides available health fund card types.
     * @return Array of HealthFundCard enums
     */
    @ModelAttribute("healthFundCards")
    public HealthFundCard[] healthFundCards() {
        return HealthFundCard.values();
    }

    /**
     * Provides available printed/digital order types.
     * @return Array of PrintedDigitalOrder enums
     */
    @ModelAttribute("printedDigitalOrder")
    public PrintedDigitalOrder[] printedDigitalOrder() {
        return PrintedDigitalOrder.values();
    }

    /**
     * Provides available episode locations.
     * @return Array of PatientEpisodeInitiated enums
     */
    @ModelAttribute("episodeLocations")
    public PatientEpisodeInitiated[] episodeLocations() {
        return PatientEpisodeInitiated.values();
    }

    /**
     * Provides available sample statuses.
     * @return Array of CollectionSampleStatus enums
     */
    @ModelAttribute("sampleStatuses")
    public CollectionSampleStatus[] sampleStatuses() {
        return CollectionSampleStatus.values();
    }
// TODO ADD IN NEW
//    @ModelAttribute("unknownProvider")
//    public ReferrerDTO unknownProvider() {
//        return conversionService.convert(collectionOrderService.getUnknownProvider(), ReferrerDTO.class);
//    }

    /**
     * Provides available patient episode initiation types.
     * @return Array of PatientEpisodeInitiated enums
     */
    @ModelAttribute("patientEpisodeInitiates")
    public PatientEpisodeInitiated[] patientEpisodeInitiates() {
        return PatientEpisodeInitiated.values();
    }

    /**
     * Handles NoSuchElementException by logging the error and redirecting to dashboard.
     * @param ex The exception that was thrown
     * @return Redirect string to the dashboard
     */
    @ExceptionHandler({NoSuchElementException.class})
    public String noSuchElement(Exception ex) {
        log.error("Element not found", ex);
        return REDIRECT_DASHBOARD;
    }

    /**
     * Displays the referral view for a specific Healius ID.
     * @param healiusId The Healius ID to view
     * @param submitted Optional parameter indicating if the form was submitted
     * @param uiModel The Spring MVC model for view rendering
     * @return The referral view template name
     */
    @GetMapping(value = "/ref/{healiusId}")
    @RequiresCollectorSession
    public String getReferralView(@PathVariable("healiusId") String healiusId, @RequestParam("submitted") Optional<String> submitted, Model uiModel) {
        ZoneId zoneId = collectorSession.getZoneId();

        Order order = collectionOrderService.viewOrderVisit(healiusId, collectorSession.getSelectedCC(), zoneId);

        OrderView orderView = conversionService.convert(order, OrderView.class);

        assert orderView != null;
        VisitView recentVisit = orderView.getRecentVisit();

        boolean canCollect =
                submitted.isEmpty() &&
                !recentVisit.isSubmitted();

        VisitView visitView = canCollect ? recentVisit : orderView.getVisits().stream()
                .filter(VisitView::isSubmitted)
                .max(naturalOrder())
                .orElse(recentVisit);

        visitView.setFirstVisit(!orderView.isToFollow());
        visitView.setRequestPriorityStatus(getPriority(Optional.ofNullable(order.getClinicalDetails())));
        CCSummary cc = getCC(recentVisit);
        List<VisitEvent> visitEvents = getVisitEvents(order);
        uiModel.addAttribute("cc", cc);
        uiModel.addAttribute("summary", getOrderSummary(order));
        uiModel.addAttribute("clinicalNotes", order.getClinicalDetails());
        uiModel.addAttribute("eReferral", order.getPathologyOrderWorkflow() == E_ORDER);
        uiModel.addAttribute("canCollect", canCollect);
        uiModel.addAttribute("orderView", orderView);
        uiModel.addAttribute("visitView", visitView);
        uiModel.addAttribute("visitEvents", visitEvents);

        DecimalFormat formatter = new DecimalFormat("#.00");

        uiModel.addAttribute("bPointPayment", visitView.getBPointPayment() != null && visitView.getBPointPayment().getBPointReceiptNo() != null ? visitView.getBPointPayment().getBPointReceiptNo() + ", $" + formatter.format(Float.parseFloat(visitView.getBPointPayment().getBPointAmountPaid())) : "");
        uiModel.addAttribute("buStatus", collectorSession.getSelectedCC().getActiveType().toString());
        return "referral/view";
    }

    /**
     * Generates and returns a PDF invoice for a specific order and visit.
     * @param orderId The ID of the order
     * @param visitId The ID of the visit
     * @return ResponseEntity containing the PDF file
     */
    @GetMapping(value = "/ref/{orderId}/{visitId}/invoice")
    public ResponseEntity<byte[]> getInvoicePDF(@PathVariable("orderId") String orderId, @PathVariable("visitId") String visitId) {
        Visit visitDTO = collectionVisitService.getVisit(visitId);
        Company company = companyRepository.findById(collectorSession.getSelectedCC().getCompanyId()).orElseThrow();

        String report = thymeleafReportGenerator.generatePaymentReceiptForm(collectionOrderService.getOrderById(orderId), visitDTO, company);
        byte[] contents = documentGenerator.htmlToPdf(report);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        // Here you have to set the actual filename of your pdf
        String filename = "output.pdf";
        headers.setContentDispositionFormData(filename, filename);
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        return new ResponseEntity<>(contents, headers, HttpStatus.OK);
    }

    /**
     * Displays the collection view for a specific Healius ID.
     * @param healiusId The Healius ID to collect
     * @param uiModel The Spring MVC model for view rendering
     * @param graph The OAuth2 authorized client for orderables
     * @return The collection view template name or redirect to referral view
     */
    @GetMapping(value = "/ref/{healiusId}/collecting")
    @RequiresCollectorSession
    public String getReferralCollecting(@PathVariable("healiusId") String healiusId, Model uiModel, @RegisteredOAuth2AuthorizedClient("tests") OAuth2AuthorizedClient graph) {
        ZoneId zoneId = collectorSession.getZoneId();
        CCSummary selectedCC = collectorSession.getSelectedCC();

        Visit visit = collectionOrderService.collectOrderVisit(healiusId, selectedCC, zoneId);
        if (visit == null) {
            return "redirect:/collection/ref/" + healiusId;
        }

        uiModel.addAttribute("oauth2_token", graph.getAccessToken().getTokenValue());

        Order order = collectionOrderService.getOrderByHealiusId(healiusId);
        OrderView orderView = conversionService.convert(order, OrderView.class);
        assert orderView != null;
        VisitView visitView = orderView.getRecentVisit();
        visitView.setFirstVisit(!orderView.isToFollow());


        if (!List.of(READY_TO_COLLECT, PRINTED).contains(visit.getPathologyVisitState()) && !order.getCompany().getId().equals(collectorSession.getCompany().getId())) {
            return REDIRECT_DASHBOARD;
        }


        ZonedDateTime dob = Optional.of(order.getReferredPatient().getDateOfBirth().atStartOfDay(zoneId)).orElse(ZonedDateTime.of(1900, 1, 1, 0, 0, 0, 0, zoneId));
        order.getReferredPatient().setAge((int) ChronoUnit.YEARS.between(dob, ZonedDateTime.now(zoneId)));

        String freeText = order.getFreeText().stream().map(FreeText::getTest).collect(Collectors.joining(", "));

        EditPatientDTO patient = conversionService.convert(order.getReferredPatient(), EditPatientDTO.class);

        uiModel.addAttribute("summary", getOrderSummary(order));
        uiModel.addAttribute("object", order);
        uiModel.addAttribute("patient", patient);

        if (order.getReferredPatient() != null) {
            uiModel.addAttribute("dob", order.getReferredPatient().getDateOfBirth().format(dateFormatter));
        }

        if (visit.getPatientEpisodeInitiated() == null) {
            visit.setPatientEpisodeInitiated(PatientEpisodeInitiated.C);
        }

        uiModel.addAttribute("visit", visit);
        List<ContainerCollected> domainContainers = containerCollectedRepository.findByVisitId(visit.getId());

        // Use the new converter
        CollectedContainers collectedContainers = containerCollectedListToCollectedContainersConverter.convert(
                new ContainerCollectedListToCollectedContainersConverter.Input(
                        domainContainers,
                        visit.getUnknownContainers()
                )
        );


        uiModel.addAttribute("containersCollected", collectedContainers);

        uiModel.addAttribute("clinicalNotes", order.getClinicalDetails());
        uiModel.addAttribute("freeText", freeText);
        uiModel.addAttribute("lastUsedTerminal", collectorSession.getLastUsedTerminal());
        uiModel.addAttribute("allowSquareTerminal", selectedCC.isSquareEnabled());
        uiModel.addAttribute("squareTerminals", selectedCC.getSquareTerminals());
        uiModel.addAttribute("eReferral", order.getPathologyOrderWorkflow() == E_ORDER);
        uiModel.addAttribute("workflow", order.getPathologyOrderWorkflow());

        uiModel.addAttribute("cc", getCC(visitView));
        uiModel.addAttribute("bu", collectorSession.getCompany().getCode());
        uiModel.addAttribute("buStatus", selectedCC.getActiveType().toString());

        uiModel.addAttribute("orderView", orderView);
        uiModel.addAttribute("visitView", visitView);
        uiModel.addAttribute("visitEvents", conversionService.convert(visit, List.class));

        uiModel.addAttribute("labNumbers", Stream.concat(
                        visitView.getLabNumbers().stream(), Stream.generate(() -> LabNumberView.builder().build()))
                        .limit(MAX_LAB_NUMBERS)
                .toList());

        boolean isLocked = isPaymentLocked(orderView, visitView);
        uiModel.addAttribute("isLocked", isLocked);

        uiModel.addAttribute("toFollow", Objects.requireNonNull(orderView).isToFollow());
        uiModel.addAttribute("hasPaid", isLocked);
        uiModel.addAttribute("payment", visitView.getSquarePayment());
        uiModel.addAttribute("bPointPayment", visitView.getBPointPayment());
        return "referral/edit";
    }

    private boolean isPaymentLocked(OrderView orderView, VisitView visitView) {
        // commerical visit with a bpoint payment saved
        if (visitView.getBPointPayment() != null && visitView.getBPointPayment().getBPointAmountPaid() != null) {
            return true;
        }

        // commerical visit with a bpoint payment saved
        if (visitView.getSquarePayment() != null && (
                visitView.getSquarePayment().getPaymentState() == PaymentState.COMPLETED ||
                orderView.getVisits().stream().anyMatch(VisitView::isSubmitted)
                )) {
            return true;
        }

        return false;
    }

    private OrderSummary getOrderSummary(Order order) {
        Optional<ClinicalDetails> clinicalDetails = Optional.ofNullable(order.getClinicalDetails());
        Visit visit = collectionOrderService.getRecentVisit(order, Optional.empty()).orElseThrow();
        RequestPriorityStatus visitPriority = Optional.ofNullable(visit.getRequestPriorityStatus())
                .orElseGet(() -> getPriority(clinicalDetails));
        return OrderSummary.builder()
                .priority(visitPriority)
                .pregnant(clinicalDetails.map(ClinicalDetails::isPregnant).orElse(false))
                .confidential(clinicalDetails.map(ClinicalDetails::isConfidential).orElse(false))
                .fasting(clinicalDetails.map(ClinicalDetails::isFasting).orElse(false))
                .build();
    }

    private RequestPriorityStatus getPriority(Optional<ClinicalDetails> clinicalDetails) {
        return clinicalDetails
                .map(cd -> cd.isUrgent() ? URGENT : ROUTINE)
                .orElse(ROUTINE);
    }

    private List<VisitEvent> getVisitEvents(Order order) {
        return order.getVisits().stream()
                .map(this::toVisitEvents)
                .flatMap(List::stream)
                .sorted()
                .toList();
    }

    private List<VisitEvent> toVisitEvents(Visit visit) {
        return visit.getLabNumbers().stream()
                .map(labNumber -> toVisitEvent(getCC(visit), visit, labNumber))
                .toList();
    }

    private VisitEvent toVisitEvent(CCSummary cc, Visit visit, LabNumber labNumber) {
        VisitView visitView = conversionService.convert(visit, VisitView.class);
        List<LabNumberView> labNumbers = visit.getLabNumbers().stream()
                .map(number -> conversionService.convert(number, LabNumberView.class))
                .sorted()
                .toList();
        boolean extraLabNumber = !(labNumbers.isEmpty() || labNumbers.get(0).getLabNumber().equals(labNumber.getLabNumber()));
        assert visitView != null;

        // todo: original code: visit.getPathologyVisitState() == COLLECTION_SUBMITTED || visit.getPathologyVisitState() == ABANDONED || extraLabNumber ? labNumber.getCreatedDate() : visit.getModifiedDate()
        ZonedDateTime eventTimestamp = visit.getModifiedDate();
        if (extraLabNumber) {
            eventTimestamp = labNumber.getCreatedDate();
        }

        return VisitEvent.builder()
                .visitId(visit.getId())
                .labNumber(labNumber.getLabNumber())
                .labNumberPlusYear(labNumber.getLabNumberPlusYear())
                .labNumberCheckDigit(labNumber.getLabNumberCheckDigit())
                .timestamp(eventTimestamp)
                .submitted(visit.getPathologyVisitState() == COLLECTION_SUBMITTED && !extraLabNumber)
                .cancelled(visit.getPathologyVisitState() == ABANDONED)
                .printed(visit.getPathologyVisitState() == PRINTED)
                .extraLabNumber(extraLabNumber)
                .state(visit.getPathologyVisitState().name())
                .label(visit.getPathologyVisitState().label())
                .canPrintLabels(visit.getPathologyVisitState() == COLLECTION_SUBMITTED && Objects.requireNonNull(visitView).isCanPrintLabels())
                .cc(cc)
                .collectorName(extraLabNumber ? null : visit.getCollectorName())
                .collectorNotes(visit.getCollectorNotes())
                .freeText(visit.getFreeText())
                .freeHandDeclinedToPay(visit.getFreeHandDeclinedToPay())
                .freeHandDeclinedToCollect(visit.getFreeHandDeclinedToCollect())
                .doesNotConsentToUploadEHealth(visit.getDoesNotConsentToUploadEHealth())
                .patientAgreedService(visit.isPatientAgreedService())
                .patientInformedPossibleCharge(visit.isPatientInformedPossibleCharge())
                .stampPaperInformFinancialConsent(visit.isStampPaperInformFinancialConsent())
                .writeReceiptOnPaper(visit.isWriteReceiptOnPaper())
                .finalizeJob(visit.isFinalizeJob())
                .squarePayment(visit.getSquarePayment())
                .bPointPayment(visit.getBPointPayment())
                .specimenCount(visit.getSpecimens().size()
                        + visit.getSpecimenMultiCollects().size()
                        + visit.getSpecimenMultiCollects()
                            .stream()
                            .map(specimenMultiCollect -> specimenMultiCollect.getSpecimenRequired() == null ? 1 : specimenMultiCollect.getSpecimenRequired() - 1)
                            .mapToInt(i -> i)
                            .sum()
                        + visit.getSpecimenGroups().size()
                        + visit.getSpecimenGroups().stream().map(x -> x.getOrderableGroupings().getOrderables()).toList().size()
                )
                .build();
    }

    private CCSummary getCC(VisitView visit) {
        return Optional.ofNullable(visit)
                .map(VisitView::getAccCombinedId)
            .map(ccRepository::findByCombinedId)
                .map(cc -> conversionService.convert(cc, CCSummary.class))
                .orElse(collectorSession.getSelectedCC());
    }
    private CCSummary getCC(Visit visit) {
        return Optional.ofNullable(visit)
                .map(Visit::getAccCombinedId)
            .map(ccRepository::findByCombinedId)
                .map(cc -> conversionService.convert(cc, CCSummary.class))
                .orElse(collectorSession.getSelectedCC());
    }

    private int specimenComparator(Specimen a, Specimen b) {
        if (a.isManuallyAdded() == b.isManuallyAdded() && a.getUnknownTest() == null && b.getUnknownTest() == null) {
            return a.compareTo(b);
        } else if (a.isManuallyAdded()) {
            return -1;
        }
        return 1;
    }
}
