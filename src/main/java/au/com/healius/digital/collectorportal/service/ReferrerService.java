package au.com.healius.digital.collectorportal.service;

import au.com.healius.digital.collectorportal.referrer.api.ReferrerApi;
import au.com.healius.digital.collectorportal.referrer.api.model.ReferrerResponse;
import au.com.healius.digital.collectorportal.referrer.api.invoker.ApiClient;
import com.azure.core.credential.TokenRequestContext;
import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;

/**
 * Service for handling referrer-related operations.
 * This service provides functionality for managing referrers and related entities
 * by delegating to the Referrer service API.
 */
@Service
@Log4j2
@Getter
public class ReferrerService {

    @Value("${healius.digital.referrer.api.base-url}")
    private String baseUrl;

    @Value("${spring.cloud.azure.active-directory.profile.tenant-id}")
    private String tenantId;

    @Value("${spring.cloud.azure.active-directory.credential.client-id}")
    private String clientId;

    @Value("${spring.cloud.azure.active-directory.credential.client-secret}")
    private String clientSecret;

    @Value("${spring.cloud.azure.active-directory.authorization-clients.referrer.scopes[0]}")
    private String scope;

    private String generateEntraToken() {
        ClientSecretCredential credential = new ClientSecretCredentialBuilder()
                .tenantId(tenantId)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .build();
        TokenRequestContext context = new TokenRequestContext().addScopes(scope);
        return credential.getToken(context).block().getToken();
    }

    public ApiClient getApiClient() {
        ApiClient apiClient = new ApiClient();
        apiClient.setBasePath(baseUrl);
        apiClient.addDefaultHeader("Authorization", "Bearer " + generateEntraToken());
        return apiClient;
    }

    /**
     * Search for referrers based on various criteria.
     * 
     * @param providerNumber The provider number to search for
     * @param firstName The first name to search for
     * @param lastName The last name to search for
     * @param postCode The post code to search for
     * @param city The city to search for
     * @return A Mono containing the search results
     */
    public Flux<ReferrerResponse> findReferrerBySearch(
            @Valid @RequestParam(value = "providerNumber", required = false) String providerNumber,
            @Valid @RequestParam(value = "firstName", required = false) String firstName,
            @Valid @RequestParam(value = "lastName", required = false) String lastName,
            @Valid @RequestParam(value = "postCode", required = false) String postCode,
            @Valid @RequestParam(value = "city", required = false) String city) {
        
        try {
            ReferrerApi referrerApi = new ReferrerApi(getApiClient());
            return referrerApi.findReferrerBySearch(providerNumber, firstName, lastName, postCode, city);
        } catch (WebClientResponseException e) {
            log.error("Error calling referrer service: {} - {}", e.getStatusCode(), e.getMessage());
            return Flux.empty();
        }
    }
} 