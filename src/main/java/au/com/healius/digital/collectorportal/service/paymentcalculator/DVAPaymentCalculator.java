package au.com.healius.digital.collectorportal.service.paymentcalculator;

import au.com.healius.digital.collectorportal.dto.VisitCostDTO;
import au.com.healius.digital.librarycollections.domain.Visit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static au.com.healius.digital.librarycollections.domain.enumeration.DVACardType.DVG;

/**
 * Implementation of PaymentCalculator for Department of Veterans' Affairs (DVA) payment calculations.
 * This calculator handles cost calculations for visits covered by DVA, including special handling
 * for DVA Gold Card (DVG) holders.
 */
@Component("DVA")
public class DVAPaymentCalculator implements PaymentCalculator {

    @Value("${healius.digital.collection.collectectFee}")
    private long collectionFee;

    /**
     * Calculates the visit cost for DVA patients. For DVA Gold Card holders, all costs are covered.
     * For other DVA card types, calculates costs based on rebatable and non-rebatable items.
     *
     * @param visit The visit for which to calculate costs
     * @param rebatableCount Number of rebatable items
     * @param criteriaExceptionCount Number of items with criteria exceptions
     * @param exceptionCount Number of items with general exceptions
     * @param nonRebatableCost Cost of non-rebatable items
     * @param criteriaCost Cost of items with criteria exceptions
     * @param exceptionCost Cost of items with general exceptions
     * @param unknownCompanyCost Cost of items with unknown company
     * @return VisitCostDTO containing the calculated costs
     */
    @Override
    public VisitCostDTO getVisitCost(Visit visit,
                                     int rebatableCount,
                                     int criteriaExceptionCount,
                                     int exceptionCount,
                                     long nonRebatableCost,
                                     long criteriaCost,
                                     long exceptionCost,
                                     long unknownCompanyCost){
        if (visit.getDvaCardType() == DVG) {
            return VisitCostDTO.builder().gold(true).covered(true).build();
        }

        return VisitCostDTO.builder()
                .collectionFee(this.getCollectionFee(rebatableCount, criteriaExceptionCount, exceptionCount, nonRebatableCost))
                .nonRebatable(nonRebatableCost)
                .rebatableCapVisual(unknownCompanyCost)
                .rebatablePreCapVisual(unknownCompanyCost)
                .unknownCompany(unknownCompanyCost)
                .covered(true)
                .build();
    }

    /**
     * Calculates the collection fee based on the number of tests and non-rebatable costs.
     * Collection fee is only charged if there are no rebatable tests and there are non-rebatable costs.
     *
     * @param rebatableCount Number of rebatable items
     * @param criteriaExceptionCount Number of items with criteria exceptions
     * @param exceptionCount Number of items with general exceptions
     * @param nonRebatableCost Cost of non-rebatable items
     * @return The calculated collection fee
     */
    public long getCollectionFee(int rebatableCount, int criteriaExceptionCount, int exceptionCount, long nonRebatableCost) {
        int totalTestsExcludingNonRebatable = rebatableCount + criteriaExceptionCount + exceptionCount;
        return totalTestsExcludingNonRebatable > 0 || (totalTestsExcludingNonRebatable == 0 && nonRebatableCost == 0) ? 0 : collectionFee;
    }
}


