package au.com.healius.digital.collectorportal.service;

import au.com.healius.digital.collectorportal.dto.VisitCostDTO;
import au.com.healius.digital.collectorportal.dto.VisitSpecimens;
import au.com.healius.digital.collectorportal.service.paymentcalculator.PaymentCalculator;
import au.com.healius.digital.librarycollections.domain.Order;
import au.com.healius.digital.librarycollections.domain.Specimen;
import au.com.healius.digital.librarycollections.domain.SpecimenGroup;
import au.com.healius.digital.librarycollections.domain.SpecimenLabel;
import au.com.healius.digital.librarycollections.domain.SpecimenMultiCollect;
import au.com.healius.digital.librarycollections.domain.Visit;
import au.com.healius.digital.librarycollections.domain.enumeration.BillingType;
import au.com.healius.digital.trm.domain.Orderable;
import au.com.healius.digital.trm.domain.enumeration.RebatabilityOptions;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.ToLongFunction;

import static au.com.healius.digital.librarycollections.domain.enumeration.BillingType.*;
import static au.com.healius.digital.librarycollections.domain.enumeration.CollectionSampleStatus.*;
import static au.com.healius.digital.librarycollections.domain.enumeration.HealthFundCard.OTHER;
import static au.com.healius.digital.trm.domain.enumeration.RebatabilityOptions.*;
import static java.lang.Math.min;

/**
 * Service responsible for calculating payment costs for medical specimen collections.
 * This service handles different billing types (Bulk Billed, DVA, Private, etc.) and
 * calculates costs based on specimen types, rebatability status, and collection criteria.
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class PaymentService {
    private final Map<String, PaymentCalculator> calculators;

    @Value("${healius.digital.collection.rebatableCost}")
    private long rebatableCost;

    /**
     * Calculates the total cost for a visit based on the order, visit details, and specimens collected.
     *
     * @param order The order containing billing information
     * @param visit The visit details including billing type and health fund card information
     * @param specimens The collection of specimens including groups, individuals, and multi-collect specimens
     * @return VisitCostDTO containing the calculated costs and billing information
     */
    public VisitCostDTO getVisitCost(Order order, Visit visit, VisitSpecimens specimens) {
        return getCalculator(order, visit)
                .getVisitCost(visit,
                        getRebatableCount(specimens.getGroups(), specimens.getIndividuals(), specimens.getMultiCollectSpecimens()),
                        getCriteriaCount(specimens.getGroups(), specimens.getIndividuals(), specimens.getMultiCollectSpecimens()),
                        getExceptionCount(specimens.getGroups(), specimens.getIndividuals(), specimens.getMultiCollectSpecimens()),
                        getNonRebatableCost(specimens.getGroups(), specimens.getIndividuals(), specimens.getMultiCollectSpecimens(), this::getSpecimenCost, this::getMultiDaySpecimenCost),
                        getCriteriaCost(specimens.getGroups(), specimens.getIndividuals(), specimens.getMultiCollectSpecimens()),
                        getExceptionCost(specimens.getIndividuals()),
                        getUnknownCompanyCost(specimens.getIndividuals()))
                .withBillingType(visit.getBillingType())
                .withPrivateHealthCard(visit.getHealthFundCard());
    }

    /**
     * Retrieves the appropriate payment calculator based on the order and visit billing type.
     *
     * @param order The order containing billing information
     * @param visit The visit details
     * @return The appropriate PaymentCalculator implementation
     */
    private PaymentCalculator getCalculator(Order order, Visit visit) {
        return calculators.get(getPaymentBillingType(order, visit).name());
    }

    /**
     * Determines the final billing type based on order billing type and visit card sighting status.
     * Falls back to PRIVATE billing if card sighting requirements are not met.
     *
     * @param order The order containing initial billing type
     * @param visit The visit containing card sighting information
     * @return The final billing type to be used for payment calculation
     */
    @NotNull
    private BillingType getPaymentBillingType(Order order, Visit visit) {
        BillingType billingType = order.getBillingType();
        if (
                billingType == BULK_BILLED && !visit.isMedicareCardSighted() ||
                        billingType == DVA && !visit.isDvaCardSighted() ||
                        billingType == OVERSEAS_STUDENT_VISITOR && (!visit.isPrivateHealthSighted() || visit.getHealthFundCard() == OTHER)) {
            return PRIVATE;
        }

        return Optional.ofNullable(billingType)
                .orElse(PRIVATE);
    }

    /**
     * Calculates the count of rebatable specimens from all specimen types.
     *
     * @param groups List of specimen groups
     * @param individuals List of individual specimens
     * @param multiCollectSpecimens List of multi-collect specimens
     * @return Total count of rebatable specimens
     */
    private int getRebatableCount(List<SpecimenGroup> groups, List<Specimen> individuals, List<SpecimenMultiCollect> multiCollectSpecimens) {
        return groupCount(groups, REBATABLE) + individualCount(individuals, isRebatable.and(Predicate.not(hasPrice))) + multiCollectCount(multiCollectSpecimens, REBATABLE);
    }

    /**
     * Calculates the count of exception specimens that have custom pricing.
     *
     * @param groups List of specimen groups
     * @param individuals List of individual specimens
     * @param multiCollectSpecimens List of multi-collect specimens
     * @return Total count of exception specimens
     */
    private int getExceptionCount(List<SpecimenGroup> groups, List<Specimen> individuals, List<SpecimenMultiCollect> multiCollectSpecimens) {
        return groupCount(groups, REBATABLE) + individualCount(individuals, isRebatable.and(hasPrice)) + multiCollectCount(multiCollectSpecimens, REBATABLE);
    }

    /**
     * Calculates the count of criteria-based specimens from all specimen types.
     *
     * @param groups List of specimen groups
     * @param individuals List of individual specimens
     * @param multiCollectSpecimens List of multi-collect specimens
     * @return Total count of criteria-based specimens
     */
    private int getCriteriaCount(List<SpecimenGroup> groups, List<Specimen> individuals, List<SpecimenMultiCollect> multiCollectSpecimens) {
        return groupCount(groups, CRITERIA_BASED) + individualCount(individuals, isCriteria) + multiCollectCount(multiCollectSpecimens, CRITERIA_BASED);
    }

    /**
     * Calculates the total weight of groups matching the specified rebatability option.
     *
     * @param groups List of specimen groups to count
     * @param rebatabilityOptions The rebatability option to filter by
     * @return Total weight of matching groups
     */
    private int groupCount(List<SpecimenGroup> groups, RebatabilityOptions rebatabilityOptions) {
        return groups.stream()
                .filter(groupRebatability(rebatabilityOptions))
                .map(i -> i.getOrderableGroupings().getWeight())
                .mapToInt(Integer::intValue)
                .sum();
    }

    /**
     * Counts the number of multi-collect specimens matching the specified rebatability option.
     *
     * @param multi List of multi-collect specimens
     * @param rebatabilityOptions The rebatability option to filter by
     * @return Count of matching multi-collect specimens
     */
    public int multiCollectCount(List<SpecimenMultiCollect> multi, RebatabilityOptions rebatabilityOptions) {
        return (int) multi.stream()
                .filter(r -> r.getRebatabilityState() == rebatabilityOptions)
                .count();
    }

    /**
     * Counts the number of individual specimens matching the specified filter.
     *
     * @param individuals List of individual specimens
     * @param filter Predicate to filter specimens
     * @return Count of matching individual specimens
     */
    private int individualCount(List<Specimen> individuals, Predicate<Specimen> filter) {
        return (int) individuals.stream()
                .filter(filter)
                .count();
    }

    private final Predicate<Specimen> isCollected = specimen ->
            specimen.getCollectionSampleStatus() != DECLINE_TO_COLLECT &&
                    specimen.getCollectionSampleStatus() != DECLINE_TO_PAY &&
                    specimen.getCollectionSampleStatus() != TEST_NOT_AVAILABLE;

    private final Predicate<Specimen> isRebatable = isCollected.and(rebatability(REBATABLE)).and(specimen -> !specimen.isAllowPriceOverride());

    private final Predicate<Specimen> isCriteria = isCollected.and(rebatability(CRITERIA_BASED));

    private final Predicate<Specimen> hasPrice = specimen -> specimen.getCompanyOrderablePrice() != null && specimen.getCompanyOrderablePrice() > 0;

    private Predicate<Specimen> rebatability(au.com.healius.digital.trm.domain.enumeration.RebatabilityOptions rebatabilityOptions) {
        return specimen -> Optional.ofNullable(specimen.getOrderable())
                .map(Orderable::getRebatabilityState)
                .orElse(specimen.getRebatabilityState()) == rebatabilityOptions;
    }

    private Predicate<SpecimenGroup> groupRebatability(RebatabilityOptions rebatabilityOptions) {
        return group -> group.getOrderableGroupings().getRebatability() == rebatabilityOptions;
    }

    /**
     * Calculates the total cost of non-rebatable specimens.
     *
     * @param groups List of specimen groups
     * @param individuals List of individual specimens
     * @param multiCollects List of multi-collect specimens
     * @param costGetter Function to get cost for individual specimens
     * @param multiCostGetter Function to get cost for multi-collect specimens
     * @return Total cost of non-rebatable specimens
     */
    private long getNonRebatableCost(
            List<SpecimenGroup> groups,
            List<Specimen> individuals,
            List<SpecimenMultiCollect> multiCollects,
            ToLongFunction<Specimen> costGetter,
            ToLongFunction<SpecimenMultiCollect> multiCostGetter) {

        return individuals.stream()
                .filter(rebatability(NON_REBATABLE))
                .mapToLong(costGetter).sum() +
                getNonRebatableGroupCost(groups) +
                getMultiDayRebatabilityCost(multiCollects, NON_REBATABLE, multiCostGetter);
    }

    /**
     * Calculates the total cost of non-rebatable specimen groups.
     *
     * @param groups List of specimen groups
     * @return Total cost of non-rebatable groups
     */
    private long getNonRebatableGroupCost(List<SpecimenGroup> groups) {
        return groups.stream()
                .filter(groupRebatability(NON_REBATABLE))
                .mapToLong(SpecimenGroup::getCustomPrice)
                .sum();
    }

    /**
     * Calculates the total cost of criteria-based specimens.
     *
     * @param groups List of specimen groups
     * @param individuals List of individual specimens
     * @param multi List of multi-collect specimens
     * @return Total cost of criteria-based specimens
     */
    private long getCriteriaCost(List<SpecimenGroup> groups, List<Specimen> individuals, List<SpecimenMultiCollect> multi) {
        return
                getIndividualRebatabilityCost(individuals, CRITERIA_BASED, this::getSpecimenCost) +
                        groups.stream()
                                .filter(groupRebatability(CRITERIA_BASED))
                                .map(this::getCriteriaGroupCost)
                                .mapToLong(Long::longValue)
                                .sum() +
                        getMultiDayRebatabilityCost(multi, CRITERIA_BASED, this::getMultiDaySpecimenCost);
    }

    /**
     * Calculates the cost of individual specimens matching the specified rebatability option.
     *
     * @param individuals List of individual specimens
     * @param rebatabilityOptions The rebatability option to filter by
     * @param toLongFunction Function to get cost for each specimen
     * @return Total cost of matching individual specimens
     */
    private long getIndividualRebatabilityCost(List<Specimen> individuals, RebatabilityOptions rebatabilityOptions, ToLongFunction<Specimen> toLongFunction) {
        return individuals.stream()
                .filter(rebatability(rebatabilityOptions))
                .mapToLong(toLongFunction)
                .sum();
    }

    /**
     * Calculates the cost of multi-collect specimens matching the specified rebatability option.
     *
     * @param multiCollectSpecimens List of multi-collect specimens
     * @param rebatabilityOptions The rebatability option to filter by
     * @param toLongFunction Function to get cost for each multi-collect specimen
     * @return Total cost of matching multi-collect specimens
     */
    private long getMultiDayRebatabilityCost(List<SpecimenMultiCollect> multiCollectSpecimens, RebatabilityOptions rebatabilityOptions, ToLongFunction<SpecimenMultiCollect> toLongFunction) {
        return multiCollectSpecimens.stream()
                .filter(result -> result.getRebatabilityState() == rebatabilityOptions)
                .mapToLong(toLongFunction)
                .sum();
    }

    /**
     * Calculates the cost of exception specimens that have custom pricing.
     *
     * @param individuals List of individual specimens
     * @return Total cost of exception specimens
     */
    private long getExceptionCost(List<Specimen> individuals) {
        return individuals.stream()
                .filter(rebatability(REBATABLE))
                .filter(i -> i.getCompanyOrderablePrice() > 0)
                .mapToLong(this::getSpecimenCost)
                .sum();
    }

    /**
     * Calculates the cost of specimens with unknown company information.
     *
     * @param individuals List of individual specimens
     * @return Total cost of specimens with unknown company
     */
    private long getUnknownCompanyCost(List<Specimen> individuals) {
        return individuals.stream()
                .filter(i -> i.getCompanyOrderableId() == null)
                .mapToLong(this::getCustomPrice)
                .sum();
    }

    /**
     * Calculates the cost of a criteria-based specimen group.
     * Returns the minimum of the sum of individual specimen costs or the group's custom price.
     *
     * @param group The specimen group to calculate cost for
     * @return The calculated cost for the group
     */
    private long getCriteriaGroupCost(SpecimenGroup group) {
        long specimensPriceSum = group.getSpecimens().stream()
                .mapToLong(this::getSpecimenCost)
                .sum();
        return specimensPriceSum > 0 ? min(specimensPriceSum, group.getCustomPrice()) : group.getCustomPrice();
    }

    /**
     * Calculates the cost of an individual specimen.
     * Returns rebatable cost for tier specimens, custom price for others, or 0 if not collected.
     *
     * @param specimen The specimen to calculate cost for
     * @return The calculated cost for the specimen
     */
    private long getSpecimenCost(Specimen specimen) {
        if (isCollected.test(specimen)) {
            if (specimen.isTier()) {
                    return rebatableCost;
                } else {
                    return getCustomPrice(specimen);
                }
        }
        return 0;
    }

    /**
     * Calculates the cost of a multi-collect specimen.
     * Returns rebatable cost for tier specimens, custom price if specified, or 0 if not collected.
     *
     * @param multiCollectSpecimens The multi-collect specimen to calculate cost for
     * @return The calculated cost for the multi-collect specimen
     */
    private long getMultiDaySpecimenCost(SpecimenMultiCollect multiCollectSpecimens) {
        boolean isCollected = multiCollectSpecimens.getSpecimens().stream()
                .map(SpecimenLabel::getSpecimen)
                .anyMatch(this.isCollected);

        if (isCollected) {
            if (multiCollectSpecimens.isTier()) {
                return this.rebatableCost;
            } else if (multiCollectSpecimens.getCustomPrice() > 0) {
                return multiCollectSpecimens.getCustomPrice();
            }
        }
        return 0L;
    }

    /**
     * Gets the custom price of a specimen, returning 0 if no custom price is set.
     *
     * @param specimen The specimen to get the custom price for
     * @return The custom price or 0 if not set
     */
    private long getCustomPrice(Specimen specimen) {
        return Optional.ofNullable(specimen.getCustomPrice())
                .orElse(0L);
    }
}
