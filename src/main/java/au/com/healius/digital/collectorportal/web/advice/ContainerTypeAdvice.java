package au.com.healius.digital.collectorportal.web.advice;

import au.com.healius.digital.collectorportal.session.CollectorSession;
import au.com.healius.digital.collectorportal.session.RequiresCollectorSession;
import au.com.healius.digital.collectorportal.web.page.CollectionController;
import au.com.healius.digital.collectorportal.web.page.DayBookController;
import au.com.healius.digital.trm.domain.ContainerType;
import au.com.healius.digital.trm.repository.ContainerTypeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;

import java.util.ArrayList;
import java.util.List;

/**
 * Controller advice that provides a list of available container types as a model attribute
 * for use in views handled by {@link CollectionController} and {@link DayBookController}.
 * <p>
 * This advice injects the "availableContainers" attribute into the model for relevant controllers,
 * making it accessible in the corresponding views. The list is determined based on the current collector's company.
 * </p>
 *
 * <AUTHOR> Digital
 * @version 2.0.0
 * @since 1.2.0
 */
@Log4j2
@ControllerAdvice(assignableTypes = {CollectionController.class, DayBookController.class})
@RequiredArgsConstructor

public class ContainerTypeAdvice {

    private final ContainerTypeRepository containerTypeRepository;
    private final CollectorSession collectorSession;

    /**
     * Returns a list of container types available for collection for the current collector's company.
     * This list is added to the model as the attribute "availableContainers" for use in views.
     *
     * @return List of available {@link ContainerType} objects
     */
    @RequiresCollectorSession
    @ModelAttribute("availableContainers")
    public List<ContainerType> containerTypes() {
        return containerTypeRepository.findAllContainerTypeForCollection(collectorSession.getCompany().getId());
    }
}
