package au.com.healius.digital.collectorportal.service;

/**
 * Utility class for generating SMS messages related to Square payment links.
 * This class provides functionality to format payment link messages with business unit information.
 */
public class SquareSMSParser {

    private static final String TEMPLATE =
            """

            Payment link for ${bu} ${newLine}${newLine} ${link}
            """;

    /**
     * Private constructor to prevent instantiation of this utility class.
     * @throws IllegalStateException if an attempt is made to instantiate this class
     */
    private SquareSMSParser() {
        throw new IllegalStateException("SMS Parser class");
    }

    /**
     * Generates an SMS message containing a payment link for a specific business unit.
     *
     * @param link The payment link to be included in the message
     * @param bu The business unit identifier
     * @return A formatted SMS message containing the payment link and business unit information
     */
    public static String generateSMS(String link, String bu) {
        String message = TEMPLATE.replace("${bu}",bu);
        message = message.replace("${link}",link);
        message = message.replace("${newLine}", "\n");
        return message;
    }
}
