package au.com.healius.digital.collectorportal.web.page;

import au.com.healius.digital.collectorportal.component.BarcodeGenerator;
import au.com.healius.digital.collectorportal.dto.DayBookItem;
import au.com.healius.digital.collectorportal.dto.EditPatientDTO;
import au.com.healius.digital.collectorportal.dto.PatientSearch;
import au.com.healius.digital.collectorportal.identity.api.model.ProfileLookupResponseItem;
import au.com.healius.digital.collectorportal.identity.api.model.ReadProfileResponseApiResponse;
import au.com.healius.digital.collectorportal.identity.api.model.ReadProfileResponseBody;
import au.com.healius.digital.collectorportal.service.DayBookService;
import au.com.healius.digital.collectorportal.service.IdentityService;
import au.com.healius.digital.collectorportal.service.ReferredPatientService;
import au.com.healius.digital.collectorportal.session.CollectorSession;
import au.com.healius.digital.collectorportal.session.RequiresCollectorSession;
import au.com.healius.digital.librarycollections.domain.Order;
import au.com.healius.digital.librarycollections.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

/**
 * Controller for handling patient-related operations in the Collector Portal.
 * Provides endpoints for patient lookup, search, and profile viewing.
 * Requires collector authentication and authorization.
 */
@Log4j2
@RequiredArgsConstructor
@Controller
@PreAuthorize("hasAnyAuthority('APPROLE_Collector')")
@RequestMapping(value = "/patient")
public class PatientController {
    public static final String REDIRECT_DASHBOARD = "redirect:/dashboard";

    private final ReferredPatientService patientService;
    private final DayBookService dayBookService;
    private final CollectorSession collectorSession;
    private final IdentityService identityService;
    private final OrderRepository orderRepository;

    @Value(value = "${healius.digital.google-api.maps}")
    private String googleAPIMaps;

    /**
     * Retrieves the Google Maps API key from application properties.
     * @return The Google Maps API key as a String
     */
    @ModelAttribute(value = "googleAPIMaps")
    public String getGoogleKey() {
        return googleAPIMaps;
    }

    /**
     * Handles NoSuchElementException by logging the error and redirecting to dashboard.
     *
     * @param ex The exception that was thrown
     * @return Redirect string to the dashboard
     */
    @ExceptionHandler({NoSuchElementException.class})
    public String noSuchElement(Exception ex) {
        log.error("Element not found", ex);
        return REDIRECT_DASHBOARD;
    }

    /**
     * Displays the patient lookup page for a given Healius identity.
     *
     * @param healiusIdentity The Healius identity to look up
     * @return The patient lookup view template name
     */
    @GetMapping(value = "/{healiusIdentity}")
    @RequiresCollectorSession
    public String getPatientLookup(@PathVariable String healiusIdentity) {
        return "patient/lookup";
    }

    /**
     * Searches for a patient by Healius ID and displays their profile or redirects to collection.
     *
     * @param uiModel   The Spring MVC model for view rendering
     * @param healiusId The Healius ID to search for
     * @return The patient profile view template name or redirect to collection
     */
    @GetMapping(value = "/search/{healiusId}")
    @RequiresCollectorSession
    public String getPatientLookupResults(Model uiModel, @PathVariable String healiusId) {

        if (patientService.existsByHealiusId(healiusId.toUpperCase())) {
            return "redirect:/collection/ref/" + healiusId.toUpperCase();
        }

        Optional<Order> findLastByPlacerGroup = orderRepository.findTopByPlacerGroupCombinedOrderByRequestedDateDesc(healiusId);
        if (findLastByPlacerGroup.isPresent()) {
            return "redirect:/collection/ref/" + findLastByPlacerGroup.get().getHealiusId();
        }

        Optional<ReadProfileResponseBody> identityProfileOption = identityService
                .lookupByFriendlyId(healiusId).map(ReadProfileResponseApiResponse::getPayload);

       return identityProfileOption.map(readProfileResponseBody -> {
            List<DayBookItem> dayBookItems = dayBookService.getDayBookByPatientHealiusId(collectorSession.getSelectedCC().getCombinedId(), healiusId);
            uiModel.addAttribute("items", dayBookItems);

            uiModel.addAttribute("patient", readProfileResponseBody);
            uiModel.addAttribute("patientBarcode", new BarcodeGenerator().generateBarcodeNoMargin(healiusId, 505, 120));
            uiModel.addAttribute("patientBarcodeH", new BarcodeGenerator().generateBarcodeNoMargin("H" + healiusId, 493, 120));
            return "patient/profile";
        }).orElseGet(() -> {
           uiModel.addAttribute("patients", Collections.emptyList());
           uiModel.addAttribute("eReferrals", Collections.emptyList());
           uiModel.addAttribute("patient", new EditPatientDTO());
           return "patient/results";
       });
    }

    /**
     * Searches for patients based on various criteria and displays the results.
     *
     * @param uiModel     The Spring MVC model for view rendering
     * @param familyName  The patient's family name to search for
     * @param givenName   The patient's given name to search for
     * @param mobilePhone The patient's mobile phone number to search for
     * @param medicareNo  The patient's Medicare number to search for
     * @return The patient search results view template name
     */
    @GetMapping(value = "/results")
    @RequiresCollectorSession
    public String getPatientLookupList(Model uiModel,
                                       @RequestParam(required = false) String familyName,
                                       @RequestParam(required = false) String givenName,
                                       @RequestParam(required = false) String mobilePhone,
                                       @RequestParam(required = false) String medicareNo,
                                       @RequestParam(required = false) String dateOfBirth) {
        PatientSearch search = PatientSearch.builder()
                .familyName(familyName)
                .givenName(givenName)
                .mobileNumber(mobilePhone)
                .medicareNo(medicareNo)
                .dateOfBirth(dateOfBirth)
                .build();

        List<ProfileLookupResponseItem> patientList = patientService.findPatients(search);
        List<Order> orders = patientService.findEreferralOrders(search);
        uiModel.addAttribute("patients", patientList);
        uiModel.addAttribute("eReferrals", orders);
        uiModel.addAttribute("queryString", search.toString());
        uiModel.addAttribute("patient", new EditPatientDTO());
        return "patient/results";
    }
}
