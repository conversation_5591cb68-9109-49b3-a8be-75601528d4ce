package au.com.healius.digital.collectorportal.mocking.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class RestNotificationPreference {
    @JsonProperty("type")
    private String type;

    public RestNotificationPreference() {}

    public RestNotificationPreference(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RestNotificationPreference that = (RestNotificationPreference) o;
        return Objects.equals(type, that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type);
    }

    @Override
    public String toString() {
        return "RestNotificationPreference{" +
                "type='" + type + '\'' +
                '}';
    }
} 