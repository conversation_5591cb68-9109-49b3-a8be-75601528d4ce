package au.com.healius.digital.collectorportal.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;
import java.util.Optional;

/**
 * DTO representing the lab numbers associated with a visit.
 * This class contains both the primary lab number and any additional lab numbers
 * that may be associated with the visit.
 */
@Builder
@Getter
@ToString
public class VisitLabNumbers {
    /** The primary lab number for the visit, if one exists */
    private Optional<LabNumberView> primaryLabNumber;
    
    /** List of all lab numbers associated with the visit */
    private List<LabNumberView> labNumbers;
}
