# Time-Based OHLC Candle Bar Aggregator

## Overview

The `TimeBasedOHLCBarAggregator` is a comprehensive time-based OHLC (Open, High, Low, Close) candle aggregator that implements the `MarketDataIndicator` interface. It processes `AggTradeEvent` objects and generates OHLC candles based on configurable time intervals with proper time alignment.

## Features

- ✅ **Dynamic Time Intervals**: Supports "1s", "5s", "10s", "1m", "5m", "1h", "1d" and more
- ✅ **Time-Aligned Boundaries**: Candles align with system time (e.g., 1m candles: 12:34:00-12:34:59)
- ✅ **MarketDataIndicator Interface**: Full integration with existing indicator framework
- ✅ **AggTradeEvent Processing**: Processes trade events in `onMarketData` method
- ✅ **Efficient Data Structures**: Uses CircularBuffer for rolling window of candles
- ✅ **Thread Safety**: Concurrent-safe with ReadWriteLock
- ✅ **Real-time Updates**: Reactive streams for both complete and incomplete candles
- ✅ **Volume Metrics**: Tracks total, buy, and sell volumes with VWAP calculation
- ✅ **Spring Integration**: Full integration with channels and gateways

## Architecture

### Core Components

1. **OHLCCandle Model** (`domain/model/OHLCCandle.java`)
   - Complete OHLC data structure
   - Volume metrics and trade count
   - Completion status and metadata

2. **TimeIntervalUtils** (`util/TimeIntervalUtils.java`)
   - Time interval parsing and validation
   - Time alignment calculations
   - Support for various time units

3. **TimeBasedOHLCBarAggregator** (`aggregator/TimeBasedOHLCBarAggregator.java`)
   - Main aggregator implementation
   - Thread-safe candle processing
   - Reactive stream publishing

## Configuration

### Application Properties

```properties
# Time-Based OHLC Bar Aggregator Configuration
aggregator.ohlc.timeInterval=1m
aggregator.ohlc.bufferSize=1000
```

### Supported Time Intervals

| Format | Description | Example Alignment |
|--------|-------------|-------------------|
| `1s`   | 1 second    | 12:34:01.000 - 12:34:01.999 |
| `5s`   | 5 seconds   | 12:34:00.000 - 12:34:04.999 |
| `1m`   | 1 minute    | 12:34:00.000 - 12:34:59.999 |
| `5m`   | 5 minutes   | 12:30:00.000 - 12:34:59.999 |
| `1h`   | 1 hour      | 12:00:00.000 - 12:59:59.999 |
| `1d`   | 1 day       | 00:00:00.000 - 23:59:59.999 |

## Usage Examples

### Basic Integration in Strategy

```java
@Component
public class MyStrategy {
    
    private final TimeBasedOHLCBarAggregator ohlcAggregator;
    private final IndicatorRegistry indicatorRegistry;
    
    @PostConstruct
    public void initialize() {
        // Register with indicator registry
        indicatorRegistry.register(ohlcAggregator);
        
        // Subscribe to candle updates
        ohlcAggregator.getStream().subscribe(this::onCandleUpdate);
    }
    
    private void onCandleUpdate(OHLCCandle candle) {
        if (candle.isComplete()) {
            // Process completed candle for trading decisions
            analyzeCompletedCandle(candle);
        } else {
            // Monitor real-time candle formation
            monitorIncompleteCandle(candle);
        }
    }
}
```

### Accessing Candle Data

```java
// Get current incomplete candle
OHLCCandle current = aggregator.getCurrentCandle();

// Get all completed candles
List<OHLCCandle> completed = aggregator.getCompletedCandles();

// Get recent N candles
List<OHLCCandle> recent = aggregator.getRecentCandles(10);

// Get last completed candle
OHLCCandle last = aggregator.getLastCompletedCandle();
```

### Manual Candle Completion

```java
// Force complete current candle (useful for testing)
OHLCCandle completed = aggregator.forceCompleteCurrentCandle();

// Clear completed candles buffer
aggregator.clearCompletedCandles();
```

### Statistics and Monitoring

```java
// Get aggregator statistics
String stats = aggregator.getStatistics();
// Output: "TimeBasedOHLCBarAggregator Stats: completed=45, current=[trades=12, volume=1.5], interval=1m"

// Get configuration info
String config = aggregator.getConfigInfo();
// Output: "TimeBasedOHLCBarAggregator[interval=1m, duration=PT1M, bufferSize=1000]"

// Check if candles exist
boolean hasCandles = aggregator.hasCompletedCandles();
```

## OHLC Candle Structure

```java
OHLCCandle {
    Instant openTime;           // Candle period start time
    Instant closeTime;          // Candle period end time
    String symbol;              // Trading symbol (e.g., "BTCUSDT")
    BigDecimal open;            // Opening price
    BigDecimal high;            // Highest price
    BigDecimal low;             // Lowest price
    BigDecimal close;           // Closing price
    BigDecimal vwap;            // Volume-weighted average price
    BigDecimal totalVolume;     // Total volume traded
    BigDecimal buyVolume;       // Buy-side volume
    BigDecimal sellVolume;      // Sell-side volume
    int tradeCount;             // Number of trades
    String timeInterval;        // Time interval (e.g., "1m")
    boolean isComplete;         // Completion status
    String metadata;            // Additional metadata
}
```

## Spring Integration

### Message Channels

The aggregator integrates with Spring Integration channels:

```java
// Receives AggTradeEvent messages
@ServiceActivator(inputChannel = "aggTradeChannel")
public void handleAggTrade(Message<AggTradeEvent> message)

// Publishes completed candles
@Gateway(requestChannel = "ohlcCandleChannel")
void publish(OHLCCandle candle);
```

### Channel Configuration

```java
@Bean
public MessageChannel ohlcCandleChannel() {
    return new DirectChannel();
}
```

## Time Alignment Examples

### 1-Minute Candles
- Trade at `12:34:23.456` → Candle period: `12:34:00.000` to `12:34:59.999`
- Trade at `12:35:01.123` → New candle period: `12:35:00.000` to `12:35:59.999`

### 5-Minute Candles
- Trade at `12:34:23.456` → Candle period: `12:30:00.000` to `12:34:59.999`
- Trade at `12:37:45.789` → Same candle period: `12:35:00.000` to `12:39:59.999`

### 1-Hour Candles
- Trade at `12:34:23.456` → Candle period: `12:00:00.000` to `12:59:59.999`
- Trade at `13:05:30.123` → New candle period: `13:00:00.000` to `13:59:59.999`

## Performance Considerations

- **CircularBuffer**: Efficient O(1) operations for candle storage
- **ReadWriteLock**: Optimized for concurrent read access
- **Reactive Streams**: Non-blocking candle updates
- **Memory Management**: Fixed-size buffer prevents memory leaks
- **Time Complexity**: O(1) for trade processing, O(n) for VWAP calculation

## Testing

Comprehensive unit tests are provided in `TimeBasedOHLCBarAggregatorTest.java`:

```bash
# Run tests
mvn test -Dtest=TimeBasedOHLCBarAggregatorTest
```

Test coverage includes:
- Time interval parsing and validation
- OHLC calculation accuracy
- Volume breakdown (buy/sell)
- Time alignment verification
- Candle completion logic
- Reactive stream behavior
- Thread safety scenarios

## Error Handling

The aggregator handles various error scenarios:

- **Invalid Trade Events**: Null or malformed trades are logged and ignored
- **Time Parsing Errors**: Invalid intervals throw `IllegalArgumentException`
- **Concurrent Access**: Thread-safe operations with proper locking
- **Gateway Failures**: Publishing errors are logged but don't stop processing

## Integration with Existing System

The aggregator seamlessly integrates with the existing trading system:

1. **IndicatorRegistry**: Registers as a market data indicator
2. **Spring Integration**: Uses existing channel infrastructure
3. **MarketDataGateway**: Publishes candles through gateway
4. **AggTradeEvent**: Processes existing trade event format
5. **Reactive Streams**: Compatible with existing stream processing

## Future Enhancements

Potential improvements and extensions:

- **Multiple Timeframes**: Support multiple intervals simultaneously
- **Persistence**: Database storage for historical candles
- **Compression**: Efficient storage for large datasets
- **Metrics**: Prometheus/Micrometer integration
- **Backfill**: Historical data processing capabilities
- **Custom Intervals**: Support for non-standard intervals
