#!/usr/bin/env python3
"""
Troubleshooting script for the Adaptive Bar Chart Viewer.
This script helps diagnose common issues and provides solutions.
"""

import sys
import os
import subprocess
import socket
import platform

def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    print(f"   Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ❌ Python 3.8+ is required")
        return False
    else:
        print("   ✅ Python version is compatible")
        return True

def check_pip():
    """Check if pip is available."""
    print("\n📦 Checking pip...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   ✅ {result.stdout.strip()}")
            return True
        else:
            print(f"   ❌ pip check failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ pip not available: {e}")
        return False

def check_requirements_file():
    """Check if requirements.txt exists."""
    print("\n📄 Checking requirements.txt...")
    if os.path.exists("requirements.txt"):
        print("   ✅ requirements.txt found")
        with open("requirements.txt", "r") as f:
            requirements = f.read().strip().split('\n')
            print(f"   📋 {len(requirements)} packages listed")
            for req in requirements[:5]:  # Show first 5
                print(f"      - {req}")
            if len(requirements) > 5:
                print(f"      ... and {len(requirements) - 5} more")
        return True
    else:
        print("   ❌ requirements.txt not found")
        return False

def check_installed_packages():
    """Check if required packages are installed."""
    print("\n📚 Checking installed packages...")
    
    required_packages = [
        'flask',
        'flask-socketio', 
        'websocket-client',
        'plotly',
        'pandas'
    ]
    
    all_installed = True
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - NOT INSTALLED")
            all_installed = False
    
    return all_installed

def check_port_availability():
    """Check if port 5000 is available."""
    print("\n🔌 Checking port 5000...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("   ❌ Port 5000 is already in use")
            print("   💡 Try: netstat -ano | findstr :5000 (Windows) or lsof -i :5000 (Mac/Linux)")
            return False
        else:
            print("   ✅ Port 5000 is available")
            return True
    except Exception as e:
        print(f"   ❌ Error checking port: {e}")
        return False

def check_java_websocket():
    """Check if Java WebSocket endpoint is reachable."""
    print("\n☕ Checking Java WebSocket endpoint...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('localhost', 8080))
        sock.close()
        
        if result == 0:
            print("   ✅ Java server is running on port 8080")
            return True
        else:
            print("   ❌ Java server is not reachable on port 8080")
            print("   💡 Make sure your Java trading bot is running")
            return False
    except Exception as e:
        print(f"   ❌ Error checking Java server: {e}")
        return False

def check_firewall():
    """Check for common firewall issues."""
    print("\n🔥 Checking firewall/network...")
    
    system = platform.system()
    print(f"   🖥️  Operating System: {system}")
    
    if system == "Windows":
        print("   💡 If connection fails, try:")
        print("      - Disable Windows Firewall temporarily")
        print("      - Run as Administrator")
        print("      - Check Windows Defender settings")
    elif system == "Darwin":  # macOS
        print("   💡 If connection fails, try:")
        print("      - Check macOS Firewall in System Preferences")
        print("      - Allow Python in firewall settings")
    else:  # Linux
        print("   💡 If connection fails, try:")
        print("      - Check iptables: sudo iptables -L")
        print("      - Check ufw: sudo ufw status")

def provide_solutions():
    """Provide common solutions."""
    print("\n" + "="*50)
    print("🔧 COMMON SOLUTIONS")
    print("="*50)
    
    print("\n1️⃣  Install missing packages:")
    print("   pip install -r requirements.txt")
    
    print("\n2️⃣  If port 5000 is busy:")
    print("   # Windows:")
    print("   netstat -ano | findstr :5000")
    print("   taskkill /PID <PID> /F")
    print("   # Mac/Linux:")
    print("   lsof -i :5000")
    print("   kill -9 <PID>")
    
    print("\n3️⃣  Try different port:")
    print("   python app.py --port 5001")
    
    print("\n4️⃣  Run test server:")
    print("   python test_server.py")
    
    print("\n5️⃣  Check Java bot:")
    print("   Make sure Java trading bot is running on port 8080")
    print("   Test: http://localhost:8080")

def main():
    """Main troubleshooting function."""
    print("🔍 ADAPTIVE BAR CHART VIEWER TROUBLESHOOTER")
    print("="*50)
    
    issues = []
    
    # Run all checks
    if not check_python_version():
        issues.append("Python version")
    
    if not check_pip():
        issues.append("pip")
    
    if not check_requirements_file():
        issues.append("requirements.txt")
    
    if not check_installed_packages():
        issues.append("missing packages")
    
    if not check_port_availability():
        issues.append("port 5000")
    
    if not check_java_websocket():
        issues.append("Java WebSocket")
    
    check_firewall()
    
    # Summary
    print("\n" + "="*50)
    print("📊 DIAGNOSIS SUMMARY")
    print("="*50)
    
    if not issues:
        print("✅ All checks passed! The server should work.")
        print("   Try running: python test_server.py")
    else:
        print(f"❌ Found {len(issues)} issue(s):")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    
    provide_solutions()

if __name__ == "__main__":
    main()
