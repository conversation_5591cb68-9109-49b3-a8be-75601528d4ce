# Adaptive Bar Chart Viewer

A real-time web application for visualizing adaptive volume bars from the Java trading bot. This Python Flask application connects to the Java WebSocket endpoint and displays interactive charts with live market data.

## Features

- 📊 **Real-time Charts**: Interactive candlestick charts using Plotly.js
- 🔄 **Live Updates**: WebSocket connection to Java trading bot
- 📈 **Market Metrics**: Display of TPS, volatility, and volume thresholds
- 📋 **Data Table**: Recent bars with detailed information
- 🎨 **Dark Theme**: Professional trading interface
- 📱 **Responsive**: Works on desktop and mobile devices

## Prerequisites

- Python 3.8 or higher
- Java trading bot running on `localhost:8080`
- Internet connection (for CDN resources)

## Quick Start

### 1. Install Dependencies

```bash
cd python-chart-viewer
pip install -r requirements.txt
```

### 2. Start the Application

```bash
python run.py
```

Or directly:

```bash
python app.py
```

### 3. Open in Browser

Navigate to: `http://localhost:5000`

## Architecture

```
┌─────────────────┐    WebSocket     ┌─────────────────┐    HTTP/WebSocket    ┌─────────────────┐
│   Java Bot      │ ───────────────> │  Python Flask   │ ──────────────────> │   Web Browser   │
│   (Port 8080)   │                  │   (Port 5000)   │                     │                 │
└─────────────────┘                  └─────────────────┘                     └─────────────────┘
```

### Data Flow

1. **Java Bot** → Generates adaptive bars and streams via WebSocket
2. **Python Flask** → Receives bars, stores in memory, serves web interface
3. **Web Browser** → Displays real-time charts and data tables

## Configuration

### Java WebSocket URL

The default configuration connects to:
```
ws://localhost:8080/ws/adaptive-bars
```

To change this, modify the `JavaWebSocketClient` URL in `app.py`:

```python
java_ws_client = JavaWebSocketClient("ws://your-java-host:port/ws/adaptive-bars")
```

### Flask Server Settings

Default settings:
- **Host**: `0.0.0.0` (all interfaces)
- **Port**: `5000`
- **Debug**: `False` in production

## API Endpoints

### REST API

- `GET /` - Main dashboard page
- `GET /api/bars` - Get all bars as JSON
- `GET /api/chart` - Get chart data as Plotly JSON
- `GET /api/status` - Get connection and data status

### WebSocket Events

#### From Client to Server
- `connect` - Client connection
- `disconnect` - Client disconnection
- `request_chart_update` - Request chart refresh

#### From Server to Client
- `status` - Connection and data status
- `new_bar` - New adaptive bar received
- `welcome` - Welcome message with initial data
- `chart_update` - Updated chart data
- `error` - Error messages

## Data Structure

### Adaptive Bar Data
```json
{
  "openTime": "2024-01-01T12:00:00Z",
  "closeTime": "2024-01-01T12:01:30Z",
  "symbol": "BTCUSDT",
  "openPrice": 50000.00,
  "highPrice": 50100.00,
  "lowPrice": 49900.00,
  "closePrice": 50050.00,
  "volumeWeightedAveragePrice": 50025.00,
  "totalVolumeTraded": 12.5,
  "aggressiveBuyVolume": 8.2,
  "aggressiveSellVolume": 4.3,
  "tradeCount": 45,
  "closingReason": "ADAPTIVE_VOLUME_THRESHOLD",
  "marketConditionSnapshotAtClose": {
    "rollingVolatility": 85.4,
    "tradesPerSecond": 3.2,
    "recentVolumeRate": 0.28,
    "dynamicVolumeThreshold": 10.0
  }
}
```

## Features Detail

### Real-time Charts
- **Candlestick Chart**: OHLC data with green/red coloring
- **Volume Bars**: Secondary y-axis showing volume
- **Auto-refresh**: Configurable automatic updates
- **Interactive**: Zoom, pan, hover tooltips

### Market Metrics Dashboard
- **Total Bars**: Count of received bars
- **Current TPS**: Trades per second
- **Volatility**: Rolling volatility metric
- **Volume Threshold**: Current dynamic threshold

### Data Table
- **Recent Bars**: Last 10 bars with full details
- **Real-time Updates**: Automatically updates with new data
- **Formatted Display**: Color-coded high/low prices

## Troubleshooting

### Connection Issues

1. **Java Bot Not Running**
   ```
   Error: WebSocket connection failed
   ```
   - Ensure Java trading bot is running on port 8080
   - Check WebSocket endpoint: `ws://localhost:8080/ws/adaptive-bars`

2. **Port Already in Use**
   ```
   Error: Address already in use
   ```
   - Change Flask port in `app.py` or kill existing process

3. **Missing Dependencies**
   ```
   ImportError: No module named 'flask'
   ```
   - Run: `pip install -r requirements.txt`

### Data Issues

1. **No Data Displayed**
   - Check Java bot is generating adaptive bars
   - Verify WebSocket connection status
   - Check browser console for errors

2. **Chart Not Loading**
   - Ensure internet connection (for Plotly.js CDN)
   - Check browser compatibility
   - Verify JSON data format

## Development

### Running in Development Mode

```bash
export FLASK_ENV=development
python app.py
```

### Adding New Features

1. **New Chart Types**: Modify `get_chart()` function in `app.py`
2. **Additional Metrics**: Update WebSocket message handling
3. **UI Improvements**: Edit `templates/index.html`

### Testing

```bash
# Test WebSocket connection
python -c "
import websocket
ws = websocket.create_connection('ws://localhost:8080/ws/adaptive-bars')
print('Connected successfully')
ws.close()
"
```

## Performance

- **Memory Usage**: Stores last 1000 bars in memory
- **Update Frequency**: Real-time updates via WebSocket
- **Chart Rendering**: Client-side with Plotly.js
- **Concurrent Users**: Supports multiple browser connections

## Security Notes

- **CORS**: Currently allows all origins (`*`) for development
- **Authentication**: No authentication implemented
- **HTTPS**: Not configured (use reverse proxy for production)

For production deployment, consider:
- Implementing authentication
- Using HTTPS with proper certificates
- Restricting CORS origins
- Adding rate limiting

## License

This project is part of the Java trading bot system and follows the same licensing terms.
