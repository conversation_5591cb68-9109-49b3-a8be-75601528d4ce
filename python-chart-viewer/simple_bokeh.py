#!/usr/bin/env python3
"""
Simple Bokeh Chart Viewer for Adaptive Bars
Minimal implementation to get charts working first.
"""

import json
import logging
import threading
import time
from datetime import datetime, timezone
from collections import deque
from typing import Dict, List, Optional

import pandas as pd
import numpy as np
import websocket
from flask import Flask, render_template_string

from bokeh.plotting import figure
from bokeh.models import ColumnDataSource
from bokeh.layouts import column
from bokeh.embed import components
from bokeh.resources import CDN

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app
app = Flask(__name__)

# Global data storage
bars_data = deque(maxlen=100)
connection_status = "Disconnected"

class JavaWebSocketClient:
    def __init__(self, url: str):
        self.url = url
        self.ws = None
        self.running = False
    
    def on_message(self, ws, message):
        global bars_data, connection_status
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'ADAPTIVE_BAR':
                bar_data = data.get('data')
                if bar_data:
                    bars_data.append(bar_data)
                    logger.info(f"Added bar: {bar_data.get('symbol')} - Total bars: {len(bars_data)}")
            
            elif message_type == 'WELCOME':
                welcome_data = data.get('data', {})
                logger.info(f"Connected: {welcome_data.get('message')}")
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    def on_error(self, ws, error):
        global connection_status
        logger.error(f"WebSocket error: {error}")
        connection_status = "Error"
    
    def on_close(self, ws, close_status_code, close_msg):
        global connection_status
        logger.info("WebSocket connection closed")
        connection_status = "Disconnected"
    
    def on_open(self, ws):
        global connection_status
        logger.info("WebSocket connection opened")
        connection_status = "Connected"
    
    def connect(self):
        if not self.running:
            return
        
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            logger.info(f"Connecting to: {self.url}")
            self.ws.run_forever()
            
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
    
    def start(self):
        self.running = True
        thread = threading.Thread(target=self.connect, daemon=True)
        thread.start()
        return thread

def create_simple_chart():
    """Create a simple Bokeh chart."""
    global bars_data
    
    if not bars_data:
        # Empty chart
        p = figure(
            title="Adaptive Volume Bars - No Data",
            x_axis_type='datetime',
            width=1000,
            height=400
        )
        p.text(x=[datetime.now()], y=[0], text=["Waiting for data..."], text_align="center")
        return p
    
    # Convert to DataFrame
    df = pd.DataFrame(list(bars_data))
    logger.info(f"Creating chart with {len(df)} bars")
    logger.info(f"Columns: {df.columns.tolist()}")
    
    # Convert data types
    df['datetime'] = pd.to_datetime(df['closeTime'])
    for col in ['openPrice', 'highPrice', 'lowPrice', 'closePrice', 'totalVolumeTraded']:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Create chart
    p = figure(
        title=f"Adaptive Volume Bars - {df['symbol'].iloc[-1]} ({len(df)} bars)",
        x_axis_type='datetime',
        width=1000,
        height=400,
        tools="pan,wheel_zoom,box_zoom,reset"
    )
    
    # Simple line chart first
    p.line(df['datetime'], df['closePrice'], line_width=2, color='blue', legend_label="Close Price")
    p.circle(df['datetime'], df['closePrice'], size=4, color='blue', alpha=0.6)
    
    # Style
    p.title.text_font_size = "14pt"
    p.legend.location = "top_left"
    p.yaxis.axis_label = "Price"
    p.xaxis.axis_label = "Time"
    
    return p

@app.route('/')
def index():
    """Main page with simple chart."""
    global bars_data, connection_status
    
    chart = create_simple_chart()
    script, div = components(chart)
    
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Adaptive Bar Viewer</title>
        {{ bokeh_css|safe }}
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: #f0f0f0; padding: 10px; margin-bottom: 20px; }
            .status { color: {{ 'green' if status == 'Connected' else 'red' }}; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Simple Adaptive Bar Viewer</h1>
            <p>Status: <span class="status">{{ status }}</span> | Bars: {{ bar_count }}</p>
            <button onclick="location.reload()">Refresh</button>
        </div>
        
        {{ div|safe }}
        
        {{ bokeh_js|safe }}
        {{ script|safe }}
    </body>
    </html>
    """
    
    return render_template_string(
        html_template,
        script=script,
        div=div,
        bokeh_css=CDN.render_css(),
        bokeh_js=CDN.render_js(),
        status=connection_status,
        bar_count=len(bars_data)
    )

@app.route('/debug')
def debug():
    """Debug endpoint."""
    global bars_data, connection_status
    
    return {
        'status': connection_status,
        'bar_count': len(bars_data),
        'sample_data': list(bars_data)[-3:] if bars_data else []
    }

if __name__ == '__main__':
    # Start WebSocket client
    logger.info("Starting simple Bokeh chart viewer...")
    client = JavaWebSocketClient("ws://localhost:8080/ws/adaptive-bars")
    client.start()
    
    # Start Flask
    logger.info("Starting Flask server on http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=False)
