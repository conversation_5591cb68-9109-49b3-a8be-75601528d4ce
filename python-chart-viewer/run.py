#!/usr/bin/env python3
"""
Startup script for the Adaptive Bar Chart Viewer.
This script sets up the environment and starts the Flask application.
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if required packages are installed."""
    try:
        import flask
        import flask_socketio
        import websocket
        import plotly
        import pandas
        logger.info("All required packages are available")
        return True
    except ImportError as e:
        logger.error(f"Missing required package: {e}")
        return False

def install_requirements():
    """Install required packages from requirements.txt."""
    logger.info("Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False

def main():
    """Main startup function."""
    logger.info("Starting Adaptive Bar Chart Viewer...")
    
    # Check if we're in the right directory
    if not os.path.exists("requirements.txt"):
        logger.error("requirements.txt not found. Please run this script from the python-chart-viewer directory.")
        sys.exit(1)
    
    # Check requirements
    if not check_requirements():
        logger.info("Some packages are missing. Attempting to install...")
        if not install_requirements():
            logger.error("Failed to install requirements. Please install manually:")
            logger.error("pip install -r requirements.txt")
            sys.exit(1)
    
    # Import and run the app
    try:
        from app import app, socketio, java_ws_client
        
        logger.info("Starting Java WebSocket client...")
        java_ws_client.start()
        
        logger.info("Starting Flask-SocketIO server on http://localhost:5000")
        logger.info("Make sure your Java trading bot is running on http://localhost:8080")
        
        socketio.run(app, host='0.0.0.0', port=5000, debug=False, use_reloader=False)
        
    except ImportError as e:
        logger.error(f"Failed to import app: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Shutting down...")
        java_ws_client.stop()
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
