#!/usr/bin/env python3
"""
Adaptive Bar Chart Viewer
A Flask web application that connects to the Java trading bot's WebSocket endpoint
to display real-time adaptive volume bars in interactive charts.
"""

import json
import logging
import threading
import time
from datetime import datetime, timezone
from collections import deque
from typing import Dict, List, Optional

import pandas as pd
import plotly.graph_objs as go
import plotly.utils
import websocket
from flask import Flask, render_template, jsonify
from flask_socketio import SocketIO, emit

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app configuration
app = Flask(__name__)
app.config['SECRET_KEY'] = 'adaptive-bar-viewer-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global data storage
class AdaptiveBarStore:
    def __init__(self, max_bars: int = 1000):
        self.max_bars = max_bars
        self.bars: deque = deque(maxlen=max_bars)
        self.market_conditions: Optional[Dict] = None
        self.connection_status = "Disconnected"
        self.last_update = None
        self.lock = threading.Lock()
    
    def add_bar(self, bar_data: Dict):
        with self.lock:
            self.bars.append(bar_data)
            self.last_update = datetime.now(timezone.utc)
            logger.info(f"Added bar: {bar_data.get('symbol')} at {bar_data.get('closeTime')}")
    
    def update_market_conditions(self, conditions: Dict):
        with self.lock:
            self.market_conditions = conditions
    
    def set_connection_status(self, status: str):
        with self.lock:
            self.connection_status = status
    
    def get_bars_df(self) -> pd.DataFrame:
        with self.lock:
            if not self.bars:
                return pd.DataFrame()
            
            bars_list = list(self.bars)
            df = pd.DataFrame(bars_list)
            
            # Convert timestamp columns
            if 'openTime' in df.columns:
                df['openTime'] = pd.to_datetime(df['openTime'])
            if 'closeTime' in df.columns:
                df['closeTime'] = pd.to_datetime(df['closeTime'])
            
            return df
    
    def get_status(self) -> Dict:
        with self.lock:
            return {
                'connection_status': self.connection_status,
                'total_bars': len(self.bars),
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'market_conditions': self.market_conditions
            }

# Global store instance
bar_store = AdaptiveBarStore()

class JavaWebSocketClient:
    def __init__(self, url: str):
        self.url = url
        self.ws = None
        self.running = False
        self.reconnect_interval = 5
    
    def on_message(self, ws, message):
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'ADAPTIVE_BAR':
                bar_data = data.get('data')
                if bar_data:
                    bar_store.add_bar(bar_data)
                    # Emit to web clients
                    socketio.emit('new_bar', bar_data)
            
            elif message_type == 'WELCOME':
                welcome_data = data.get('data', {})
                logger.info(f"Connected to Java WebSocket: {welcome_data.get('message')}")
                
                # Update market conditions
                conditions = welcome_data.get('currentMarketConditions')
                if conditions:
                    bar_store.update_market_conditions(conditions)
                
                # Emit welcome to web clients
                socketio.emit('welcome', welcome_data)
            
            elif message_type == 'ERROR':
                logger.error(f"Java WebSocket error: {data.get('data')}")
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse WebSocket message: {e}")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
    
    def on_error(self, ws, error):
        logger.error(f"WebSocket error: {error}")
        bar_store.set_connection_status("Error")
    
    def on_close(self, ws, close_status_code, close_msg):
        logger.info("WebSocket connection closed")
        bar_store.set_connection_status("Disconnected")
        
        # Attempt to reconnect
        if self.running:
            logger.info(f"Attempting to reconnect in {self.reconnect_interval} seconds...")
            time.sleep(self.reconnect_interval)
            self.connect()
    
    def on_open(self, ws):
        logger.info("WebSocket connection opened")
        bar_store.set_connection_status("Connected")
        
        # Send a test message
        test_message = {"type": "CLIENT_HELLO", "timestamp": datetime.now().isoformat()}
        ws.send(json.dumps(test_message))
    
    def connect(self):
        if not self.running:
            return
        
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            logger.info(f"Connecting to Java WebSocket: {self.url}")
            self.ws.run_forever()
            
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            bar_store.set_connection_status("Connection Failed")
    
    def start(self):
        self.running = True
        thread = threading.Thread(target=self.connect, daemon=True)
        thread.start()
        return thread
    
    def stop(self):
        self.running = False
        if self.ws:
            self.ws.close()

# WebSocket client instance
java_ws_client = JavaWebSocketClient("ws://localhost:8080/ws/adaptive-bars")

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('index.html')

@app.route('/api/bars')
def get_bars():
    """API endpoint to get all bars as JSON."""
    df = bar_store.get_bars_df()
    if df.empty:
        return jsonify([])
    
    # Convert DataFrame to JSON-serializable format
    bars = df.to_dict('records')
    
    # Convert datetime objects to ISO strings
    for bar in bars:
        if 'openTime' in bar and pd.notna(bar['openTime']):
            bar['openTime'] = bar['openTime'].isoformat()
        if 'closeTime' in bar and pd.notna(bar['closeTime']):
            bar['closeTime'] = bar['closeTime'].isoformat()
    
    return jsonify(bars)

@app.route('/api/chart')
def get_chart():
    """API endpoint to get chart data as Plotly JSON."""
    df = bar_store.get_bars_df()
    
    if df.empty:
        return jsonify({
            'data': [],
            'layout': {
                'title': 'No Data Available',
                'xaxis': {'title': 'Time'},
                'yaxis': {'title': 'Price'}
            }
        })
    
    # Create OHLC candlestick chart
    fig = go.Figure()
    
    # Add candlestick trace
    fig.add_trace(go.Candlestick(
        x=df['closeTime'],
        open=df['openPrice'],
        high=df['highPrice'],
        low=df['lowPrice'],
        close=df['closePrice'],
        name='Adaptive Bars',
        increasing_line_color='green',
        decreasing_line_color='red'
    ))
    
    # Add volume bar chart on secondary y-axis
    fig.add_trace(go.Bar(
        x=df['closeTime'],
        y=df['totalVolumeTraded'],
        name='Volume',
        yaxis='y2',
        opacity=0.3,
        marker_color='blue'
    ))
    
    # Update layout
    fig.update_layout(
        title=f'Adaptive Volume Bars - {df["symbol"].iloc[-1] if not df.empty else "Unknown"}',
        xaxis_title='Time',
        yaxis_title='Price',
        yaxis2=dict(
            title='Volume',
            overlaying='y',
            side='right'
        ),
        template='plotly_dark',
        height=600,
        showlegend=True
    )
    
    return json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)

@app.route('/api/status')
def get_status():
    """API endpoint to get connection and data status."""
    return jsonify(bar_store.get_status())

@socketio.on('connect')
def handle_connect():
    """Handle client connection to Flask-SocketIO."""
    logger.info('Client connected to Flask-SocketIO')
    emit('status', bar_store.get_status())

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection from Flask-SocketIO."""
    logger.info('Client disconnected from Flask-SocketIO')

@socketio.on('request_chart_update')
def handle_chart_update_request():
    """Handle client request for chart update."""
    try:
        chart_json = get_chart()
        emit('chart_update', {'chart': json.loads(chart_json)})
    except Exception as e:
        logger.error(f"Error generating chart update: {e}")
        emit('error', {'message': str(e)})

if __name__ == '__main__':
    # Start Java WebSocket client
    logger.info("Starting Java WebSocket client...")
    java_ws_client.start()
    
    # Start Flask-SocketIO server
    logger.info("Starting Flask-SocketIO server...")
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, use_reloader=False)
