#!/usr/bin/env python3
"""
Simple test server to diagnose connection issues.
This minimal Flask app helps identify if the problem is with dependencies,
port conflicts, or other configuration issues.
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """Test if all required packages can be imported."""
    print("Testing imports...")
    
    try:
        import flask
        print(f"✓ Flask {flask.__version__} imported successfully")
    except ImportError as e:
        print(f"✗ Flask import failed: {e}")
        return False
    
    try:
        import flask_socketio
        print(f"✓ Flask-SocketIO imported successfully")
    except ImportError as e:
        print(f"✗ Flask-SocketIO import failed: {e}")
        return False
    
    try:
        import websocket
        print(f"✓ WebSocket-client imported successfully")
    except ImportError as e:
        print(f"✗ WebSocket-client import failed: {e}")
        return False
    
    try:
        import plotly
        print(f"✓ Plotly {plotly.__version__} imported successfully")
    except ImportError as e:
        print(f"✗ Plotly import failed: {e}")
        return False
    
    try:
        import pandas
        print(f"✓ Pandas {pandas.__version__} imported successfully")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False
    
    return True

def test_port():
    """Test if port 5000 is available."""
    import socket
    
    print("\nTesting port availability...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("✗ Port 5000 is already in use!")
            print("  Try killing any existing Flask processes or use a different port")
            return False
        else:
            print("✓ Port 5000 is available")
            return True
    except Exception as e:
        print(f"✗ Error testing port: {e}")
        return False

def create_simple_app():
    """Create a minimal Flask app for testing."""
    try:
        from flask import Flask, jsonify
        
        app = Flask(__name__)
        
        @app.route('/')
        def home():
            return f"""
            <html>
            <head><title>Test Server</title></head>
            <body>
                <h1>🎉 Flask Server is Working!</h1>
                <p>Time: {datetime.now()}</p>
                <p>If you see this, the basic Flask server is running correctly.</p>
                <p><a href="/test">Test JSON endpoint</a></p>
            </body>
            </html>
            """
        
        @app.route('/test')
        def test():
            return jsonify({
                'status': 'success',
                'message': 'Flask server is working correctly',
                'timestamp': datetime.now().isoformat()
            })
        
        return app
    except Exception as e:
        print(f"✗ Error creating Flask app: {e}")
        traceback.print_exc()
        return None

def main():
    """Main test function."""
    print("=" * 50)
    print("🔍 Flask Server Diagnostic Tool")
    print("=" * 50)
    
    # Test 1: Check imports
    if not test_imports():
        print("\n❌ Import test failed. Please install missing packages:")
        print("   pip install -r requirements.txt")
        return False
    
    # Test 2: Check port availability
    if not test_port():
        print("\n❌ Port test failed.")
        print("   Try using a different port or kill existing processes")
        return False
    
    # Test 3: Create and run simple Flask app
    print("\nCreating simple Flask app...")
    app = create_simple_app()
    
    if app is None:
        print("❌ Failed to create Flask app")
        return False
    
    print("✓ Flask app created successfully")
    
    # Test 4: Try to start the server
    print("\n🚀 Starting test server...")
    print("   Server will be available at: http://localhost:5000")
    print("   Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\n✓ Server stopped by user")
        return True
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ All tests passed!")
        else:
            print("\n❌ Some tests failed. Check the output above for details.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
