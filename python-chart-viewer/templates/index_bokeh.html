<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptive Bar Chart Viewer</title>
    
    <!-- Bokeh CSS -->
    {{ bokeh_css|safe }}
    
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }
        
        .status-connected {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-disconnected {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-error {
            color: #ffc107;
            font-weight: bold;
        }
        
        .metrics-panel {
            background: white;
            margin: 1rem 2rem;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .metric-item {
            text-align: center;
            padding: 0.5rem;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.25rem;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .chart-container {
            background: white;
            margin: 1rem 2rem;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 500;
            color: #333;
            margin: 0;
        }
        
        .chart-controls {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #f8f9fa;
            border-color: #667eea;
        }
        
        .btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .info-panel {
            background: white;
            margin: 1rem 2rem;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .info-item {
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .info-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.25rem;
        }
        
        .info-value {
            color: #666;
            font-family: monospace;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Bokeh chart styling */
        .bk-root {
            margin: 0 !important;
        }
        
        .bk-canvas-wrapper {
            border-radius: 4px;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .header, .metrics-panel, .chart-container, .info-panel {
                margin: 0.5rem;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .chart-header {
                flex-direction: column;
                gap: 0.5rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>📊 Adaptive Bar Chart Viewer</h1>
        <div class="status-bar">
            <div>
                <span>Status: </span>
                <span id="connection-status" class="status-disconnected">Disconnected</span>
            </div>
            <div>
                <span>Total Bars: </span>
                <span id="total-bars">{{ total_bars }}</span>
            </div>
        </div>
    </div>

    <!-- Metrics Panel -->
    <div class="metrics-panel">
        <h3 style="margin: 0 0 0.5rem 0; color: #333;">Market Conditions</h3>
        <div class="metrics-grid">
            <div class="metric-item">
                <div class="metric-value" id="current-tps">0.0</div>
                <div class="metric-label">Trades/Second</div>
            </div>
            <div class="metric-item">
                <div class="metric-value" id="current-volatility">0.0</div>
                <div class="metric-label">Volatility</div>
            </div>
            <div class="metric-item">
                <div class="metric-value" id="volume-threshold">0.0</div>
                <div class="metric-label">Volume Threshold</div>
            </div>
            <div class="metric-item">
                <div class="metric-value" id="recent-volume-rate">0.0</div>
                <div class="metric-label">Avg Volume/Trade</div>
            </div>
        </div>
    </div>

    <!-- Chart Container -->
    <div class="chart-container">
        <div class="chart-header">
            <h3 class="chart-title">Real-time Adaptive Volume Bars</h3>
            <div class="chart-controls">
                <button class="btn" onclick="refreshChart()">🔄 Refresh</button>
                <button class="btn" id="auto-refresh-btn" onclick="toggleAutoRefresh()">⏸️ Auto</button>
            </div>
        </div>
        
        <div id="chart-content">
            {% if div %}
                {{ div|safe }}
            {% else %}
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Waiting for data...</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Info Panel -->
    <div class="info-panel">
        <h3 style="margin: 0 0 0.5rem 0; color: #333;">Connection Information</h3>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Java WebSocket</div>
                <div class="info-value">ws://localhost:8080/ws/adaptive-bars</div>
            </div>
            <div class="info-item">
                <div class="info-label">Flask Server</div>
                <div class="info-value">http://localhost:5000</div>
            </div>
            <div class="info-item">
                <div class="info-label">Last Update</div>
                <div class="info-value" id="last-update">Never</div>
            </div>
            <div class="info-item">
                <div class="info-label">Chart Style</div>
                <div class="info-value">Bokeh (backtesting.py style)</div>
            </div>
        </div>
    </div>

    <!-- Bokeh JS -->
    {{ bokeh_js|safe }}
    
    <!-- Chart Script -->
    {{ script|safe }}

    <script>
        // Global variables
        let socket;
        let autoRefresh = true;
        let autoRefreshInterval;

        // Initialize Socket.IO connection
        function initializeSocket() {
            socket = io();

            socket.on('connect', function() {
                console.log('Connected to Flask-SocketIO');
                updateConnectionStatus('Connected', 'connected');
            });

            socket.on('disconnect', function() {
                console.log('Disconnected from Flask-SocketIO');
                updateConnectionStatus('Disconnected', 'disconnected');
            });

            socket.on('status', function(data) {
                updateStatus(data);
            });

            socket.on('new_bar', function(barData) {
                console.log('New bar received:', barData);
                updateLastUpdate();
                if (autoRefresh) {
                    refreshChart();
                }
            });

            socket.on('welcome', function(data) {
                console.log('Welcome message:', data);
                updateMarketConditions(data.currentMarketConditions);
            });

            socket.on('chart_update', function(data) {
                updateChart(data);
            });

            socket.on('error', function(data) {
                console.error('Socket error:', data);
            });
        }

        // Update connection status
        function updateConnectionStatus(status, type) {
            const statusElement = document.getElementById('connection-status');
            statusElement.textContent = status;
            statusElement.className = `status-${type}`;
        }

        // Update status metrics
        function updateStatus(data) {
            document.getElementById('total-bars').textContent = data.total_bars || 0;
            
            if (data.market_conditions) {
                updateMarketConditions(data.market_conditions);
            }
            
            if (data.last_update) {
                document.getElementById('last-update').textContent = 
                    new Date(data.last_update).toLocaleString();
            }
        }

        // Update market conditions
        function updateMarketConditions(conditions) {
            if (!conditions) return;
            
            document.getElementById('current-tps').textContent = 
                (conditions.tradesPerSecond || 0).toFixed(1);
            document.getElementById('current-volatility').textContent = 
                (conditions.rollingVolatility || 0);
            document.getElementById('volume-threshold').textContent = 
                (conditions.dynamicVolumeThreshold || 0);
            document.getElementById('recent-volume-rate').textContent = 
                (conditions.recentVolumeRate || 0);
        }

        // Update last update time
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = 
                new Date().toLocaleString();
        }

        // Refresh chart
        function refreshChart() {
            socket.emit('request_chart_update');
        }

        // Update chart with new data
        function updateChart(data) {
            const container = document.getElementById('chart-content');
            container.innerHTML = data.div;
            
            // Execute the new script
            const script = document.createElement('script');
            script.text = data.script;
            document.head.appendChild(script);
            
            // Update total bars
            document.getElementById('total-bars').textContent = data.total_bars;
        }

        // Toggle auto refresh
        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const button = document.getElementById('auto-refresh-btn');
            
            if (autoRefresh) {
                button.textContent = '⏸️ Auto';
                button.classList.add('active');
                startAutoRefresh();
            } else {
                button.textContent = '▶️ Auto';
                button.classList.remove('active');
                stopAutoRefresh();
            }
        }

        // Start auto refresh
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            autoRefreshInterval = setInterval(() => {
                refreshChart();
            }, 5000); // Refresh every 5 seconds
        }

        // Stop auto refresh
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing Adaptive Bar Chart Viewer (Bokeh)...');
            initializeSocket();
            
            // Set initial auto refresh button state
            const autoRefreshBtn = document.getElementById('auto-refresh-btn');
            autoRefreshBtn.classList.add('active');
            
            // Start auto refresh
            if (autoRefresh) {
                startAutoRefresh();
            }
            
            // Initial chart load
            setTimeout(() => {
                refreshChart();
            }, 1000);
        });
    </script>
</body>
</html>
