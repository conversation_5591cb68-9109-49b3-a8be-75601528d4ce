<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptive Bar Chart Viewer</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <style>
        body {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        
        .card {
            background-color: #2d2d2d;
            border: 1px solid #404040;
        }
        
        .card-header {
            background-color: #404040;
            border-bottom: 1px solid #555555;
        }
        
        .status-connected {
            color: #28a745;
        }
        
        .status-disconnected {
            color: #dc3545;
        }
        
        .status-error {
            color: #ffc107;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        
        .chart-container {
            height: 600px;
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #888;
        }
        
        .navbar {
            background-color: #343a40 !important;
        }
        
        .table-dark {
            background-color: #2d2d2d;
        }
        
        .badge {
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                📊 Adaptive Bar Chart Viewer
            </span>
            <span class="navbar-text">
                Status: <span id="connection-status" class="status-disconnected">Disconnected</span>
            </span>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Status Cards Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Bars</h5>
                        <h2 id="total-bars">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Current TPS</h5>
                        <h2 id="current-tps">0.0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Volatility</h5>
                        <h2 id="current-volatility">0.0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Volume Threshold</h5>
                        <h2 id="volume-threshold">0.0</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">📈 Real-time Adaptive Volume Bars</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-light" onclick="refreshChart()">
                                🔄 Refresh
                            </button>
                            <button class="btn btn-sm btn-outline-light" onclick="toggleAutoRefresh()">
                                <span id="auto-refresh-text">▶️ Auto</span>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="chart-container" class="chart-container">
                            <div class="loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-3">Waiting for data...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Bars Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📋 Recent Adaptive Bars</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Symbol</th>
                                        <th>Open</th>
                                        <th>High</th>
                                        <th>Low</th>
                                        <th>Close</th>
                                        <th>Volume</th>
                                        <th>Trades</th>
                                        <th>Threshold</th>
                                        <th>TPS</th>
                                    </tr>
                                </thead>
                                <tbody id="bars-table-body">
                                    <tr>
                                        <td colspan="10" class="text-center text-muted">No data available</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let socket;
        let autoRefresh = true;
        let autoRefreshInterval;
        let barsData = [];

        // Initialize Socket.IO connection
        function initializeSocket() {
            socket = io();

            socket.on('connect', function() {
                console.log('Connected to Flask-SocketIO');
                updateConnectionStatus('Connected', 'connected');
            });

            socket.on('disconnect', function() {
                console.log('Disconnected from Flask-SocketIO');
                updateConnectionStatus('Disconnected', 'disconnected');
            });

            socket.on('status', function(data) {
                updateStatus(data);
            });

            socket.on('new_bar', function(barData) {
                console.log('New bar received:', barData);
                addNewBar(barData);
                if (autoRefresh) {
                    refreshChart();
                }
            });

            socket.on('welcome', function(data) {
                console.log('Welcome message:', data);
                updateMarketConditions(data.currentMarketConditions);
            });

            socket.on('chart_update', function(data) {
                updateChart(data.chart);
            });

            socket.on('error', function(data) {
                console.error('Socket error:', data);
                showNotification('Error: ' + data.message, 'danger');
            });
        }

        // Update connection status
        function updateConnectionStatus(status, type) {
            const statusElement = document.getElementById('connection-status');
            statusElement.textContent = status;
            statusElement.className = `status-${type}`;
        }

        // Update status metrics
        function updateStatus(data) {
            document.getElementById('total-bars').textContent = data.total_bars || 0;
            
            if (data.market_conditions) {
                updateMarketConditions(data.market_conditions);
            }
        }

        // Update market conditions
        function updateMarketConditions(conditions) {
            if (!conditions) return;
            
            document.getElementById('current-tps').textContent = 
                (conditions.tradesPerSecond || 0).toFixed(1);
            document.getElementById('current-volatility').textContent = 
                (conditions.rollingVolatility || 0);
            document.getElementById('volume-threshold').textContent = 
                (conditions.dynamicVolumeThreshold || 0);
        }

        // Add new bar to local data
        function addNewBar(barData) {
            barsData.push(barData);
            
            // Keep only last 100 bars for table display
            if (barsData.length > 100) {
                barsData = barsData.slice(-100);
            }
            
            updateBarsTable();
            updateMetrics();
        }

        // Update bars table
        function updateBarsTable() {
            const tbody = document.getElementById('bars-table-body');
            
            if (barsData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">No data available</td></tr>';
                return;
            }

            // Show last 10 bars
            const recentBars = barsData.slice(-10).reverse();
            
            tbody.innerHTML = recentBars.map(bar => {
                const conditions = bar.marketConditionSnapshotAtClose || {};
                return `
                    <tr>
                        <td>${new Date(bar.closeTime).toLocaleTimeString()}</td>
                        <td><span class="badge bg-primary">${bar.symbol}</span></td>
                        <td>${parseFloat(bar.openPrice).toFixed(2)}</td>
                        <td class="text-success">${parseFloat(bar.highPrice).toFixed(2)}</td>
                        <td class="text-danger">${parseFloat(bar.lowPrice).toFixed(2)}</td>
                        <td>${parseFloat(bar.closePrice).toFixed(2)}</td>
                        <td>${parseFloat(bar.totalVolumeTraded).toFixed(3)}</td>
                        <td>${bar.tradeCount}</td>
                        <td>${parseFloat(conditions.dynamicVolumeThreshold || 0).toFixed(1)}</td>
                        <td>${(conditions.tradesPerSecond || 0).toFixed(1)}</td>
                    </tr>
                `;
            }).join('');
        }

        // Update metrics from latest bar
        function updateMetrics() {
            if (barsData.length === 0) return;
            
            const latestBar = barsData[barsData.length - 1];
            const conditions = latestBar.marketConditionSnapshotAtClose;
            
            if (conditions) {
                updateMarketConditions(conditions);
            }
            
            document.getElementById('total-bars').textContent = barsData.length;
        }

        // Refresh chart
        function refreshChart() {
            socket.emit('request_chart_update');
        }

        // Update chart with new data
        function updateChart(chartData) {
            const container = document.getElementById('chart-container');
            container.innerHTML = ''; // Clear loading message
            
            Plotly.newPlot('chart-container', chartData.data, chartData.layout, {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d']
            });
        }

        // Toggle auto refresh
        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const button = document.getElementById('auto-refresh-text');
            
            if (autoRefresh) {
                button.textContent = '⏸️ Auto';
                startAutoRefresh();
            } else {
                button.textContent = '▶️ Auto';
                stopAutoRefresh();
            }
        }

        // Start auto refresh
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            autoRefreshInterval = setInterval(() => {
                refreshChart();
            }, 5000); // Refresh every 5 seconds
        }

        // Stop auto refresh
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            // Simple console log for now - could be enhanced with toast notifications
            console.log(`${type.toUpperCase()}: ${message}`);
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing Adaptive Bar Chart Viewer...');
            initializeSocket();
            
            // Start auto refresh
            if (autoRefresh) {
                startAutoRefresh();
            }
            
            // Initial chart load
            setTimeout(() => {
                refreshChart();
            }, 1000);
        });
    </script>
</body>
</html>
