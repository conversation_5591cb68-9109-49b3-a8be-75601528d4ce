#!/usr/bin/env python3
"""
Adaptive Bar Chart Viewer with Bokeh (backtesting.py style)
A Flask web application that displays real-time adaptive volume bars
using Bokeh charts similar to the backtesting.py library interface.
"""

import json
import logging
import threading
import time
from datetime import datetime, timezone
from collections import deque
from typing import Dict, List, Optional

import pandas as pd
import numpy as np
import websocket
from flask import Flask, render_template, jsonify
from flask_socketio import Socket<PERSON>, emit

from bokeh.plotting import figure
from bokeh.models import HoverTool, CrosshairTool, PanTool, WheelZoomTool, BoxZoomTool, ResetTool
from bokeh.models import DatetimeTickFormatter, NumeralTickFormatter
from bokeh.layouts import column, row
from bokeh.embed import components
from bokeh.resources import CDN

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app configuration
app = Flask(__name__)
app.config['SECRET_KEY'] = 'adaptive-bar-viewer-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global data storage
class AdaptiveBarStore:
    def __init__(self, max_bars: int = 1000):
        self.max_bars = max_bars
        self.bars: deque = deque(maxlen=max_bars)
        self.market_conditions: Optional[Dict] = None
        self.connection_status = "Disconnected"
        self.last_update = None
        self.lock = threading.Lock()
    
    def add_bar(self, bar_data: Dict):
        with self.lock:
            self.bars.append(bar_data)
            self.last_update = datetime.now(timezone.utc)
            logger.info(f"Added bar: {bar_data.get('symbol')} at {bar_data.get('closeTime')}")
    
    def update_market_conditions(self, conditions: Dict):
        with self.lock:
            self.market_conditions = conditions
    
    def set_connection_status(self, status: str):
        with self.lock:
            self.connection_status = status
    
    def get_bars_df(self) -> pd.DataFrame:
        with self.lock:
            if not self.bars:
                return pd.DataFrame()
            
            bars_list = list(self.bars)
            df = pd.DataFrame(bars_list)
            
            # Convert timestamp columns
            if 'openTime' in df.columns:
                df['openTime'] = pd.to_datetime(df['openTime'])
            if 'closeTime' in df.columns:
                df['closeTime'] = pd.to_datetime(df['closeTime'])
            
            return df
    
    def get_status(self) -> Dict:
        with self.lock:
            return {
                'connection_status': self.connection_status,
                'total_bars': len(self.bars),
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'market_conditions': self.market_conditions
            }

# Global store instance
bar_store = AdaptiveBarStore()

class JavaWebSocketClient:
    def __init__(self, url: str):
        self.url = url
        self.ws = None
        self.running = False
        self.reconnect_interval = 5
    
    def on_message(self, ws, message):
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'ADAPTIVE_BAR':
                bar_data = data.get('data')
                if bar_data:
                    bar_store.add_bar(bar_data)
                    # Emit to web clients
                    socketio.emit('new_bar', bar_data)
            
            elif message_type == 'WELCOME':
                welcome_data = data.get('data', {})
                logger.info(f"Connected to Java WebSocket: {welcome_data.get('message')}")
                
                # Update market conditions
                conditions = welcome_data.get('currentMarketConditions')
                if conditions:
                    bar_store.update_market_conditions(conditions)
                
                # Emit welcome to web clients
                socketio.emit('welcome', welcome_data)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse WebSocket message: {e}")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
    
    def on_error(self, ws, error):
        logger.error(f"WebSocket error: {error}")
        bar_store.set_connection_status("Error")
    
    def on_close(self, ws, close_status_code, close_msg):
        logger.info("WebSocket connection closed")
        bar_store.set_connection_status("Disconnected")
        
        if self.running:
            logger.info(f"Attempting to reconnect in {self.reconnect_interval} seconds...")
            time.sleep(self.reconnect_interval)
            self.connect()
    
    def on_open(self, ws):
        logger.info("WebSocket connection opened")
        bar_store.set_connection_status("Connected")
        
        test_message = {"type": "CLIENT_HELLO", "timestamp": datetime.now().isoformat()}
        ws.send(json.dumps(test_message))
    
    def connect(self):
        if not self.running:
            return
        
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            logger.info(f"Connecting to Java WebSocket: {self.url}")
            self.ws.run_forever()
            
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            bar_store.set_connection_status("Connection Failed")
    
    def start(self):
        self.running = True
        thread = threading.Thread(target=self.connect, daemon=True)
        thread.start()
        return thread
    
    def stop(self):
        self.running = False
        if self.ws:
            self.ws.close()

# WebSocket client instance
java_ws_client = JavaWebSocketClient("ws://localhost:8080/ws/adaptive-bars")

def create_bokeh_chart(df: pd.DataFrame):
    """Create a Bokeh chart similar to backtesting.py style."""
    if df.empty:
        # Create empty chart
        p = figure(
            title="Adaptive Volume Bars - No Data",
            x_axis_type='datetime',
            width=1200,
            height=400,
            toolbar_location="above"
        )
        p.title.text_font_size = "16pt"
        return p, None
    
    # Prepare data
    df = df.copy()
    df['datetime'] = df['closeTime']
    df['color'] = ['green' if close >= open_price else 'red' 
                   for close, open_price in zip(df['closePrice'], df['openPrice'])]
    
    # Main price chart
    price_chart = figure(
        title=f"Adaptive Volume Bars - {df['symbol'].iloc[-1] if not df.empty else 'Unknown'}",
        x_axis_type='datetime',
        width=1200,
        height=400,
        toolbar_location="above",
        tools="pan,wheel_zoom,box_zoom,reset,crosshair"
    )
    
    # Candlestick bodies
    price_chart.rect(
        x='datetime', 
        y=(df['openPrice'] + df['closePrice']) / 2,
        width=pd.Timedelta(minutes=1).total_seconds() * 1000 * 0.8,  # 80% of estimated bar width
        height=abs(df['closePrice'] - df['openPrice']),
        color='color',
        alpha=0.8,
        source=df
    )
    
    # Candlestick wicks
    price_chart.segment(
        x0='datetime', y0='lowPrice',
        x1='datetime', y1='highPrice',
        color='color',
        alpha=0.8,
        source=df
    )
    
    # Volume chart
    volume_chart = figure(
        x_axis_type='datetime',
        width=1200,
        height=150,
        x_range=price_chart.x_range,  # Link x-axis with price chart
        toolbar_location=None
    )
    
    volume_chart.vbar(
        x='datetime',
        top='totalVolumeTraded',
        width=pd.Timedelta(minutes=1).total_seconds() * 1000 * 0.8,
        color='blue',
        alpha=0.6,
        source=df
    )
    
    # Styling similar to backtesting.py
    for chart in [price_chart, volume_chart]:
        chart.title.text_font_size = "14pt"
        chart.title.text_color = "#333333"
        chart.background_fill_color = "#fafafa"
        chart.border_fill_color = "#ffffff"
        chart.outline_line_color = "#cccccc"
        
        # Grid styling
        chart.grid.grid_line_color = "#e6e6e6"
        chart.grid.grid_line_alpha = 0.8
        
        # Axis styling
        chart.axis.axis_line_color = "#cccccc"
        chart.axis.major_tick_line_color = "#cccccc"
        chart.axis.minor_tick_line_color = "#cccccc"
        chart.axis.axis_label_text_color = "#666666"
        chart.axis.major_label_text_color = "#666666"
    
    # Format axes
    price_chart.yaxis.formatter = NumeralTickFormatter(format="0,0.00")
    volume_chart.yaxis.formatter = NumeralTickFormatter(format="0.00a")
    
    # Add hover tools
    price_hover = HoverTool(tooltips=[
        ("Time", "@datetime{%F %T}"),
        ("Open", "@openPrice{0.00}"),
        ("High", "@highPrice{0.00}"),
        ("Low", "@lowPrice{0.00}"),
        ("Close", "@closePrice{0.00}"),
        ("Volume", "@totalVolumeTraded{0.00}"),
        ("Trades", "@tradeCount")
    ], formatters={'@datetime': 'datetime'})
    
    volume_hover = HoverTool(tooltips=[
        ("Time", "@datetime{%F %T}"),
        ("Volume", "@totalVolumeTraded{0.00}")
    ], formatters={'@datetime': 'datetime'})
    
    price_chart.add_tools(price_hover)
    volume_chart.add_tools(volume_hover)
    
    # Labels
    price_chart.yaxis.axis_label = "Price"
    volume_chart.yaxis.axis_label = "Volume"
    volume_chart.xaxis.axis_label = "Time"
    
    # Combine charts
    layout = column(price_chart, volume_chart)
    
    return layout, df

@app.route('/')
def index():
    """Main dashboard page with Bokeh charts."""
    df = bar_store.get_bars_df()
    chart_layout, chart_df = create_bokeh_chart(df)
    
    # Get Bokeh components
    script, div = components(chart_layout)
    
    # Get status and market conditions
    status = bar_store.get_status()
    
    return render_template('index_bokeh.html', 
                         script=script, 
                         div=div,
                         bokeh_css=CDN.render_css(),
                         bokeh_js=CDN.render_js(),
                         status=status,
                         total_bars=len(df) if not df.empty else 0)

@app.route('/api/chart-update')
def chart_update():
    """API endpoint for chart updates."""
    df = bar_store.get_bars_df()
    chart_layout, chart_df = create_bokeh_chart(df)
    
    script, div = components(chart_layout)
    
    return jsonify({
        'script': script,
        'div': div,
        'total_bars': len(df) if not df.empty else 0
    })

@app.route('/api/status')
def get_status():
    """API endpoint to get connection and data status."""
    return jsonify(bar_store.get_status())

@socketio.on('connect')
def handle_connect():
    """Handle client connection to Flask-SocketIO."""
    logger.info('Client connected to Flask-SocketIO')
    emit('status', bar_store.get_status())

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection from Flask-SocketIO."""
    logger.info('Client disconnected from Flask-SocketIO')

@socketio.on('request_chart_update')
def handle_chart_update_request():
    """Handle client request for chart update."""
    try:
        df = bar_store.get_bars_df()
        chart_layout, chart_df = create_bokeh_chart(df)
        script, div = components(chart_layout)
        
        emit('chart_update', {
            'script': script,
            'div': div,
            'total_bars': len(df) if not df.empty else 0
        })
    except Exception as e:
        logger.error(f"Error generating chart update: {e}")
        emit('error', {'message': str(e)})

if __name__ == '__main__':
    # Start Java WebSocket client
    logger.info("Starting Java WebSocket client...")
    java_ws_client.start()
    
    # Start Flask-SocketIO server
    logger.info("Starting Flask-SocketIO server...")
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, use_reloader=False)
