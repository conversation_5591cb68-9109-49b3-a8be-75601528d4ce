#!/usr/bin/env python3
"""
Ultra-simple chart viewer using Chart.js instead of Bokeh
This will help us verify the data is working correctly.
"""

import json
import logging
import threading
import time
from datetime import datetime, timezone
from collections import deque
from typing import Dict, List, Optional

import pandas as pd
import websocket
from flask import Flask, render_template_string

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app
app = Flask(__name__)

# Global data storage
bars_data = deque(maxlen=100)
connection_status = "Disconnected"

class JavaWebSocketClient:
    def __init__(self, url: str):
        self.url = url
        self.ws = None
        self.running = False
    
    def on_message(self, ws, message):
        global bars_data, connection_status
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'ADAPTIVE_BAR':
                bar_data = data.get('data')
                if bar_data:
                    bars_data.append(bar_data)
                    logger.info(f"Added bar: {bar_data.get('symbol')} - Total bars: {len(bars_data)}")
            
            elif message_type == 'WELCOME':
                welcome_data = data.get('data', {})
                logger.info(f"Connected: {welcome_data.get('message')}")
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    def on_error(self, ws, error):
        global connection_status
        logger.error(f"WebSocket error: {error}")
        connection_status = "Error"
    
    def on_close(self, ws, close_status_code, close_msg):
        global connection_status
        logger.info("WebSocket connection closed")
        connection_status = "Disconnected"
    
    def on_open(self, ws):
        global connection_status
        logger.info("WebSocket connection opened")
        connection_status = "Connected"
    
    def connect(self):
        if not self.running:
            return
        
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            logger.info(f"Connecting to: {self.url}")
            self.ws.run_forever()
            
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
    
    def start(self):
        self.running = True
        thread = threading.Thread(target=self.connect, daemon=True)
        thread.start()
        return thread

def prepare_chart_data():
    """Prepare data for Chart.js."""
    global bars_data
    
    if not bars_data:
        return {"labels": [], "prices": [], "volumes": []}
    
    # Convert to DataFrame
    df = pd.DataFrame(list(bars_data))
    
    # Convert timestamps to readable format
    df['datetime'] = pd.to_datetime(df['closeTime'], unit='s')
    df['time_label'] = df['datetime'].dt.strftime('%H:%M:%S')
    
    # Convert prices to numeric
    for col in ['closePrice', 'totalVolumeTraded']:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    return {
        "labels": df['time_label'].tolist(),
        "prices": df['closePrice'].tolist(),
        "volumes": df['totalVolumeTraded'].tolist(),
        "count": len(df)
    }

@app.route('/')
def index():
    """Main page with Chart.js chart."""
    global bars_data, connection_status
    
    chart_data = prepare_chart_data()
    
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Adaptive Bar Viewer</title>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 20px; 
                background-color: #f8f9fa;
            }
            .header { 
                background: #e9ecef; 
                padding: 15px; 
                margin-bottom: 20px; 
                border-radius: 5px;
                border: 1px solid #dee2e6;
            }
            .status { 
                color: {{ 'green' if status == 'Connected' else 'red' }}; 
                font-weight: bold;
            }
            .chart-container {
                background: white;
                padding: 20px;
                border-radius: 5px;
                border: 1px solid #dee2e6;
                margin-top: 20px;
                height: 500px;
            }
            button {
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
            }
            button:hover {
                background: #0056b3;
            }
            .data-info {
                background: #d4edda;
                border: 1px solid #c3e6cb;
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📊 Simple Adaptive Bar Viewer (Chart.js)</h1>
            <p>Status: <span class="status">{{ status }}</span> | Bars: {{ bar_count }}</p>
            <button onclick="location.reload()">🔄 Refresh</button>
        </div>
        
        {% if chart_data['count'] > 0 %}
        <div class="data-info">
            <strong>✅ Data Available:</strong> {{ chart_data['count'] }} bars received<br>
            <strong>Price Range:</strong> {{ "%.2f"|format(chart_data['prices']|min) }} - {{ "%.2f"|format(chart_data['prices']|max) }}<br>
            <strong>Latest Time:</strong> {{ chart_data['labels'][-1] if chart_data['labels'] else 'N/A' }}
        </div>
        
        <div class="chart-container">
            <canvas id="priceChart"></canvas>
        </div>
        
        <script>
            const ctx = document.getElementById('priceChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: {{ chart_data['labels'] | tojson }},
                    datasets: [{
                        label: 'BTCUSDT Close Price',
                        data: {{ chart_data['prices'] | tojson }},
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'Volume',
                        data: {{ chart_data['volumes'] | tojson }},
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        yAxisID: 'y1',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Price'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Volume'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Real-time Adaptive Volume Bars'
                        }
                    }
                }
            });
        </script>
        
        {% else %}
        <div class="chart-container">
            <h3>⏳ Waiting for data...</h3>
            <p>Connected to WebSocket, waiting for adaptive bars to arrive.</p>
            <p>Make sure your Java trading bot is running and generating adaptive bars.</p>
        </div>
        {% endif %}
        
        <script>
            // Auto-refresh every 10 seconds
            setTimeout(function() {
                location.reload();
            }, 10000);
        </script>
    </body>
    </html>
    """
    
    return render_template_string(
        html_template,
        status=connection_status,
        bar_count=len(bars_data),
        chart_data=chart_data
    )

@app.route('/debug')
def debug():
    """Debug endpoint."""
    global bars_data, connection_status
    
    chart_data = prepare_chart_data()
    
    return {
        'status': connection_status,
        'bar_count': len(bars_data),
        'chart_data': chart_data,
        'sample_data': list(bars_data)[-3:] if bars_data else []
    }

if __name__ == '__main__':
    # Start WebSocket client
    logger.info("Starting simple Chart.js viewer...")
    client = JavaWebSocketClient("ws://localhost:8080/ws/adaptive-bars")
    client.start()
    
    # Start Flask
    logger.info("Starting Flask server on http://localhost:5000")
    app.run(host='0.0.0.0', port=5001, debug=False)  # Use port 5001 to avoid conflict
