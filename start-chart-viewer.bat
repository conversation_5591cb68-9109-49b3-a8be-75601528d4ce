@echo off
echo ========================================
echo  Adaptive Bar Chart Viewer Launcher
echo ========================================
echo.

echo [1/4] Checking Java Trading Bot...
echo Make sure your Java trading bot is running on port 8080
echo WebSocket endpoint: ws://localhost:8080/ws/adaptive-bars
echo.

echo [2/4] Checking Python Environment...
cd python-chart-viewer

echo Running diagnostics...
python troubleshoot.py
echo.

echo [3/4] Installing Python dependencies...
pip install -r requirements.txt
echo.

echo [4/4] Launching Flask Application...
echo Chart viewer will be available at: http://localhost:5000
echo.
echo If the server fails to start, try:
echo   python test_server.py
echo   python troubleshoot.py
echo.

python run.py

echo.
echo Chart viewer stopped.
echo.
echo If you encountered issues, run:
echo   python troubleshoot.py
echo.
pause
