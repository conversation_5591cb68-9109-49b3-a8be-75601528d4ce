plugins {
	id 'java'
	id 'org.springframework.boot' version '3.2.3'
	id 'io.spring.dependency-management' version '1.1.4'
}

group = 'au.com.healius'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
	mavenLocal()
	mavenCentral()
	maven {
		url "https://repo1.maven.org/maven2"
	}
	maven { url 'https://repo.spring.io/milestone' }

	maven {
		url 'https://pkgs.dev.azure.com/HLSPathology/Digital/_packaging/DigitalLibraries/maven/v1'
		credentials {
			username = 'DigitalLibraries'
			password = System.getenv("SYSTEM_ACCESSTOKEN") != null ? System.getenv("SYSTEM_ACCESSTOKEN") : vstsMavenAccessToken
		}
	}
}

ext {
	set('springShellVersion', "3.2.2")
}

tasks.withType(JavaCompile) {
	options.compilerArgs << '-parameters'
	options.fork = true
}


dependencyManagement {
	imports {
		mavenBom "org.springframework.shell:spring-shell-dependencies:${springShellVersion}"
	}
}

dependencies {
	implementation 'org.springframework.shell:spring-shell-starter'

	implementation 'org.springframework.boot:spring-boot-starter-data-neo4j'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'

	implementation 'org.apache.commons:commons-csv:1.9.0'

	implementation 'org.apache.commons:commons-lang3:3.12.0'

	implementation 'au.com.healius.digital:library-collections:20250526.1'
	implementation 'au.com.healius.digital:library-trm:beta'
   	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0'

}

tasks.named('test') {
	useJUnitPlatform()
}