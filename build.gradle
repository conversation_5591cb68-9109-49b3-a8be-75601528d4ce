plugins {
    id 'org.springframework.boot' version '3.4.4'
    id 'io.spring.dependency-management' version '1.1.7'
    id "net.linguica.maven-settings" version "0.5"
    id 'java-library'
    id 'maven-publish'
    id 'java'
    id "idea"
    id "jacoco"
    id "org.sonarqube" version "6.0.1.5171"
}

group = 'au.com.healius.digital'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

if (project.hasProperty('projectVersion')) {
    project.version = project.projectVersion
} else {
    project.version = 'beta'
}

repositories {
    mavenLocal()
    maven {
        url "https://repo1.maven.org/maven2"
    }
    maven { url 'https://repo.spring.io/milestone' }

    maven {
        url 'https://pkgs.dev.azure.com/HLSPathology/Digital/_packaging/DigitalLibraries/maven/v1'
        credentials {
            username = 'DigitalLibraries'
            password = System.getenv("SYSTEM_ACCESSTOKEN") != null ? System.getenv("SYSTEM_ACCESSTOKEN") : vstsMavenAccessToken
        }
    }
}

dependencies {
    implementation 'au.com.healius.digital:library-common:20250211.2'

    implementation 'org.springframework.boot:spring-boot-starter-data-neo4j:3.4.4'
    implementation 'org.springframework.boot:spring-boot-starter-validation:3.4.4'
    implementation 'org.springframework.boot:spring-boot-starter-cache:3.4.4'

    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'

    // JSON
    implementation 'com.google.code.gson:gson:2.10.1'

    // OPEN API
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.4'

    implementation 'com.google.guava:guava:32.0.1-android'
}

test {
    useJUnitPlatform()
}

publishing {
    publications {
        library(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            url 'https://pkgs.dev.azure.com/HLSPathology/Digital/_packaging/DigitalLibraries/maven/v1'
            credentials {
                username = 'DigitalLibraries'
                password = System.getenv("SYSTEM_ACCESSTOKEN") != null ? System.getenv("SYSTEM_ACCESSTOKEN") : vstsMavenAccessToken
            }
        }
    }
}