plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.4'
    id 'io.spring.dependency-management' version '1.1.7'
    id "idea"
    id "jacoco"
    id "org.sonarqube" version "6.0.1.5171"
    id 'org.openapi.generator' version '7.10.0'
}

group = 'au.com.healius'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

if (project.hasProperty('projectVersion')) {
    project.version = project.projectVersion
} else {
    project.version = 'beta'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenLocal()
    maven {
        url "https://repo1.maven.org/maven2"
    }
    maven { url 'https://repo.spring.io/milestone' }

    maven {
        url 'https://pkgs.dev.azure.com/HLSPathology/Digital/_packaging/DigitalLibraries/maven/v1'
        credentials {
            username = 'DigitalLibraries'
            password = System.getenv("SYSTEM_ACCESSTOKEN") != null ? System.getenv("SYSTEM_ACCESSTOKEN") : vstsMavenAccessToken
        }
    }
}

ext {
    set('springCloudAzureVersion', "5.19.0")
    set('springCloudVersion', "2024.0.0")
}

dependencies {
//    HEALIUS

//  implementation 'au.com.healius.digital:library-collections:20250326.5'
//    implementation 'au.com.healius.digital:library-golden-record:20250220.7'
    implementation 'au.com.healius.digital:library-trm:beta'
    implementation 'au.com.healius.digital:library-common:20240723.1'
    implementation 'au.com.healius.digital:library-ui:20250224.1'

//    SPRING
    implementation 'org.springframework.session:spring-session-core'
    implementation 'com.azure.spring:spring-cloud-azure-starter-keyvault'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-data-neo4j'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    // eureka needed for service discovery in azure spring apps
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity6'
    implementation 'nz.net.ultraq.thymeleaf:thymeleaf-layout-dialect:3.3.0'
    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'org.aspectj:aspectjweaver:1.9.9.1'

    implementation 'com.azure.spring:spring-cloud-azure-starter-active-directory'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'

    implementation 'org.apache.commons:commons-csv:1.9.0'
    implementation 'org.json:json:20231013'

    implementation 'co.elastic.apm:apm-agent-attach:1.52.1'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.7.2'
    testImplementation 'org.mockito:mockito-inline:4.0.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:4.0.0'

}

dependencyManagement {
    imports {
        mavenBom "com.azure.spring:spring-cloud-azure-dependencies:${springCloudAzureVersion}"
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

test {
    useJUnitPlatform()
}

bootJar {
    setArchiveFileName("webservice-admin-tests.jar")
    manifest {
        attributes 'Implementation-Title': 'Admin Tests', 'Implementation-Version': project.version
    }
}

sourceSets {
    main {
        java {
            srcDir("$buildDir/generated/api/src/main/java")
        }
    }
}
