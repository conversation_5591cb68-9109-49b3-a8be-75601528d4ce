# Adaptive Volume Bar Aggregator

## Overview

The `AdaptiveVolumeBarAggregator` is an advanced market data aggregator that creates volume-based bars with dynamically adjusted volume thresholds. Unlike traditional time-based bars, these adaptive bars are completed when a dynamically calculated volume threshold is reached, making them more responsive to changing market conditions.

## Key Features

- ✅ **Dynamic Volume Thresholds**: Volume thresholds adapt based on real-time market conditions
- ✅ **Internal Market Metrics**: Calculates volatility, TPS, and volume rate from incoming trades
- ✅ **MarketDataIndicator Interface**: Full integration with existing indicator framework
- ✅ **Thread-Safe Operation**: Concurrent-safe with ReadWriteLock
- ✅ **Reactive Streams**: Real-time bar updates via Flux
- ✅ **Comprehensive Market Data**: OHLC, VWAP, volume breakdown, and market condition snapshots
- ✅ **Spring Integration**: Full integration with channels and gateways

## Architecture

### Core Components

1. **AdaptiveBarData Model** (`domain/model/AdaptiveBarData.java`)
   - Complete OHLC data structure
   - Volume metrics (total, aggressive buy/sell)
   - Market condition snapshot at bar close
   - Closing reason and metadata

2. **MarketConditionSnapshot** (Inner class)
   - Rolling volatility metrics
   - Trades per second (TPS)
   - Recent volume rate
   - Dynamic threshold information
   - Adjustment factors applied

3. **AdaptiveVolumeBarAggregator** (`aggregator/AdaptiveVolumeBarAggregator.java`)
   - Main aggregator implementation
   - Internal market condition calculation
   - Dynamic threshold adaptation
   - Thread-safe bar processing

## Dynamic Threshold Calculation

The volume threshold is dynamically adjusted based on three key market metrics:

### 1. **Volatility Adjustment** (Inverse Relationship)
```
volatilityAdjustment = 1.0 / (1.0 + volatility * volatilitySensitivity)
```
- **Higher volatility** → **Lower threshold** → **More frequent bars**
- **Lower volatility** → **Higher threshold** → **Less frequent bars**

### 2. **TPS Adjustment** (Inverse Relationship)
```
tpsAdjustment = 1.0 / (1.0 + TPS * tpsSensitivity * 0.1)
```
- **Higher TPS** → **Lower threshold** → **More frequent bars**
- **Lower TPS** → **Higher threshold** → **Less frequent bars**

### 3. **Volume Rate Adjustment** (Direct Relationship)
```
volumeRateAdjustment = 1.0 + avgVolumePerTrade * volumeRateSensitivity * 0.1
```
- **Higher avg volume** → **Higher threshold** → **Less frequent bars**
- **Lower avg volume** → **Lower threshold** → **More frequent bars**

### Final Threshold Calculation
```
dynamicThreshold = baseThreshold * volatilityAdj * tpsAdj * volumeRateAdj
```
The result is clamped between `minDynamicVolumeThreshold` and `maxDynamicVolumeThreshold`.

## Configuration

### Application Properties

```properties
# Adaptive Volume Bar Aggregator Configuration
aggregator.adaptive.baseVolumeThreshold=10.0
aggregator.adaptive.volatilityWindowSize=50
aggregator.adaptive.tpsWindowDurationSeconds=10
aggregator.adaptive.volumeRateWindowSize=30
aggregator.adaptive.metricsUpdateFrequency=5
aggregator.adaptive.volatilitySensitivity=1.0
aggregator.adaptive.tpsSensitivity=1.0
aggregator.adaptive.volumeRateSensitivity=0.5
aggregator.adaptive.minVolumeThreshold=1.0
aggregator.adaptive.maxVolumeThreshold=100.0
```

### Configuration Parameters

| Parameter | Description | Default | Range |
|-----------|-------------|---------|-------|
| `baseVolumeThreshold` | Base volume threshold before adjustments | 10.0 | > 0 |
| `volatilityWindowSize` | Number of trades for volatility calculation | 50 | 10-200 |
| `tpsWindowDurationSeconds` | Time window for TPS calculation | 10 | 5-60 |
| `volumeRateWindowSize` | Number of trades for volume rate calculation | 30 | 10-100 |
| `metricsUpdateFrequency` | Update metrics every N trades | 5 | 1-20 |
| `volatilitySensitivity` | Volatility adjustment sensitivity | 1.0 | 0.1-5.0 |
| `tpsSensitivity` | TPS adjustment sensitivity | 1.0 | 0.1-5.0 |
| `volumeRateSensitivity` | Volume rate adjustment sensitivity | 0.5 | 0.1-2.0 |
| `minVolumeThreshold` | Minimum allowed threshold | 1.0 | > 0 |
| `maxVolumeThreshold` | Maximum allowed threshold | 100.0 | > min |

## Usage Examples

### Basic Integration in Strategy

```java
@Component
public class MyStrategy {
    
    private final AdaptiveVolumeBarAggregator adaptiveAggregator;
    private final IndicatorRegistry indicatorRegistry;
    
    @PostConstruct
    public void initialize() {
        // Register with indicator registry
        indicatorRegistry.register(adaptiveAggregator);
        
        // Subscribe to adaptive bar updates
        adaptiveAggregator.getStream().subscribe(this::onAdaptiveBarUpdate);
    }
    
    private void onAdaptiveBarUpdate(AdaptiveBarData adaptiveBar) {
        // Process completed adaptive bar
        analyzeAdaptiveBar(adaptiveBar);
    }
}
```

### Accessing Market Conditions

```java
// Get current market conditions
AdaptiveBarData.MarketConditionSnapshot conditions = 
    adaptiveAggregator.getCurrentMarketConditions();

// Access individual metrics
BigDecimal volatility = conditions.getRollingVolatility();
double tps = conditions.getTradesPerSecond();
BigDecimal volumeRate = conditions.getRecentVolumeRate();
BigDecimal currentThreshold = conditions.getDynamicVolumeThreshold();
```

### Bar Statistics and Monitoring

```java
// Get current bar statistics
String barStats = adaptiveAggregator.getCurrentBarStatistics();
// Output: "Current Bar [BTCUSDT]: trades=45, volume=8.5, threshold=10.0 (85.0% filled)"

// Get configuration info
String config = adaptiveAggregator.getConfigInfo();
// Output: "AdaptiveVolumeBarAggregator[baseThreshold=10.0, volWindow=50, ...]"

// Force complete current bar (for testing)
AdaptiveBarData completedBar = adaptiveAggregator.forceCompleteCurrentBar();
```

## AdaptiveBarData Structure

```java
AdaptiveBarData {
    Instant openTime;                    // Bar start time
    Instant closeTime;                   // Bar end time
    String symbol;                       // Trading symbol
    BigDecimal openPrice;                // Opening price
    BigDecimal highPrice;                // Highest price
    BigDecimal lowPrice;                 // Lowest price
    BigDecimal closePrice;               // Closing price
    BigDecimal volumeWeightedAveragePrice; // VWAP
    BigDecimal totalVolumeTraded;        // Total volume
    BigDecimal aggressiveBuyVolume;      // Aggressive buy volume
    BigDecimal aggressiveSellVolume;     // Aggressive sell volume
    int tradeCount;                      // Number of trades
    String closingReason;                // "ADAPTIVE_VOLUME_THRESHOLD"
    MarketConditionSnapshot marketConditionSnapshotAtClose; // Market metrics
}
```

## Market Condition Analysis

### Example Analysis Patterns

```java
private void analyzeAdaptiveBar(AdaptiveBarData adaptiveBar) {
    MarketConditionSnapshot conditions = adaptiveBar.getMarketConditionSnapshotAtClose();
    
    // High activity detection
    if (conditions.getTradesPerSecond() > 5.0) {
        logger.info("High trading activity: {:.2f} TPS", conditions.getTradesPerSecond());
    }
    
    // Volatility analysis
    if (conditions.getRollingVolatility().doubleValue() > 100.0) {
        logger.info("High volatility period: {}", conditions.getRollingVolatility());
    }
    
    // Volume imbalance detection
    double buyRatio = adaptiveBar.getAggressiveBuyVolume().doubleValue() / 
                     adaptiveBar.getTotalVolumeTraded().doubleValue();
    
    if (buyRatio > 0.7) {
        logger.info("Strong buying pressure: {:.1f}%", buyRatio * 100);
    }
    
    // Threshold efficiency
    double efficiency = adaptiveBar.getTotalVolumeTraded().doubleValue() / 
                       conditions.getDynamicVolumeThreshold().doubleValue();
    
    if (efficiency > 1.2) {
        logger.info("Bar exceeded threshold: {:.1f}%", efficiency * 100);
    }
}
```

## Spring Integration

### Message Channels

```java
// Receives AggTradeEvent messages
@ServiceActivator(inputChannel = "aggTradeChannel")
public void handleAggTrade(Message<AggTradeEvent> message)

// Publishes completed adaptive bars
@Gateway(requestChannel = "adaptiveBarChannel")
void publish(AdaptiveBarData adaptiveBar);
```

### Channel Configuration

```java
@Bean
public MessageChannel adaptiveBarChannel() {
    return new DirectChannel();
}
```

## Performance Characteristics

- **Memory Usage**: Fixed-size circular buffers prevent memory leaks
- **CPU Usage**: Efficient O(1) operations for most calculations
- **Latency**: Low-latency bar completion and emission
- **Throughput**: Handles high-frequency trade streams efficiently
- **Scalability**: Thread-safe design supports concurrent access

## Comparison with Time-Based Bars

| Aspect | Time-Based Bars | Adaptive Volume Bars |
|--------|-----------------|---------------------|
| **Completion Trigger** | Fixed time intervals | Dynamic volume thresholds |
| **Market Responsiveness** | Low | High |
| **Bar Frequency** | Constant | Variable (adapts to conditions) |
| **Information Content** | Time-normalized | Volume-normalized |
| **Volatility Handling** | Poor | Excellent |
| **Activity Periods** | Fixed regardless of activity | More bars during high activity |

## Use Cases

### 1. **High-Frequency Trading**
- Adaptive bars provide more timely signals during volatile periods
- Better capture of market microstructure changes

### 2. **Market Making**
- Volume-based bars align better with inventory management
- Better detection of order flow imbalances

### 3. **Volatility Trading**
- Automatic adjustment to volatility changes
- More frequent signals during volatile periods

### 4. **Algorithmic Trading**
- Better signal-to-noise ratio in varying market conditions
- Reduced lag during high-activity periods

## Integration with Existing System

The aggregator seamlessly integrates with the existing trading system:

1. **IndicatorRegistry**: Registers as a market data indicator
2. **Spring Integration**: Uses existing channel infrastructure  
3. **MarketDataGateway**: Publishes bars through gateway
4. **AggTradeEvent**: Processes existing trade event format
5. **Reactive Streams**: Compatible with existing stream processing
6. **Thread Safety**: Safe for concurrent use with other indicators

## Future Enhancements

- **Multiple Symbols**: Support for multi-symbol aggregation
- **Custom Metrics**: Pluggable market condition calculators
- **Machine Learning**: ML-based threshold adaptation
- **Persistence**: Database storage for historical bars
- **Backtesting**: Historical data processing capabilities
