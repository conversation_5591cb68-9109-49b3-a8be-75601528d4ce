#!/bin/bash

echo "========================================"
echo " Adaptive Bar Chart Viewer Launcher"
echo "========================================"
echo

echo "[1/3] Checking Java Trading Bot..."
echo "Make sure your Java trading bot is running on port 8080"
echo "WebSocket endpoint: ws://localhost:8080/ws/adaptive-bars"
echo

echo "[2/3] Starting Python Chart Viewer..."
cd python-chart-viewer

echo "Installing Python dependencies..."
pip install -r requirements.txt

echo
echo "[3/3] Launching Flask Application..."
echo "Chart viewer will be available at: http://localhost:5000"
echo

python run.py

echo
echo "Chart viewer stopped."
