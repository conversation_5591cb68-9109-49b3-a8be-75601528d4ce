openapi: 3.0.0
info:
  title: SVC Referral API
  version: 1.0.0
servers:
  - url: https://example.com/v1/api
    description: Dev Server base url
tags:
  - name: referralQuery
    description: referralQuery

paths:
  /v1/user/referral/{referralCode}:
    parameters:
      - schema:
          type: string
        name: referralCode
        in: path
        required: true
        description: Code on referral
      - schema:
          type: string
        in: header
        name: X-device-id

    get:
      operationId: get-referral-v1
      description: get a referral
      tags:
        - referralQuery
      summary: Get a referral for person

      responses:
        '200':
          description: Referral sucessfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/getReferralApiResponse'
        '400':
          description: Unprocessable
  /v1/user/patient/{patientId}/referrals:
    parameters:
      - schema:
          type: string
        in: header
        name: X-device-id
      - schema:
          type: string
        in: path
        name: patientId
    get:
      operationId: get-referrals-v1
      description: get all referrals for user
      tags:
        - referralQuery
      summary: Get all referrals for person

      responses:
        '200':
          description: Referrals successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/getReferralsApiResponse'
        '400':
          description: Unprocessable

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:

    #COMMON
    error:
      type: object
      properties:
        message:
          type: string
        errorType:
          type: string
        source:
          type: string
        errorCategory:
          type: string
      required:
        - message
        - errorType
        - source
        - errorCategory
    apiResponse:
      type: object
      properties:
        code:
          type: string
        responseMessage:
          type: string
        errors:
          type: array
          items:
            $ref: '#/components/schemas/error'
        page:
          type: integer
      required:
        - code
        - responseMessage
        - errors
    errorResponse:
      allOf:
        - $ref: '#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              type: string
              nullable: true

    getReferralsApiResponse:
      allOf:
        - $ref: '#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              $ref: '#/components/schemas/getReferralsPayload'
    getReferralsPayload:
      type: object
      properties:
        referrals:
          type: array
          items:
            $ref: '#/components/schemas/orderDetails'
    getReferralApiResponse:
      allOf:
        - $ref: '#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              $ref: '#/components/schemas/getReferralPayload'
    getReferralPayload:
      type: object
      properties:
        responseName:
          type: string
        orderDetails:
          $ref: '#/components/schemas/orderDetails'
      required:
        - orderDetails

    orderDetails:
      type: object
      properties:
        healiusId:
          type: string
        orderables:
          type: array
          items:
            $ref: '#/components/schemas/orderable'
          description: the unique id for the device
        clinicalDetails:
          $ref: '#/components/schemas/clinicalDetails'
        referredPatient:
          $ref: '#/components/schemas/referredPatient'
        referrer:
          $ref: '#/components/schemas/referrer'

    clinicalDetails:
      type: object
      properties:
        id:
          type: string
          description: clinical details property
        fasting:
          type: boolean
          description: clinical details property
        nonFasting:
          type: boolean
          description: clinical details property
        diabetic:
          type: boolean
          description: clinical details property
        thyroxine:
          type: boolean
          description: clinical details property
        antithyroid:
          type: boolean
          description: clinical details property
        hormoneTherapy:
          type: boolean
          description: clinical details property
        pregnant:
          type: boolean
          description: clinical details property
        lnmp:
          type: boolean
          description: clinical details property
        edc:
          type: boolean
          description: clinical details property
        lnmpString:
          type: string
          description: clinical details property
        edcString:
          type: string
          description: clinical details property
        cervix:
          type: boolean
          description: clinical details property
        vaginalVault:
          type: boolean
          description: clinical details property
        vagina:
          type: boolean
          description: clinical details property
        ectropion:
          type: boolean
          description: clinical details property
        benign:
          type: boolean
          description: clinical details property
        suspicious:
          type: boolean
          description: clinical details property
        highAbnormal:
          type: boolean
          description: clinical details property
        lowAbnormal:
          type: boolean
          description: clinical details property
        inconclusiveAbnormal:
          type: boolean
          description: clinical details property
        postmenopausalBleeding:
          type: boolean
          description: clinical details property
        intemenstrualBleeding:
          type: boolean
          description: clinical details property
        postcoitalBleeding:
          type: boolean
          description: clinical details property
        vaginalDischarge:
          type: boolean
          description: clinical details property
        dyspareunia:
          type: boolean
          description: clinical details property
        hysterectomy:
          type: boolean
          description: clinical details property
        adenocarcinomaInSitu:
          type: boolean
          description: clinical details property
        hsil:
          type: boolean
          description: clinical details property
        immunodeficient:
          type: boolean
          description: clinical details property
        desExposure:
          type: boolean
          description: clinical details property
        radioTherapy:
          type: boolean
          description: clinical details property
        pcbSingleEpisode:
          type: boolean
          description: clinical details property
        pcbRecurrent:
          type: boolean
          description: clinical details property
        pmb:
          type: boolean
          description: clinical details property
        previousAIS:
          type: boolean
          description: clinical details property
        postMenopausal:
          type: boolean
          description: clinical details property
        postNatal:
          type: boolean
          description: clinical details property
        hrt:
          type: boolean
          description: clinical details property
        ocp:
          type: boolean
          description: clinical details property
        iucd:
          type: boolean
          description: clinical details property
        registerPapTest:
          type: boolean
          description: clinical details property
        urgent:
          type: boolean
          description: clinical details property
        urgentFax:
          type: string
          description: clinical details property
        urgentPhone:
          type: string
          description: clinical details property
        urgentBy:
          type: string
          description: clinical details property
        endometrium:
          type: boolean
          description: clinical details property
        other:
          type: boolean
          description: clinical details property
        abnormalBleeding:
          type: boolean
          description: clinical details property
        clinicalNotes:
          type: string
          description: clinical details property
        confidential:
          type: boolean
          description: clinical details property
        buClinicalNotes:
          type: string
          description: clinical details property
      required:
        - id
    rebatabilityType:
      type: string
      x-enum-varnames: [REBATABLE, NON_REBATABLE, CRITERIA_BASED]
      enum:
        - 'Rebatable'
        - 'Non-rebatable'
        - 'Criteria-based'
    orderableFindUs:
      type: object
      properties:
        id:
          type: string
          description: orderable find us property
        information:
          type: string
          description: orderable find us property
        duration:
          type: integer
          description: orderable find us property
        paymentRequiredIcon:
          type: boolean
          description: orderable find us property
        trmLink:
          type: string
          description: orderable find us property
    orderable:
      type: object
      properties:
        id:
          type: string
          description: orderable property
        name:
          type: string
          description: orderable property
        nameSearch:
          type: string
          description: orderable property
        code:
          type: string
          description: orderable property
        rcpaName:
          type: string
          description: orderable property
        rcpaNameSearch:
          type: string
          description: orderable property
        snomedName:
          type: string
          description: orderable property
        snomedNameSearch:
          type: string
          description: orderable property
        snomedCode:
          type: string
          description: orderable property
        snomedAUCode:
          type: string
          description: orderable property
        qmlName:
          type: string
          description: orderable property
        qmlCode:
          type: string
          description: orderable property
        lavName:
          type: string
          description: orderable property
        lavCode:
          type: string
          description: orderable property
        dorName:
          type: string
          description: orderable property
        dorCode:
          type: string
          description: orderable property
        wdpName:
          type: string
          description: orderable property
        wdpCode:
          type: string
          description: orderable property
        abbottName:
          type: string
          description: orderable property
        abbottCode:
          type: string
          description: orderable property
        tmlName:
          type: string
          description: orderable property
        tmlCode:
          type: string
          description: orderable property
        department:
          type: string
          description: orderable property
        synonyms:
          type: string
          description: orderable property
        synonymsSearch:
          type: string
          description: orderable property
        information:
          type: string
          description: orderable property
        commonTest:
          type: boolean
          description: orderable property
        isCytology:
          type: boolean
          description: orderable property
        findUsSpecialTest:
          type: boolean
          description: orderable property
        bookable:
          type: boolean
          description: orderable property
        timeslotBookable:
          type: boolean
          description: orderable property
        rebatability:
          $ref: '#/components/schemas/rebatabilityType'
        findUsBookingInfo:
          $ref: '#/components/schemas/orderableFindUs'
    sex:
      type: string
      x-enum-varnames: [M,F,U,O,A,N]
      enum:
        - 'Male'
        - 'Female'
        - 'Other or Unknown'
        - 'Other'
        - 'Ambiguous'
        - 'Not applicable'
    race:
      type: string
      x-enum-varnames: [ CODE_1, CODE_2, CODE_3, CODE_4, CODE_9 ]
      enum:
        - 'Aboriginal but not Torres Strait Islander origin'
        - 'Torres Strait Islander but Not Aboriginal origin'
        - 'Both Aboriginal and Torres Strait Islander origin'
        - 'Neither Aboriginal nor Torres Strait Islander origin'
        - 'Not Stated/inadequately described'
    pensionCode:
      type: string
      x-enum-varnames: [ N, P, R, L ]
      enum:
        - 'None'
        - 'Pension/HCC'
        - 'Full DVA'
        - 'Limited DVA'
    healthCardType:
      type: string
      x-enum-varnames: [ MC, NI, DVA, DVW, DVG, DVO, OTHER, PI, MR ]
      enum:
        - 'Medicare Card'
        - 'IHI Card'
        - 'DVA'
        - 'DVA Card White'
        - 'DVA Card Gold'
        - 'DVA Cart Orange'
        - 'Other Card'
        - 'PI'
        - 'Medical Record'
    dVACardType:
      type: string
      x-enum-varnames: [ DVW, DVG, DVO ]
      enum:
        - "white"
        - "Gold"
        - "Orange"
    medicalDirectorClinical:
      type: object
      properties:
        id:
          type: string
          description: medical director clinical property
        tempLabCode:
          type: string
          description: medical director clinical property
        accessToken:
          type: string
          description: medical director clinical property
        expiresIn:
          type: string
          description: medical director clinical property
          format: date-time
    referredPatient:
      type: object
      properties:
        id:
          type: string
          description: referred patient property
        familyName:
          type: string
          description: referred patient property
        familyNameSearch:
          type: string
          description: referred patient property
        givenName:
          type: string
          description: referred patient property
        givenNameSearch:
          type: string
          description: referred patient property
        middleNames:
          type: string
          description: referred patient property
        dateOfBirth:
          type: string
          format: date
          description: referred patient property
        age:
          type: integer
          description: referred patient property
        sex:
          $ref: '#/components/schemas/sex'
        race:
          $ref: '#/components/schemas/race'
        prefix:
          type: string
          description: referred patient property
        nameType:
          type: string
          description: referred patient property
        streetAddress:
          type: string
          description: referred patient property
        streetAddress2:
          type: string
          description: referred patient property
        city:
          type: string
          description: referred patient property
        state:
          type: string
          description: referred patient property
        postCode:
          type: string
          description: referred patient property
        country:
          type: string
          description: referred patient property
        addressType:
          type: string
          description: referred patient property
        mobilePhone:
          type: string
          description: referred patient property
        homePhone:
          type: string
          description: referred patient property
        homeEmail:
          type: string
          description: referred patient property
        businessPhone:
          type: string
          description: referred patient property
        businessEmail:
          type: string
          description: referred patient property
        medicareNo:
          type: string
          description: referred patient property
        medicareIndex:
          type: string
          description: referred patient property
        DVANo:
          type: string
          description: referred patient property
        IHI:
          type: string
          description: referred patient property
        healthCareCard:
          type: string
          description: referred patient property
        healthFundName:
          type: string
          description: referred patient property
        healthFundNo:
          type: string
          description: referred patient property
        healthFundExpiry:
          type: string
          description: referred patient property
        pensionNo:
          type: string
          description: referred patient property
        pensionCode:
          $ref: '#/components/schemas/pensionCode'
        assigningAuthority:
          type: string
          description: referred patient property
        identifierType:
          $ref: '#/components/schemas/healthCardType'
        dvaCardType:
          $ref: '#/components/schemas/dVACardType'
        urNo:
          type: string
          description: referred patient property
        mdPatientId:
          type: string
          description: referred patient property
        lightHouseId:
          type: string
          description: referred patient property
        hasEHealthRecord:
          type: boolean
          description: referred patient property
        allowContacting:
          type: boolean
          description: referred patient property
        ultraId:
          type: string
          description: referred patient property
    referrer:
      type: object
      properties:
        accessToken:
          type: string
          description: referrer property
        id:
          type: string
          description: referrer property
        idOld:
          type: string
          description: referrer property
        familyName:
          type: string
          description: referrer property
        familyNameSearch:
          type: string
          description: referrer property
        givenName:
          type: string
          description: referrer property
        givenNameSearch:
          type: string
          description: referrer property
        secondName:
          type: string
          description: referrer property
        title:
          type: string
          description: referrer property
        tempLabCode:
          type: string
          description: referrer property
        buildingName:
          type: string
          description: referrer property
        address:
          type: string
          description: referrer property
        city:
          type: string
          description: referrer property
        citySearch:
          type: string
          description: referrer property
        postCode:
          type: string
          description: referrer property
        state:
          type: string
          description: referrer property
        phoneNumber:
          type: string
          description: referrer property
        faxNumber:
          type: string
          description: referrer property
        email:
          type: string
          description: referrer property
        hasAddress:
          type: boolean
          description: referrer property
        createdOn:
          type: string
          description: referrer property
          format: date-time
        modifiedOn:
          type: string
          description: referrer property
          format: date-time
        enabled:
          type: boolean
          description: referrer property
        providerStem:
          type: string
          description: referrer property
        providerNumb:
          type: string
          description: referrer property
        prescriberNumb:
          type: string
          description: referrer property
        siteId:
          type: array
          items:
            type: string
        medicalDirectorClinical:
          $ref: '#/components/schemas/medicalDirectorClinical'
        organisationName:
          type: string
          description: referrer property
        departmentName:
          type: string
          description: referrer property
        leftPracticeDate:
          type: string
          description: referrer property
          format: date
        closedPractice:
          type: string
          description: referrer property
        accessToMedicalDirectorIFrame:
          type: boolean
          description: referrer property
        medWayId:
          type: string
          description: referrer property
        mdAddressBookId:
          type: string
          description: referrer property
        ultraReferrers:
          type: array
          items:
            $ref: '#/components/schemas/ultraReferrer'
    ultraReferrer:
      type: object
      properties:
        id:
          type: string
          description: ultra referrer property
        idOld:
          type: string
          description: ultra referrer property
        createdDateTime:
          type: string
          description: ultra referrer property
          format: date-time
        updatedDateTime:
          type: string
          description: ultra referrer property
          format: date-time
        company:
          $ref: '#/components/schemas/company'
    company:
      type: object
      properties:
        id:
          type: string
          description: company property
        idOld:
          type: string
          description: company property
        seq:
          type: integer
          description: company property
        buId:
          type: integer
          description: company property
        companyId:
          type: integer
          description: company property
        name:
          type: string
          description: company property
        shortName:
          type: string
          description: company property
        code:
          type: string
          description: company property
        nataCode:
          type: integer
          description: company property
        description:
          type: string
          description: company property
        smsUsername:
          type: string
          description: company property
        smsPassword:
          type: string
          description: company property
        eReferralMessage:
          type: string
          description: company property
        eReferralLateMessage:
          type: string
          description: company property
        eReferralErrorDOBMessage:
          type: string
          description: company property
        eReferralURL:
          type: string
          description: company property
        allowCollectorPortalR1:
          type: boolean
          description: company property
        allowCollectorPortal:
          type: boolean
          description: company property
        website:
          type: string
          description: company property
        statement:
          type: string
          description: company property
        additionalTestEmail:
          type: string
          description: company property
        medicineSpecialistConsultationEmail:
          type: string
          description: company property
        pendingAssociationNotificationEmail:
          type: string
          description: company property
        medWayEnabled:
          type: boolean
          description: company property
        state:
          type: string
          description: company property
        invoiceAddressBlurb:
          type: string
          description: company property

security:
  - BearerAuth: []