openapi: 3.0.1
info:
  title: SVC Medicare Validation API Client
  description: Healius Medicare and Overseas validation
  version: 1.0.0

tags:
  - name: validation
    description: Resource for validating medicare and overseas fund

paths:
  /v1/protocol/overseas:
    post:
      tags:
        - validation
      operationId: postValidationOverseas
      summary: Post overseas details to validate fund
      description: Post overseas details to validate fund
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OverseasValidationRequest'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthFundValidationResponse'
          description: successfully validation
        400:
          description: Bad request
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Not Found
        500:
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []
  /v1/protocol/dva:
    post:
      tags:
        - validation
      operationId: postValidationDva
      summary: Post dva details to validate
      description: Post dva details to validate
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DVAValidationRequest'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DVAValidationResponse'
          description: successfully validation
        400:
          description: Bad request
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Not Found
        500:
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: [ ]

  /v1/protocol/medicare:
    post:
      tags:
        - validation
      operationId: postValidationMedicare
      summary: Post medicare details to validate fund
      description: Post medicare details to validate fund
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MedicareValidationRequest'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MedicareValidationResponse'
          description: successfully validation
        400:
          description: Bad request
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Not Found
        500:
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

components:
  schemas:
    RestError:
      title: REST Error
      description: The schema for all error responses.
      type: object
      properties:
        status:
          title: Status
          description: The HTTP status code.
          type: integer
          format: int32
          example: 400
          readOnly: true
        error:
          title: Error
          description: The short error message.
          type: string
          example: Bad Request
          readOnly: true
        path:
          title: Path
          description: The path of the URL for this request.
          type: string
          format: uri
          example: /api/owners
          readOnly: true
        timestamp:
          title: Timestamp
          description: The time the error occured.
          type: string
          format: date-time
          example: 2019-08-21T21:41:46.158+0000
          readOnly: true
        message:
          title: Message
          description: The long error message.
          type: string
          example: Request failed schema validation
          readOnly: true
        schemaValidationErrors:
          title: Schema validation errors
          description: Validation errors against the OpenAPI schema.
          type: array
          items:
            $ref: '#/components/schemas/ValidationMessage'
        trace:
          title: Trace
          description: The stacktrace for this error.
          type: string
          readOnly: true
      required:
        - status
        - error
        - path
        - timestamp
        - message
        - schemaValidationErrors
    DVAValidationRequest:
      type: object
      properties:
        dvaNumber:
          type: string
          description: DVA number of the patient
        familyName:
          type: string
          description: Family name of the patient
        givenName:
          type: string
          description: Given name of the patient.
        secondInitial:
          type: string
          description: Second initial of the patient
        patientDoB:
          type: string
          format: dd/MM/yyyy
          description: patient dob
        sex:
          type: string
          description: Sex of the patient
    DVAValidationResponse:
      type: object
      properties:
        dvaCardNo:
          type: string
          description: medicare Card No
        hasCover:
          type: boolean
          description: validation Text
    MedicareValidationResponse:
      type: object
      properties:
        medicareCardNo:
          type: string
          description: medicare Card No
        hasCover:
          type: boolean
          description: validation Text
        medicare:
          $ref: '#/components/schemas/MedicareResponse'
    HealthFundValidationResponse:
      type: object
      properties:
        healthFundNo:
          type: string
          description: health fund Card No
        hasCover:
          type: boolean
          description: validation Text
        medicare:
          $ref: '#/components/schemas/MedicareResponse'
    MedicareValidationRequest:
      type: object
      properties:
        familyName:
          type: string
          description: family Name
        givenName:
          type: string
          description: given Name
        patientDoB:
          type: string
          format: dd/MM/yyyyy
          description: patient dob
        medicareNumber:
          type: string
          description: medicare Card No
        memberRefNumber:
          type: string
          description: medicare number ref
        secondInitial:
          type: string
          description: patient second initial
        sex:
          type: string
          description: patient sex

    MedicareResponse:
      type: object
      properties:
        text:
          type: string
          description: Response text from medicare
        code:
          type: string
          description: Response code from medicare
        correctedCardNumber:
          type: string
          description: Replacement card number sent from Medicare
        correctedCardRefNo:
          type: string
          description: Replacemnt reference number sent from Medicare
        correctedGivenName:
          type: string
          description: Medicare corrected given name sent from Medicare
        errors:
          type: array
          description: List of errors returned from Medicare
          items:
            $ref: '#/components/schemas/MedicareErrors'

    MedicareErrors:
      type: object
      properties:
        code:
          type: string
          description: Specific error code returned via Medicare
        text:
          type: string
          description: Detailed description of the error occured

    OverseasValidationRequest:
      type: object
      properties:
        familyName:
          type: string
          description: family Name
        givenName:
          type: string
          description: given Name
        healthFundCode:
          type: string
          description: health fund code
        healthFundNumber:
          type: string
          description: health fund number
        healthFundRefNumber:
          type: string
          description: request health fund ref number
        patientDoB:
          type: string
          format: dd/MM/yyyyy
          description: patient dob
        secondInitial:
          type: string
          description: patient second initial
        sex:
          type: string
          description: patient sex

    ValidationMessage:
      title: Validation message
      description: Messages describing a validation error.
      type: object
      properties:
        message:
          title: Message
          description: The validation message.
          type: string
          readOnly: true
      required:
        - message
      additionalProperties: true

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT