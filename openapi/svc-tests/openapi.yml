openapi: 3.1.0
info:
  title: SVC Tests API
  description: Healius Test api
  version: 1.0.0

tags:
  - name: orderable
    description: Everything about Orderables
  - name: orderableAI
    description: Everything about Orderables and Groups for AI
  - name: company
    description: Everything about Companies
  - name: orderableFindUs
    description: Everything about Orderable FindUs
  - name: orderableGrouping
    description: Everything about Orderable Groupings
  - name: companyOrderable
    description: Everything about Company Orderables
  - name: instantResults
    description: Everything about the Instant
  - name: container
    description: Everything about Containers
  - name: containerType
    description: Everything about Container Types
  - name: specimenType
    description: Everything about Specimen Types


paths:
  /v1/orderable:
    get:
      tags:
        - orderable
      operationId: listOrderable
      summary: List all orderables
      description: Return all object of orderables
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/orderable/common:
    get:
      tags:
        - orderable
      operationId: listOrderableCommon
      summary: List all common orderables
      description: Return all object of common orderables
      parameters:
        - name: bu
          in: query
          description: bu code string.
          required: true
          schema:
            type: string
            example: abt, lav, dor
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/orderable/{id}:
    get:
      tags:
        - orderable
      operationId: getOrderable
      summary: Find orderable by id
      description: Returns an optional of orderable
      parameters:
        - name: id
          in: path
          description: id of orderable.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []


  /v1/orderable/findBySnomedLocal:
    get:
      tags:
        - orderable
      operationId: searchBySnomedLocal
      summary: Find by snomed local
      description: Return an object of orderable
      parameters:
        - name: id
          in: query
          description: snomedLocal of orderable.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/orderable/search:
    get:
      tags:
        - orderable
      operationId: findBySearch
      summary: Find by search string
      description: Returns an array of orderables.
      parameters:
        - name: bu
          in: query
          description: bu code string.
          required: true
          schema:
            type: string
            example: abt, lav, dor
        - name: application
          in: query
          description: application code string.
          required: true
          schema:
            type: string
            enum:
              - PMS
              - CP
              - DEP
              - LAB
            example: PMS, CP, DEP, LAB
        - name: search
          in: query
          description: search string.
          required: true
          schema:
            type: string
            example: FBE or full blood
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []
  /v1/orderable/searchByOrderable:
    get:
      tags:
        - orderable
      operationId: adminSearchOrderables
      summary: Find by search string
      description: Returns an array of orderables.
      parameters:
        - name: application
          in: query
          description: application code string.
          required: true
          schema:
            type: string
            enum:
              - PMS
              - CP
              - DEP
              - LAB
            example: PMS, CP, DEP, LAB
        - name: search
          in: query
          description: search string.
          required: true
          schema:
            type: string
            example: FBE or full blood
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/orderableGrouping/findBySearch:
    get:
      tags:
        - orderableGrouping
      operationId: findGroupedBySearch
      summary: Find by search string
      description: Returns an array of grouped orderables.
      parameters:
        - name: bu
          in: query
          description: bu code string.
          required: true
          schema:
            type: string
            example: abt, lav, dor
        - name: search
          in: query
          description: search string.
          required: true
          schema:
            type: string
            example: FBE or full blood
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderableGroupingResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: [ ]
  /v1/orderable/ai:
    get:
      tags:
        - orderableAI
      operationId: getAIListing
      summary: List all for AI
      description: Returns a list of orderables for AI
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                  $ref: '#/components/schemas/ModelAPIResponse'
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/orderableGrouping/ai:
    get:
      tags:
        - orderableAI
      operationId: getAIGroupingListing
      summary: Get AI-optimized orderable groupings
      description: Returns a paginated list of orderable groupings specifically formatted for AI processing. The response includes metadata about the pagination and the formatted grouping data.
      responses:
        200:
          description: Successfully retrieved orderable groupings
          content:
            application/json:
              schema:
                  $ref: '#/components/schemas/ModelAPIResponse'
        '500':
          description: Internal server error occurred while processing the request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/orderableGrouping:
    get:
      tags:
        - orderableGrouping
      operationId: listOrderableGrouping
      summary: List all orderable grouping
      description: Returns an array of orderables grouping.
      responses:
        200:
          description: orderable group successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderableGroupingResponse'
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []


  /v1/orderableGrouping/{id}:
    get:
      tags:
        - orderableGrouping
      operationId: getOrderableGrouping
      summary: Find Orderable Grouping by id
      description: Returns an optional of Orderable Grouping
      parameters:
        - name: id
          in: path
          description: id of orderable.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: orderable Grouping successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderableGroupingResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Orderable Grouping not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/companyOrderable/{id}:
    get:
      tags:
        - companyOrderable
      operationId: getCompanyOrderableById
      summary: Find Company Orderable by id
      description: Returns an optional of Company Orderable
      parameters:
        - name: id
          in: path
          description: id of orderable.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: Company Orderable successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyOrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Company Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/companyOrderable/findByOrderable:
    get:
      tags:
        - companyOrderable
      operationId: companyOrderableFindByOrderable
      summary: Find by orderable string
      description: Returns an array of company orderable.
      parameters:
        - name: orderable
          in: query
          description: search string.
          required: true
          schema:
            type: string
            example: UUID
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CompanyOrderableResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Company Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/companyOrderable/findTrmByOrderable/{id}:
    get:
      tags:
        - companyOrderable
      operationId: companyOrderableFindTrmByOrderable
      summary: Find by orderable string
      description: Returns the test reference manual of a given company orderable
      parameters:
        - name: id
          in: path
          description: id of orderable.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: TRM content successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyOrderableTRMResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Company Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: [ ]

  /v1/companyOrderable/getBasicTrm/{id}:
    get:
      tags:
        - companyOrderable
      operationId: companyOrderableBasicTrm
      summary: Get basic trm content
      description: Returns the basic test reference manual of a given company orderable
      parameters:
        - name: id
          in: path
          description: id of orderable.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: TRM content successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyOrderableBasicTRMResponse'
        '400':
          description: Invalid ID supplied
        '404':
          description: Company Orderable not found
        '422':
          description: Validation exception
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: [ ]

  /v1/company:
    get:
      tags:
        - company
      operationId: listCompanies
      summary: List all companies
      description: Return all object of orderables
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CompanyResponse'
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/company/{id}:
    get:
      tags:
        - company
      operationId: getCompanies
      summary: Get company by id
      description: Return a company
      parameters:
        - name: id
          in: path
          description: id of orderable.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: orderable successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyResponse'
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/instantResults/{id}:
    get:
      tags:
        - instantResults
      operationId: getInstantResult
      summary: Get instant results by id
      description: Return a instantResult
      parameters:
        - name: id
          in: path
          description: id of instant Result.
          required: true
          schema:
            type: string
            example: *********
      responses:
        200:
          description: instant results successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InstantResultsResponse'
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/instantResults:
    get:
      tags:
        - instantResults
      operationId: listInstantResults
      summary: List all instant results
      description: Return all object of instant results
      responses:
        200:
          description: instantResults successfully returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InstantResultsResponse'
        '500':
          description: Server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/container:
    get:
      tags:
        - container
      operationId: listContainer
      summary: List all containers
      description: Retrieves a list of all containers sorted alphabetically. If active parameter is provided, returns only active containers.
      responses:
        200:
          description: Successfully retrieved list of containers
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Container'
        403:
          description: Access denied - insufficient permissions
        500:
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/container/{id}:
    get:
      tags:
        - container
      operationId: getContainer
      summary: Get container by ID
      description: Retrieves detailed information about a specific container by its ID
      parameters:
        - name: id
          in: path
          description: ID of the container to retrieve
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successfully retrieved container details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Container'
        403:
          description: Access denied - insufficient permissions
        404:
          description: Container not found
        500:
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/container/company/{code}:
    get:
      tags:
        - container
      operationId: getByCompanyContainer
      summary: Get containers by company
      description: Retrieves all containers associated with a specific company, sorted alphabetically
      parameters:
        - name: code
          in: path
          description: code of the company
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successfully retrieved company containers
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Container'
        403:
          description: Access denied - insufficient permissions
        404:
          description: Company not found
        500:
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/containerType:
    get:
      tags:
        - containerType
      operationId: listContainerType
      summary: List all container types
      description: Retrieves a list of all container types sorted alphabetically. If active parameter is provided, returns only active container types.
      responses:
        200:
          description: Successfully retrieved list of container types
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContainerType'
        400:
          description: Invalid input data provided
        422:
          description: Validation exception
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/containerType/{containerTypeId}:
    get:
      tags:
        - containerType
      operationId: getContainerType
      summary: Get container type details
      description: Retrieves detailed information about a specific container type
      parameters:
        - name: containerTypeId
          in: path
          description: ID of the container type to retrieve
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successfully retrieved container type details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContainerType'
        400:
          description: Invalid input data provided
        422:
          description: Validation exception
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/specimenType:
    get:
      tags:
        - specimenType
      operationId: listSpecimenTypes
      summary: List all specimen types
      description: Retrieves a list of all specimen types.
      responses:
        200:
          description: Successfully retrieved list of specimen types
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SpecimenType'
        403:
          description: Access denied - insufficient permissions
        500:
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

  /v1/specimenType/{id}:
    get:
      tags:
        - specimenType
      operationId: getSpecimenType
      summary: Get specimen type by ID
      description: Retrieves detailed information about a specific specimen type by its ID.
      parameters:
        - name: id
          in: path
          description: ID of the specimen type to retrieve
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successfully retrieved specimen type details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpecimenType'
        400:
          description: Invalid ID supplied
        403:
          description: Access denied - insufficient permissions
        404:
          description: Specimen Type not found
        500:
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestError'
      security:
        - bearerAuth: []

components:
  schemas:
    RestError:
      title: REST Error
      description: The schema for all error responses.
      type: object
      properties:
        status:
          title: Status
          description: The HTTP status code.
          type: integer
          format: int32
          example: 400
          readOnly: true
        error:
          title: Error
          description: The short error message.
          type: string
          example: Bad Request
          readOnly: true
        path:
          title: Path
          description: The path of the URL for this request.
          type: string
          format: uri
          example: /api/owners
          readOnly: true
        timestamp:
          title: Timestamp
          description: The time the error occurred.
          type: string
          example: 2019-08-21T21:41:46.158+0000
          readOnly: true
        message:
          title: Message
          description: The long error message.
          type: string
          example: Request failed schema validation
          readOnly: true
        schemaValidationErrors:
          title: Schema validation errors
          description: Validation errors against the OpenAPI schema.
          type: array
          items:
            $ref: '#/components/schemas/ValidationMessage'
        trace:
          title: Trace
          description: The stacktrace for this error.
          type: string
          readOnly: true
      required:
        - status
        - error
        - path
        - timestamp
        - message
        - schemaValidationErrors

    ModelAPIResponse:
      type: object
      description: Standard response wrapper used across all API endpoints
      x-builder-pattern: true
      properties:
        data:
          description: The payload data of the response, can be an object or an array
          oneOf:
            - type: object
              description: Object response payload
            - type: array
              description: Array response payload
              items:
                type: object
        message:
          type: string
          description: Response message
          example: 'Successfully retrieved data'
        responseCode:
          type: integer
          description: Spring HttpStatus enum value
          example: 200
        pageable:
          $ref: '#/components/schemas/ModelApiResponsePageable'

    ModelApiResponsePageable:
      type: object
      description: Pagination details when applicable
      x-builder-pattern: true
      properties:
        pageNumber:
          type: integer
          example: 0
        pageSize:
          type: integer
          example: 20
        totalPages:
          type: integer
          example: 5
        totalElements:
          type: integer
          example: 100

    CompanyResponse:
      type: object
      properties:
        id:
          type: string
          description: Company property Id
        name:
          type: string
          description: Company property Name
        shortName:
          type: string
          description: Company property Short Name
        code:
          type: string
          description: Company property Code

    CompanyInstantResultPropertiesResponse:
      type: object
      properties:
        id:
          type: string
        createdDate:
          type: string
          format: date-time
        createdBy:
          type: string
        modifiedDate:
          type: string
          format: date-time
        modifiedBy:
          type: string
        enabled:
          type: boolean
        label:
          type: string
        value:
          type: string

    ContainerCompanyResponse:
      type: object
      properties:
        id:
          type: string
        count:
          type: integer
        container:
          $ref: '#/components/schemas/Container'
        sequence:
          type: integer
        specimenType:
          $ref: '#/components/schemas/SpecimenType'
        containerType:
          $ref: '#/components/schemas/ContainerType'
        clinicalDepartment:
          type: string
        turnAroundTime:
          type: string
        testFrequency:
          type: string
        frozenExceptionForRegional:
          type: string
        bodyTemperature:
          type: boolean
        cold:
          type: boolean
        frozen:
          type: boolean
        roomTemperature:
          type: boolean
        iceBrick:
          type: boolean
        exposure:
          type: boolean
        freeze:
          type: boolean
        questionnaire:
          type: boolean
        attention:
          type: boolean
        meds:
          type: boolean
        TDD:
          type: boolean
        dryIce:
          type: boolean
        cover:
          type: boolean
        hAndW:
          type: boolean
        TDAD:
          type: boolean
        glucLoad:
          type: boolean
        urgent:
          type: boolean
        priority:
          type: boolean
        dose:
          type: boolean
        doNotCentrifuge:
          type: boolean
        centrifuge:
          type: boolean
        dedicatedTube:
          type: boolean
        createdDate:
          type: string
          format: date-time
        createdBy:
          type: string
        modifiedDate:
          type: string
          format: date-time
        modifiedBy:
          type: string
        enabled:
          type: boolean

    CompanyInstantResultResponse:
      type: object
      properties:
        id:
          type: string
        createdDate:
          type: string
          format: date-time
        createdBy:
          type: string
        modifiedDate:
          type: string
          format: date-time
        modifiedBy:
          type: string
        enabled:
          type: boolean
        company:
          $ref: '#/components/schemas/CompanyResponse'
        instantResultPropertiesList:
          type: array
          items:
            $ref: '#/components/schemas/CompanyInstantResultPropertiesResponse'
        uniqueItemCode:
          type: string
        active:
          type: boolean

    InstantResultsResponse:
      type: object
      properties:
        id:
          type: string
          description: Instant results property Id
        name:
          type: string
          description: Instant results property name
        type:
          $ref: '#/components/schemas/InstantResultsType'
        displayType:
          $ref: '#/components/schemas/InstantResultsDisplayType'
          description: Display type for instant result
        measurement:
          type: string
          description: Instant results property measurement
        minValue:
          type: number
          description: Instant results property minValue
        maxValue:
          type: number
          description: Instant results property maxValue
        decimals:
          type: integer
          description: Instant results property decimals
        errorMessage:
          type: string
          description: Instant results property errorMessage
        active:
          type: boolean
          description: Instant results property active
        orderables:
          type: array
          items:
            $ref: '#/components/schemas/OrderableResponse'
        companyInstantResults:
          type: array
          items:
            $ref: '#/components/schemas/CompanyInstantResultResponse'
          description: List of company instant results
        createdDate:
          type: string
          format: date-time
          description: Date when the instant result was created
        createdBy:
          type: string
          description: User who created the instant result
        modifiedDate:
          type: string
          format: date-time
          description: Date when the instant result was last modified
        modifiedBy:
          type: string
          description: User who last modified the instant result
        enabled:
          type: boolean
          description: Whether the instant result is enabled

    RebatabilityType:
      type: string
      enum:
        - REBATABLE
        - NON_REBATABLE
        - CRITERIA_BASED
        - PRE_PAYMENT


    CompanyOrderableBasicTRMResponse:
      type:
      properties:
        icons:
          type: array
          items:
            $ref: '#/components/schemas/HandlingIconsResponse'
        colour:
            type: string
        hasDetailedTRM:
          type: boolean

    CompanyOrderableTRMResponse:
      type: object
      properties:
        name:
          type: string
          description: Name shown on the TRM content
        code:
          type: string
          description: Bu mapping panel code
        sort:
          type: string
          description: BU mapping sorting
        mustBeFasting:
          type: boolean
          description: Boolean value for fasting
        recordSiteOfColletion:
          type: boolean
          description: Boolean value to indicated if collector should record collection site
        generalClinicalNotes:
          type: boolean
          description: Boolean value of general clinical notes
        specificClinicalNotes:
          type: boolean
          description: Boolean value for general clinical notes
        specificClinicalNotesText:
          type: string
          description: Description of the specific clinical notes
        sendAway:
          type: boolean
          description: Boolean value for sendAway
        sendAwayText:
          type: string
          description: Text for sendway desitination
        collectionSOP:
          type: string
          description: Collection standard operating procedure
        collectionSOPLink:
          type: string
          description: Link for the SOP
        questionnaire:
          type: string
          description: Link for questionnaire
        questionnaireLink:
          type: string
          description: Link for the questionnaire
        patientPrep1:
          type: string
          description: String value for patient prepartion
        patientPrep1Link:
          type: string
          description: Link to patient prep 1
        patientPrep2:
          type: string
          description: String value for patient prepartion
        patientPrep2Link:
          type: string
          description: Link for prep 2 link
        instructions:
          type: string
          description: String value for handing instructions within the TRM content
        containerRule:
          $ref: '#/components/schemas/ContainerRule'
        containers:
          type: array
          items:
            $ref: '#/components/schemas/CompanyTRMContainersResponse'

    CompanyTRMContainersResponse:
      type: object
      properties:
        mainIcon:
          type: string
          description: Header icon used in TRM content
        name:
          type: string
          description: name of the CompanyOrderable
        storageIcon:
          $ref: '#/components/schemas/HandlingIconsResponse'
        specification:
          type: string
          description: Specifications used in TRM content
        icons:
          type: array
          items:
            $ref: '#/components/schemas/HandlingIconsResponse'
        container:
          type: string
          description: Container name shown on TRM content.
        specimenType:
          type: string
          description: Specimen type used in companyOrderable
        containerType:
          type: string
          description: Container type used in companyOrderbale
        department:
          type: string
          description: String value for department
        turnAroundTime:
          type: string
          description: Turn around time for specimen
        testFrequency:
          type: string
          description: Frequency of test
        frozenException:
          type: string
          description: String value for frozen exception

    HandlingIconsResponse:
      type: object
      properties:
        tooltip:
          type: string
          description: Text shown when hovering a icon
        icon:
          type: string
          description: Svg icon to be used

    InstantResultsType:
      type: string
      x-enum-varnames: [ DECODE, DATE, TEXT, NUMERIC, TIME ]
      enum:
        - 'DECODE'
        - 'DATE'
        - 'TEXT'
        - 'NUMERIC'
        - 'TIME'

    CollectionFrequency:
      type: string
      x-enum-varnames: [ HOURS, DAYS ]
      enum:
        - 'HOURS'
        - 'DAYS'

    OrderableVisibility:
      type: string
      x-enum-varnames: [ PMS, CP, DEP,LAB ]
      enum:
        - 'PMS'
        - 'CP'
        - 'DEP'
        - 'LAB'

    OrderablePaymentResponse:
      type: object
      properties:
        id:
          type: string
          description: Orderable Payment property ID
        price:
          type: number
          description: Orderable Payment property Price
        mbs:
          type: string
          description: Orderable Payment property MBS
        tier:
          type: boolean
          description: Orderable Payment property Tier
        allowPriceOverride:
          type: boolean
          description: Orderable Payment property Allow Price Override
        minPriceOverride:
          type: number
          description: Orderable Payment property Min price
        patientSelfClaim:
          type: boolean
          description: Orderable Payment property patient self claim
        exemptionTest:
          type: boolean
          description: Orderable Payment property is exception

    OrderableBUResponse:
      type: object
      properties:
        id:
          type: string
          description: Orderable BU property Id
        name:
          type: string
          description: Orderable BU property Name
        code:
          type: string
          description: Orderable BU property Code
        specimenRequired:
          type: integer
          description: Amount of required specimens
          minimum: 1
          maximum: 9
        collectionFrequency:
          $ref: '#/components/schemas/CollectionFrequency'
        enabled:
          type: boolean
          description: Company Orderable enabled

    OrderableTRMResponse:
      type: object
      properties:
        id:
          type: string
          description: Orderable TRM property Id
        trm:
          type: string
          description: Orderable TRM property TRM

    OrderableResponse:
      type: object
      properties:
        id:
          type: string
          description: orderable property id
        companyOrderableId:
          type: string
          description: Company orderable id
        name:
          type: string
          description: orderable property name
        nameSearch:
          type: string
          description: Searchable name
        code:
          type: string
          description: orderable property code
        rcpaName:
          type: string
          description: orderable property RCPA Name
        rcpaNameSearch:
          type: string
          description: Searchable RCPA name
        snomedName:
          type: string
          description: orderable property Snomed Name
        snomedNameSearch:
          type: string
          description: Searchable Snomed name
        snomedCode:
          type: string
          description: orderable property Snomed Code
        snomedAUCode:
          type: string
          description: orderable property Snomed AU Code
        department:
          type: string
          description: orderable property Department
        synonyms:
          type: string
          description: orderable property Synonyms
        synonymsList:
          type: array
          items:
            type: string
        synonymList:
          type: array
          items:
            type: string
        synonymsSearch:
          type: string
          description: Searchable synonyms
        information:
          type: string
          description: orderable property Information
        resultsAvailable:
          type: string
          description: orderable property Results Available
        testFrequency:
          type: string
          description: orderable property Test Frequency
        prepaymentUrl:
          type: string
          description: orderable property PrePayment URL
        commonTest:
          type: boolean
          description: orderable property Common Test
        testAvailable:
          type: boolean
          description: orderable property Test Available
        isCytology:
          type: boolean
          description: orderable property Is a Cytology
        findUsSpecialTest:
          type: boolean
          description: orderable property Find Us Special Test
        isReplaceByOtherTest:
          type: boolean
          description: orderable property replace by other test
        replacedByOtherTest:
          type: boolean
          description: Whether this orderable is replaced by another test
        currentOrderable:
          $ref: '#/components/schemas/OrderableResponse'
        currentOrderableId:
          type: string
          description: orderable property current orderable Id
        currentOrderableEffectiveDate:
          type: string
          description: orderable property current effective date
        formerOrderable:
          $ref: '#/components/schemas/OrderableResponse'
        formerOrderableId:
          type: string
          description: orderable property former orderable id
        formerOrderableEffectiveDate:
          type: string
          description: orderable property former effective date
        rebatability:
          $ref: '#/components/schemas/RebatabilityType'
        visibilities:
          type: array
          items:
            $ref: '#/components/schemas/OrderableVisibility'
        findUsBookingInfo:
          type: string
          description: Orderable find us property Find Us Id
        specimenRequired:
          type: integer
          description: Amount of required specimens
        collectionFrequency:
          $ref: '#/components/schemas/CollectionFrequency'
        createdDate:
          type: string
          description: Date when the orderable was created
        createdBy:
          type: string
          description: User who created the orderable
        modifiedDate:
          type: string
          description: Date when the orderable was last modified
        modifiedBy:
          type: string
          description: User who last modified the orderable
        enabled:
          type: boolean
          description: Whether the orderable is enabled

    OrderableCompanyResponse:
      type: object
      properties:
        id:
          type: string
          description: orderable property id of company orderable
        orderableId:
          type: string
          description: orderable property id of orderable
        name:
          type: string
          description: orderable property name
        code:
          type: string
          description: orderable property code
        synonymsList:
          type: array
          items:
            type: string
          description: List of synonyms for the orderable
        commonTest:
          type: boolean
          description: orderable property Common Test
        testAvailable:
          type: boolean
          description: orderable property Test Available
        isReplaceByOtherTest:
          type: boolean
          description: orderable property replace by other test
        currentOrderable:
          $ref: '#/components/schemas/OrderableResponse'
        currentOrderableEffectiveDate:
          type: string
          description: orderable property current effective date
        rebatability:
          $ref: '#/components/schemas/RebatabilityType'
        visibilities:
          type: array
          items:
            $ref: '#/components/schemas/OrderableVisibility'
        specimenRequired:
          type: integer
          description: Amount of required specimens
        collectionFrequency:
          $ref: '#/components/schemas/CollectionFrequency'
        createdDate:
          type: string
          description: Date when the orderable was created
        createdBy:
          type: string
          description: User who created the orderable
        enabled:
          type: boolean
          description: Whether the orderable is enabled
        bu:
          type: string
          description: Business unit code
        synonyms:
          type: string
          description: Comma-separated list of synonyms
        replacedByOtherTest:
          type: boolean
          description: Whether this orderable is replaced by another test
        currentOrderableId:
          type: string
          description: ID of the current orderable
        currentOrderableName:
          type: string
          description: Name of the current orderable
        formerOrderableId:
          type: string
          description: ID of the former orderable
        formerOrderableName:
          type: string
          description: Name of the former orderable
        currentOrderableEffectDate:
          type: string
          description: Effective date of the current orderable

    CompanyOrderableResponse:
      type: object
      properties:
        id:
          type: string
          description: Company Orderable id
        trm:
          type: string
          description: Company Orderable trm
        mbs:
          type: string
          description: Company Orderable MBS
        code:
          type: string
          description: Company Orderable code
        name:
          type: string
          description: Company Orderable name
        company:
          $ref: '#/components/schemas/CompanyResponse'
        orderable:
          type: string
          description: Company Orderable Orderable
        tier:
          type: boolean
          description: Company Orderable tier
        allowPriceOverride:
          type: boolean
          description: Company Orderable allow price override
        minPriceOverride:
          type: number
          description: Company Orderable min price override
        patientSelfClaim:
          type: boolean
          description: Company Orderable patient self claim
        exemptionTest:
          type: boolean
          description: Company Orderable is exception test
        price:
          type: number
          description: Company Orderable price
        specimenRequired:
          type: integer
          description: Amount of required specimens
          minimum: 1
          maximum: 9
        collectionFrequency:
          $ref: '#/components/schemas/CollectionFrequency'
        modifiedBy:
          type: string
          description: Company Orderable modified by
        enabled:
          type: boolean
          description: Company Orderable enabled
        containers:
          type: array
          items:
            $ref: '#/components/schemas/ContainerCompanyResponse'
        buSortDestination:
          type: string
          description: BU sort destination
        clinicalNotesSpecificText:
          type: string
          description: Clinical notes specific text
        generalClinicalNotes:
          type: boolean
          description: General clinical notes
        sendAwayTest:
          type: boolean
          description: Send away test
        sendAwayTestDestination:
          type: string
          description: Send away test destination
        recordSiteOfCollection:
          type: boolean
          description: Record site of collection
        mustBeFasting:
          type: boolean
          description: Must be fasting
        lastDose:
          type: boolean
          description: Last dose
        collectionSOP:
          type: string
          description: Collection SOP
        collectionSOPLink:
          type: string
          description: Collection SOP
        questionnaire:
          type: string
          description: Questionnaire
        questionnaireLink:
          type: string
          description: Questionnaire link
        patientPreparation1:
          type: string
          description: Patient preparation 1
        patientPreparation1Link:
          type: string
          description: Patient preparation 1 link
        patientPreparation2:
          type: string
          description: Patient preparation 2
        patientPreparation2Link:
          type: string
          description: Patient preparation 2
        handlingAndSpecialInstructions:
          type: string
          description: Handling and special instructions
        containerRule:
          $ref: '#/components/schemas/ContainerRule'
          description: Container rule
        createdDate:
          type: string
          format: date-time
          description: Date when the company orderable was created
        createdBy:
          type: string
          description: User who created the company orderable
        modifiedDate:
          type: string
          format: date-time
          description: Date when the company orderable was last modified

    OrderableGroupingResponse:
      type: object
      properties:
        id:
          type: string
          description: Orderable Grouping id
        company:
          $ref: '#/components/schemas/CompanyResponse'
        orderables:
          type: array
          items:
            $ref: '#/components/schemas/OrderableResponse'
        rebatability:
          $ref: '#/components/schemas/RebatabilityType'
        code:
          type: string
          description: Orderable Grouping code
        name:
          type: string
          description: Orderable Grouping name
        nameSearch:
          type: string
          description: Searchable name
        snomedLocal:
          type: string
          description: Orderable Grouping snomed local
        synonyms:
          type: string
          description: Synonyms as string
        synonymsList:
          type: array
          items:
            type: string
        synonymList:
          type: array
          items:
            type: string
        hideFromSearch:
          type: boolean
          description: Orderable Grouping hide from search
        weight:
          type: integer
          description: Orderable Grouping weight
        price:
          type: number
          description: Orderable Grouping price
        bu:
          type: string
          description: Business unit code
        createdDate:
          type: string
          description: Date when the grouping was created
        createdBy:
          type: string
          description: User who created the grouping
        modifiedDate:
          type: string
          description: Date when the grouping was last modified
        modifiedBy:
          type: string
          description: User who last modified the grouping
        enabled:
          type: boolean
          description: Whether the grouping is enabled

    OrderableGroupingRequest:
      type: object
      properties:
        id:
          type: string
          description: Orderable Grouping id
        company:
          type: string
          description: Company Grouping id
        rebatability:
          $ref: '#/components/schemas/RebatabilityType'
        code:
          type: string
          description: Orderable Grouping code
        name:
          type: string
          description: Orderable Grouping name
        synonymsList:
          type: array
          items:
            type: string
        hideFromSearch:
          type: boolean
          description: Orderable Grouping hide from search
        weight:
          type: integer
          description: Orderable Grouping weight
        price:
          type: number
          description: Orderable Grouping price
        modifiedBy:
          type: string
          description: modified by property
        enabled:
          type: boolean
          description: Orderable Grouping enabled
    BUCodes:
      type: object
      properties:
        buCode:
          type: string
          description: BU name
        code:
          type: string
          description: test code
        name:
          type: string
          description: test name

    OrderableToAI:
      type: object
      properties:
        id:
          type: string
          description: Orderable id
        name:
          type: string
          description: Orderable name
        snomedCTAU:
          type: string
          description: Snomed CT-AU code
        snomedLocal:
          type: string
          description: Snomed Local code
        synonyms:
          type: string
          description: String of synonyms
        synonymList:
          type: array
          items:
            type: string
        buCodes:
          type: array
          items:
            $ref: '#/components/schemas/BUCodes'

    OrderableGroupingToAI:
      type: object
      properties:
        id:
          type: string
          description: Orderable Grouping id
        name:
          type: string
          description: Orderable Grouping name
        snomedLocal:
          type: string
          description: Orderable Grouping snomed local
        bu:
          type: string
          description: Orderable Grouping bu code
        synonymList:
          type: array
          items:
            type: string
        synonyms:
          type: string

    ValidationMessage:
      title: Validation message
      description: Messages describing a validation error.
      type: object
      properties:
        message:
          title: Message
          description: The validation message.
          type: string
          readOnly: true
      required:
        - message
      additionalProperties: true

    Container:
      type: object
      properties:
        id:
          type: string
          description: Container ID
        name:
          type: string
          description: Container name
        active:
          type: boolean
          description: Whether the container is active
        company:
          $ref: '#/components/schemas/CompanyResponse'
          description: Associated company
        description:
          type: string
          description: Description of container
        labelCount:
          type: integer
          description: Number of labels
        summaryLabel:
          type: string
          description: Summary label
        colourDescription:
          type: string
          description: Colour description
        specification:
          type: string
          description: Specification
        textColourCode:
          type: string
          description: Text colour code
        fillColourCode:
          type: string
          description: Fill colour code
        borderColourCode:
          type: string
          description: Border colour code
        sequence:
          type: integer
          description: Sequence number
        image:
          type: string
          description: Image enum as string
        containerLayout:
          type: string
          description: Container layout enum as string
        commonContainer:
          type: boolean
          description: Is common container
        createdDate:
          type: string
          description: Date when the container was created
        createdBy:
          type: string
          description: User who created the container
        modifiedDate:
          type: string
          description: Date when the container was last modified
        modifiedBy:
          type: string
          description: User who last modified the container
        enabled:
          type: boolean
          description: Whether the container is enabled
        containerType:
          $ref: '#/components/schemas/ContainerType'
          description: The type of this container

    ContainerType:
      type: object
      properties:
        id:
          type: string
          description: Container Type ID
        name:
          type: string
          description: Container Type name
        active:
          type: boolean
          description: Whether the container type is active
        sequence:
          type: integer
          description: Sequence number
        hidden:
          type: boolean
          description: Toggle to show / hide container types from UI. Use for importing ONLY
        commonContainerType:
          type: boolean
          description: Toggle to show on collector portal.
        icon:
          type: string
          description: Icon enum as string
        containers:
          type: array
          items:
            $ref: '#/components/schemas/Container'
        createdDate:
          type: string
          description: Date when the container type was created
        createdBy:
          type: string
          description: User who created the container type
        modifiedDate:
          type: string
          description: Date when the container type was last modified
        modifiedBy:
          type: string
          description: User who last modified the container type
        enabled:
          type: boolean
          description: Whether the container type is enabled

    SpecimenType:
      type: object
      properties:
        id:
          type: string
          description: Specimen Type ID
        name:
          type: string
          description: Specimen Type name
        createdDate:
          type: string
          description: Date when the specimen type was created
        createdBy:
          type: string
          description: User who created the specimen type
        modifiedDate:
          type: string
          description: Date when the specimen type was last modified
        modifiedBy:
          type: string
          description: User who last modified the specimen type
        enabled:
          type: boolean
          description: Whether the specimen type is enabled
        sequence:
          type: integer
          description: Ordering sequence for the specimen type

    ContainerRule:
      type: string
      enum:
        - AND
        - OR
      description: Container rule enum

    InstantResultsDisplayType:
      type: string
      enum:
        - DROPDOWN
        - RADIO
      description: Display type for instant result

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
