openapi: 3.0.1
info:
  title: SVCLocationNew
  description: API for managing locations (ccs)
  version: 1.0.0
tags:
  - name: locationQuery
    description: APIs for managing locations (CC)
paths:
  /v1/user/orderable/{orderableId}:
    get:
      tags:
        - orderableQuery
      operationId: getOrderable
      parameters:
        - name: orderableId
          in: path
          description: db primary id of orderable
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetOrderableResponse"

  /v1/user/orderables:
    get:
      tags:
        - orderableQuery
      operationId: getOrderables
      parameters:
        - name: filters
          in: query
          required: true
          schema:
            type: object
            additionalProperties:
              type: object
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetOrderablesResponse"

  /v1/user/orderables/{locationId}:
    get:
      tags:
        - orderableQuery
      operationId: orderablesForLocation
      parameters:
        - name: locationId
          in: path
          description: database id of location
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetOrderablesResponse"

  /v1/user/locations-offering-orderable/{orderableId}:
    get:
      tags:
        - locationQuery
      operationId: getLocationsOfferingOrderable
      description: get list of locations that offer a certain orderable
      parameters:
        - name: orderableId
          in: path
          description: database id of orderable
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetLocationsResponse"

  /v1/user/location:
    get:
      tags:
        - locationQuery
      operationId: getLocations
      description: get list of locations with filters
      parameters:
        - name: filters
          in: query
          required: true
          schema:
            type: object
            additionalProperties:
              type: object
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetLocationsResponse"

  /v1/user/location/{locationId}:
    get:
      tags:
        - locationQuery
      operationId: getLocation
      description: get a location by it's id
      parameters:
        - name: locationId
          in: path
          description: database id of location
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetLocationResponse"

  /v1/user/location/{locationId}/operating_hours:
    get:
      tags:
        - locationQuery
      operationId: listOperatingHours
      description: get operating hours of locations (with filters?)
      parameters:
        - name: locationId
          in: path
          description: database id of location
          required: true
          schema:
            type: string
            format: uuid
        - name: filters
          in: query
          required: true
          schema:
            type: object
            additionalProperties:
              type: object
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetLocationOperatingHoursResponse"

  /v1/user/location/{locationId}/orderables:
    get:
      tags:
        - locationQuery
      operationId: orderablesForLocationCC
      description: get available orderables of a location
      parameters:
        - name: locationId
          in: path
          description: database id of location
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/V1GetLocationOrderablesResponse"

  /v1/user/location/globalbookingsetting:
    get:
      tags:
        - globalQuery
      operationId: getGlobalBookingSetting
      description: get the global booking setting
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": '#/components/schemas/V1GetGlobalBookingSettingResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    RestOpenInfo:
      description: operating hours and related info
      type: object
      properties:
        allowSameDayBookings:
          type: boolean
        operatingHours:
          type: array
          items:
            "$ref": "#/components/schemas/RestOperatingHour"
        temporaryOperatingHours:
          type: array
          items:
            "$ref": "#/components/schemas/V1RestTemporaryOperatingHour"
        testSettings:
          type: array
          items:
            $ref: "#/components/schemas/RestTest"
        bookingCapacity:
          type: array
          items:
            $ref: "#/components/schemas/RestBookingCapacity"
      required:
        - operatingHours
        - testSettings
        - bookingCapacity
        - sameDayBookings

    # these are by date and day of whereas restOperatingHours are only by day of week
    V1RestTemporaryOperatingHour:
      description: return the weekly operating hours
      type: object
      properties:
        id:
          type: string
          format: uuid
        fullDayClosed:
          type: boolean
        date:
          type: string
          format: date
        operatingHour:
          "$ref": "#/components/schemas/RestOperatingHour"

    RestOperatingHour:
      description: return the weekly operating hours
      type: object
      properties:
        id:
          type: string
          format: uuid
        day:
          $ref: "#/components/schemas/RestDay"
        timeSlots:
          type: array
          items:
            "$ref": "#/components/schemas/RestTimeSlot"
      required:
        - day
        - timeSlots

    #    TODO: maybe call this testconfig
    RestTest:
      type: object
      properties:
        name:
          type: string
        duration:
          type: integer
        followUpAppointment:
          type: boolean
        numberOfAppointments:
          type: integer
        clientCareEnabled:
          type: boolean
        onlineBookingEnabled:
          type: boolean
        maxBookingsPerDay:
          type: integer
        minDelayBetweenBookings:
          type: string
          format: integer
          description: duration
        operatingHours:
          type: array
          items:
            "$ref": "#/components/schemas/RestOperatingHour"
        orderableId:
          type: string
          format: uuid
      required:
        - name
        - duration
        - followUpAppointment
        - numberOfAppointments
        - clientCareEnabled
        - onlineBookingEnabled
        - maxBookingsPerDay
        - minDelayBetweenBookings
        - operatingHours
        - orderableId

    RestBookingCapacity:
      type: object
      properties:
        day:
          $ref: "#/components/schemas/RestDay"
        capacity:
          type: integer
      required:
        - day
        - capacity

    RestDay:
      type: string
      enum:
        - MONDAY
        - TUESDAY
        - WEDNESDAY
        - THURSDAY
        - FRIDAY
        - SATURDAY
        - SUNDAY

    RestTimeSlot:
      type: object
      properties:
        open:
          type: string
          format: time
          description: open time
        close:
          type: string
          format: time
          description: close time
      required:
        - open
        - closed

    RestGlobalSetting:
      type: object
      properties:
        blockOver:
          type: integer
          description: minutes
        blockAfter:
          type: integer
          description: minutes

    V1LightLocation:
      type: object
      description: slim location object
      properties:
        id:
          type: string
          format: uuid
          description: primary id of cc in database
        ccId:
          type: string
        buId:
          type: integer
          format: int32
        buCode:
          type: string
          description: the standardized buCode e.g. lav, dor
        #        TODO: map the cc->company->code
        name:
          type: string
        address:
          type: string
        address2:
          type: string
        state:
          type: string
        city:
          type: string
        postcode:
          type: string
        #        allowSameDayBookings:
        #          type: boolean
        trmLink:
          type: string
      required:
        - id
        - name
        - state
        - buCode

    #TODO: add rest of fields
    V1RestLightOrderable:
      type: object
      description: a test bookable through online booking or client care
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        importantInformation:
          type: string
        duration:
          type: integer
        clientCareEnabled:
          type: boolean
        onlineBookingEnabled:
          type: boolean
        paymentRequired:
          type: boolean
        followUpRequired:
          type: boolean

    V1GetOrderableResponse:
      allOf:
        - $ref: './common.yml#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              $ref: '#/components/schemas/V1RestLightOrderable'

    V1GetOrderablesResponse:
      allOf:
        - $ref: './common.yml#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              items:
                $ref: '#/components/schemas/V1RestLightOrderable'

    V1GetLocationsResponse:
      allOf:
        - $ref: './common.yml#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              items:
                $ref: '#/components/schemas/V1LightLocation'

    V1GetLocationResponse:
      allOf:
        - $ref: './common.yml#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              $ref: '#/components/schemas/V1LightLocation'

    V1GetLocationOperatingHoursResponse:
      allOf:
        - $ref: './common.yml#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              $ref: '#/components/schemas/RestOpenInfo'

    V1GetLocationOrderablesResponse:
      allOf:
        - $ref: './common.yml#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              items:
                $ref: '#/components/schemas/V1RestLightOrderable'

    V1GetGlobalBookingSettingResponse:
      allOf:
        - $ref: './common.yml#/components/schemas/apiResponse'
        - type: object
          properties:
            payload:
              $ref: '#/components/schemas/RestGlobalSetting'
