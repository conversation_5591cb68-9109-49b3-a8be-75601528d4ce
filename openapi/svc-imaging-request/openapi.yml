openapi: 3.0.1
info:
  title: Request Imaging System Integration
  version: 1.0.0
  description: |
    This API provides endpoints for managing imaging requests and file uploads in the Healius system.
    It supports two main operations:
    1. Uploading image files for processing
    2. Submitting request forms with associated metadata
    
    The API requires authentication via Bearer token and supports different types of requests
    (doctor, commercial, ADF) with various priority levels.

servers:
  - url: https://api.dev.healius.com.au
    description: Development environment
  - url: https://api.test.healius.com.au
    description: Test environment
  - url: https://api.uat.healius.com.au
    description: User Acceptance Testing environment
  - url: https://api.training.healius.com.au
    description: Training environment
  - url: https://api.staging.healius.com.au
    description: Staging environment
  - url: https://api.prod.healius.com.au
    description: Production environment

tags:
  - name: imageUpload
    description: Operations for uploading image files to the Document Exchange system
  - name: requestForm
    description: Operations for submitting request forms to the Document Exchange system

paths:
  /v1/image/upload:
    post:
      operationId: imageUpload
      tags:
        - imageUpload
      summary: Upload an image file
      description: |
        Uploads an image file to the Document Exchange system. The file will be associated with
        the specified business unit, lab identifier, and request type.
        
        Supported file format:
        - TIFF only
        
        Maximum file size: 200KB
      parameters:
        - required: true
          schema:
            title: Business Unit Code
            type: string
            example: "lav"
          name: business_unit
          in: query
          description: The business unit code associated with the image (e.g., lav for Laverty)
        - required: true
          schema:
            title: Prefix
            type: string
            example: "25"
          name: prefix
          in: query
          description: The prefix part of the lab identifier (e.g., 25 for a specific location)
        - required: true
          schema:
            title: Lab Number
            type: integer
            example: 10000000
          name: num
          in: query
          description: The numeric part of the lab identifier
        - required: true
          schema:
            $ref: '#/components/schemas/RequestType'
          name: request_type
          in: query
          description: The type of request (doctor, commercial, or adf)
        - required: true
          schema:
            $ref: '#/components/schemas/InputType'
          name: input_type
          in: query
          description: The type of input (Attachment or Request)
        - required: false
          schema:
            title: Centre Code
            type: string
            example: "SYD01"
          name: centre_code
          in: query
          description: The specific centre code where the image was captured
      requestBody:
        description: Image file to upload
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  $ref: '#/components/schemas/FileUploadPayload'
              required:
                - file
      responses:
        '200':
          description: Successfully uploaded the image
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
              example:
                uploaded: true
                fileId: "img-123456789"
        '400':
          description: Bad Request - Invalid parameters or payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: "ERROR"
                responseMessage: "Invalid file format"
                errors:
                  - message: "File must be in TIFF format"
                    errorType: "INVALID_FORMAT"
                    source: "file_validation"
                    errorCategory: "validation"
        '401':
          description: Unauthorized - Invalid or missing authentication token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error - An unexpected error occurred
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v1/request/upload:
    post:
      operationId: requestForm
      tags:
        - requestForm
      summary: Post request form with JSON data
      description: |
        Submits a request form with associated metadata to the Document Exchange system.
        The request will be processed according to the specified priority level and
        associated with the provided business unit and lab identifier.
      parameters:
        - required: true
          schema:
            title: Business Unit Code
            type: string
            example: "lav"
          name: business_unit
          in: query
          description: The business unit code associated with the request
        - required: true
          schema:
            title: Prefix
            type: string
            example: "25"
          name: prefix
          in: query
          description: The prefix part of the lab identifier
        - required: true
          schema:
            title: Lab Number
            type: integer
            example: 10000000
          name: num
          in: query
          description: The numeric part of the lab identifier
        - required: true
          schema:
            $ref: '#/components/schemas/RequestType'
          name: request_type
          in: query
          description: The type of request (doctor, commercial, or adf)
        - required: true
          schema:
            $ref: '#/components/schemas/InputType'
          name: input_type
          in: query
          description: The type of input (Attachment or Request)
        - required: false
          schema:
            $ref: '#/components/schemas/Priority'
          name: priority
          in: query
          description: The priority level of the request (Routine or Urgent)
        - required: false
          schema:
            title: Centre Code
            type: string
            example: "SYD01"
          name: centre_code
          in: query
          description: The specific centre code where the request originated
      requestBody:
        description: Request form data in JSON format
        required: true
        content:
          application/json:
            schema:
              type: string
      responses:
        '200':
          description: Successfully submitted the request form
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestFormResponse'
              example:
                submitted: true
                requestId: "req-123456789"
        '400':
          description: Bad Request - Invalid parameters or payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - Invalid or missing authentication token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error - An unexpected error occurred
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      description: |
        JWT Bearer token authentication.
        Include the token in the Authorization header as: `Bearer <token>`
  schemas:
    Error:
      description: Details of a single error that occurred during request processing
      type: object
      properties:
        message:
          type: string
          description: A human-readable message describing the error
          example: "Invalid file format"
        errorType:
          type: string
          description: A code or type identifying the specific error
          example: "INVALID_FORMAT"
        source:
          type: string
          description: The source or component where the error originated
          example: "file_validation"
        errorCategory:
          type: string
          description: A category classifying the error (e.g., validation, system)
          example: "validation"
      required:
        - message
        - errorType
        - source
        - errorCategory

    ApiResponse:
      description: Standard API response structure containing status and error information
      type: object
      properties:
        code:
          type: string
          description: A status code for the operation (e.g., 'SUCCESS', 'ERROR')
          example: "SUCCESS"
        responseMessage:
          type: string
          description: A general message summarizing the response
          example: "Operation completed successfully"
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
          description: A list of errors, if any occurred
        page:
          type: integer
          description: Page number for paginated responses (if applicable)
          nullable: true
      required:
        - code
        - responseMessage
        - errors

    ErrorResponse:
      description: Standard structure for API error responses
      allOf:
        - $ref: '#/components/schemas/ApiResponse'

    RequestType:
      description: Defines the types of requests that can be made to the system
      type: string
      enum:
        - 'doctor'
        - 'commercial'
        - 'adf'
      example: 'doctor'

    InputType:
      description: Defines the type of input being submitted
      type: string
      enum:
        - 'Attachment'
        - 'Request'
      example: 'Attachment'

    FileUploadPayload:
      title: File Upload Payload
      description: Describes the file part in a multipart/form-data request
      required:
        - file
      type: object
      properties:
        file:
          title: File
          description: The actual file being uploaded
          type: string
          format: binary

    RequestFormData:
      title: Request Form Data
      description: JSON data structure for the request form
      type: object
      properties:
        formData:
          type: object
          description: The form data to be applied to the image renderer
          additionalProperties: true
          example:
            patientName: "John Smith"
            dateOfBirth: "1980-01-01"
            medicareNumber: "**********"
            referringDoctor: "Dr. Jane Doe"
            examinationType: "X-Ray"
            clinicalNotes: "Patient presenting with chest pain"
      required:
        - formData

    UploadResponse:
      description: Response confirming the upload status of a file
      type: object
      properties:
        uploaded:
          type: boolean
          description: Indicates whether the file was successfully uploaded
          example: true
        fileId:
          type: string
          description: The unique identifier of the uploaded file
          example: "img-123456789"
      required:
        - uploaded
        - fileId

    RequestFormResponse:
      description: Response confirming the submission status of a request form
      type: object
      properties:
        submitted:
          type: boolean
          description: Indicates whether the request form was successfully submitted
          example: true
        requestId:
          type: string
          description: The unique identifier of the submitted request
          example: "req-123456789"
      required:
        - submitted
        - requestId

    Priority:
      description: Defines the priority levels for a request
      type: string
      enum:
        - 'Routine'
        - 'Urgent'
      default: 'Routine'
      example: 'Routine'

security:
  - BearerAuth: []