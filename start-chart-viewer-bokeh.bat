@echo off
echo ========================================
echo  Adaptive Bar Chart Viewer (Bokeh)
echo  backtesting.py Style Charts
echo ========================================
echo.

echo [1/4] Checking Java Trading Bot...
echo Make sure your Java trading bot is running on port 8080
echo WebSocket endpoint: ws://localhost:8080/ws/adaptive-bars
echo.

echo [2/4] Stopping any existing Flask servers...
cd python-chart-viewer
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do taskkill /PID %%a /F >nul 2>&1
echo.

echo [3/4] Installing Python dependencies (Bokeh)...
pip install -r requirements.txt
echo.

echo [4/4] Launching Bokeh Chart Viewer...
echo Chart viewer will be available at: http://localhost:5000
echo Chart style: Bokeh (similar to backtesting.py)
echo.
echo Features:
echo   - Professional candlestick charts
echo   - Interactive zoom and pan
echo   - Volume overlay
echo   - Real-time updates
echo.

python run_bokeh.py

echo.
echo Chart viewer stopped.
echo.
pause
